import Joi from 'joi';

// AD<PERSON> (<PERSON><PERSON><PERSON>) routes for budget data
export default async function adkRoutes(fastify, options) {
  // Get ADK data with filtering and pagination
  fastify.post('/query', {
    schema: {
      body: {
        type: 'object',
        properties: {
          tahun: { type: 'integer', minimum: 2000, maximum: 2100 },
          kode_skpd: { type: 'string' },
          kode_program: { type: 'string' },
          kode_kegiatan: { type: 'string' },
          kode_sub_kegiatan: { type: 'string' },
          kode_rekening: { type: 'string' },
          jenis_anggaran: { type: 'string', enum: ['murni', 'perubahan', 'all'] },
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 1000, default: 100 },
          sortBy: { type: 'string', default: 'kode_rekening' },
          sortOrder: { type: 'string', enum: ['ASC', 'DESC'], default: 'ASC' },
          includeDetails: { type: 'boolean', default: false },
          aggregateBy: { type: 'string', enum: ['skpd', 'program', 'kegiatan', 'rekening'] }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const {
        tahun, kode_skpd, kode_program, kode_kegiatan, kode_sub_kegiatan,
        kode_rekening, jenis_anggaran, page, limit, sortBy, sortOrder,
        includeDetails, aggregateBy
      } = request.body;
      
      const offset = (page - 1) * limit;
      
      // Build WHERE conditions
      let whereConditions = [];
      let queryParams = [];
      
      if (tahun) {
        whereConditions.push('tahun = ?');
        queryParams.push(tahun);
      }
      
      if (kode_skpd) {
        whereConditions.push('kode_skpd LIKE ?');
        queryParams.push(`%${kode_skpd}%`);
      }
      
      if (kode_program) {
        whereConditions.push('kode_program LIKE ?');
        queryParams.push(`%${kode_program}%`);
      }
      
      if (kode_kegiatan) {
        whereConditions.push('kode_kegiatan LIKE ?');
        queryParams.push(`%${kode_kegiatan}%`);
      }
      
      if (kode_sub_kegiatan) {
        whereConditions.push('kode_sub_kegiatan LIKE ?');
        queryParams.push(`%${kode_sub_kegiatan}%`);
      }
      
      if (kode_rekening) {
        whereConditions.push('kode_rekening LIKE ?');
        queryParams.push(`%${kode_rekening}%`);
      }
      
      if (jenis_anggaran && jenis_anggaran !== 'all') {
        whereConditions.push('jenis_anggaran = ?');
        queryParams.push(jenis_anggaran);
      }
      
      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
      
      let results = [];
      let totalCount = 0;
      
      if (aggregateBy) {
        // Aggregated query
        const aggregateFields = {
          skpd: 'kode_skpd, nama_skpd',
          program: 'kode_program, nama_program',
          kegiatan: 'kode_kegiatan, nama_kegiatan',
          rekening: 'kode_rekening, nama_rekening'
        };
        
        const groupByField = aggregateFields[aggregateBy];
        
        if (!groupByField) {
          return reply.code(400).send({
            success: false,
            message: 'Invalid aggregateBy parameter'
          });
        }
        
        const aggregateQuery = `
          SELECT 
            ${groupByField},
            COUNT(*) as jumlah_item,
            SUM(pagu_anggaran) as total_pagu,
            SUM(realisasi_anggaran) as total_realisasi,
            SUM(sisa_anggaran) as total_sisa,
            AVG(persentase_realisasi) as rata_rata_realisasi
          FROM adk_data 
          ${whereClause}
          GROUP BY ${groupByField}
          ORDER BY ${sortBy} ${sortOrder}
          LIMIT ? OFFSET ?
        `;
        
        const [aggregateResults] = await fastify.db.query(aggregateQuery, [...queryParams, limit, offset]);
        results = aggregateResults;
        
        // Get total count for aggregated results
        const countQuery = `
          SELECT COUNT(DISTINCT ${groupByField.split(',')[0]}) as total 
          FROM adk_data 
          ${whereClause}
        `;
        const [countResults] = await fastify.db.query(countQuery, queryParams);
        totalCount = countResults[0].total;
        
      } else {
        // Regular detailed query
        const selectFields = includeDetails ? '*' : `
          kode_skpd, nama_skpd, kode_program, nama_program,
          kode_kegiatan, nama_kegiatan, kode_rekening, nama_rekening,
          pagu_anggaran, realisasi_anggaran, sisa_anggaran, persentase_realisasi
        `;
        
        const dataQuery = `
          SELECT ${selectFields}
          FROM adk_data 
          ${whereClause}
          ORDER BY ${sortBy} ${sortOrder}
          LIMIT ? OFFSET ?
        `;
        
        const [dataResults] = await fastify.db.query(dataQuery, [...queryParams, limit, offset]);
        results = dataResults;
        
        // Get total count
        const countQuery = `SELECT COUNT(*) as total FROM adk_data ${whereClause}`;
        const [countResults] = await fastify.db.query(countQuery, queryParams);
        totalCount = countResults[0].total;
      }
      
      const totalPages = Math.ceil(totalCount / limit);
      
      // Calculate summary statistics
      const summaryQuery = `
        SELECT 
          COUNT(*) as total_records,
          SUM(pagu_anggaran) as total_pagu,
          SUM(realisasi_anggaran) as total_realisasi,
          SUM(sisa_anggaran) as total_sisa,
          AVG(persentase_realisasi) as rata_rata_realisasi
        FROM adk_data 
        ${whereClause}
      `;
      
      const [summaryResults] = await fastify.db.query(summaryQuery, queryParams);
      const summary = summaryResults[0];
      
      return reply.send({
        success: true,
        message: 'ADK data retrieved successfully',
        data: {
          results,
          pagination: {
            page,
            limit,
            total: totalCount,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
          },
          summary: {
            totalRecords: summary.total_records,
            totalPagu: summary.total_pagu || 0,
            totalRealisasi: summary.total_realisasi || 0,
            totalSisa: summary.total_sisa || 0,
            rataRataRealisasi: summary.rata_rata_realisasi || 0
          },
          filters: {
            tahun, kode_skpd, kode_program, kode_kegiatan,
            kode_sub_kegiatan, kode_rekening, jenis_anggaran
          }
        }
      });
      
    } catch (error) {
      fastify.log.error('ADK query error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve ADK data'
      });
    }
  });
  
  // Get ADK summary by year
  fastify.get('/summary/:tahun', {
    schema: {
      params: {
        type: 'object',
        required: ['tahun'],
        properties: {
          tahun: { type: 'integer', minimum: 2000, maximum: 2100 }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const { tahun } = request.params;
      
      // Get overall summary
      const overallQuery = `
        SELECT 
          COUNT(*) as total_records,
          COUNT(DISTINCT kode_skpd) as total_skpd,
          COUNT(DISTINCT kode_program) as total_program,
          COUNT(DISTINCT kode_kegiatan) as total_kegiatan,
          SUM(pagu_anggaran) as total_pagu,
          SUM(realisasi_anggaran) as total_realisasi,
          SUM(sisa_anggaran) as total_sisa,
          AVG(persentase_realisasi) as rata_rata_realisasi
        FROM adk_data 
        WHERE tahun = ?
      `;
      
      const [overallResults] = await fastify.db.query(overallQuery, [tahun]);
      const overall = overallResults[0];
      
      // Get summary by SKPD
      const skpdQuery = `
        SELECT 
          kode_skpd,
          nama_skpd,
          COUNT(*) as jumlah_kegiatan,
          SUM(pagu_anggaran) as total_pagu,
          SUM(realisasi_anggaran) as total_realisasi,
          SUM(sisa_anggaran) as total_sisa,
          AVG(persentase_realisasi) as rata_rata_realisasi
        FROM adk_data 
        WHERE tahun = ?
        GROUP BY kode_skpd, nama_skpd
        ORDER BY total_pagu DESC
        LIMIT 20
      `;
      
      const [skpdResults] = await fastify.db.query(skpdQuery, [tahun]);
      
      // Get summary by program
      const programQuery = `
        SELECT 
          kode_program,
          nama_program,
          COUNT(*) as jumlah_kegiatan,
          SUM(pagu_anggaran) as total_pagu,
          SUM(realisasi_anggaran) as total_realisasi,
          SUM(sisa_anggaran) as total_sisa,
          AVG(persentase_realisasi) as rata_rata_realisasi
        FROM adk_data 
        WHERE tahun = ?
        GROUP BY kode_program, nama_program
        ORDER BY total_pagu DESC
        LIMIT 20
      `;
      
      const [programResults] = await fastify.db.query(programQuery, [tahun]);
      
      // Get realization trend by month (if monthly data exists)
      const trendQuery = `
        SELECT 
          MONTH(tanggal_realisasi) as bulan,
          SUM(realisasi_anggaran) as realisasi_bulanan,
          COUNT(*) as jumlah_transaksi
        FROM adk_data 
        WHERE tahun = ? AND tanggal_realisasi IS NOT NULL
        GROUP BY MONTH(tanggal_realisasi)
        ORDER BY bulan
      `;
      
      const [trendResults] = await fastify.db.query(trendQuery, [tahun]);
      
      return reply.send({
        success: true,
        message: 'ADK summary retrieved successfully',
        data: {
          tahun,
          overall: {
            totalRecords: overall.total_records,
            totalSkpd: overall.total_skpd,
            totalProgram: overall.total_program,
            totalKegiatan: overall.total_kegiatan,
            totalPagu: overall.total_pagu || 0,
            totalRealisasi: overall.total_realisasi || 0,
            totalSisa: overall.total_sisa || 0,
            rataRataRealisasi: overall.rata_rata_realisasi || 0,
            persentaseRealisasi: overall.total_pagu > 0 ? 
              (overall.total_realisasi / overall.total_pagu * 100) : 0
          },
          bySkpd: skpdResults,
          byProgram: programResults,
          trendBulanan: trendResults
        }
      });
      
    } catch (error) {
      fastify.log.error('ADK summary error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve ADK summary'
      });
    }
  });
  
  // Get available years
  fastify.get('/years', {
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const yearsQuery = `
        SELECT DISTINCT tahun
        FROM adk_data 
        ORDER BY tahun DESC
      `;
      
      const [years] = await fastify.db.query(yearsQuery);
      
      return reply.send({
        success: true,
        message: 'Available years retrieved successfully',
        data: years.map(row => row.tahun)
      });
      
    } catch (error) {
      fastify.log.error('Get ADK years error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve available years'
      });
    }
  });
  
  // Get SKPD list
  fastify.get('/skpd', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          tahun: { type: 'integer' },
          search: { type: 'string' }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const { tahun, search } = request.query;
      
      let whereConditions = [];
      let queryParams = [];
      
      if (tahun) {
        whereConditions.push('tahun = ?');
        queryParams.push(tahun);
      }
      
      if (search) {
        whereConditions.push('(kode_skpd LIKE ? OR nama_skpd LIKE ?)');
        queryParams.push(`%${search}%`, `%${search}%`);
      }
      
      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
      
      const skpdQuery = `
        SELECT DISTINCT 
          kode_skpd,
          nama_skpd,
          COUNT(*) as jumlah_kegiatan,
          SUM(pagu_anggaran) as total_pagu
        FROM adk_data 
        ${whereClause}
        GROUP BY kode_skpd, nama_skpd
        ORDER BY nama_skpd
      `;
      
      const [skpdResults] = await fastify.db.query(skpdQuery, queryParams);
      
      return reply.send({
        success: true,
        message: 'SKPD list retrieved successfully',
        data: skpdResults
      });
      
    } catch (error) {
      fastify.log.error('Get SKPD list error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve SKPD list'
      });
    }
  });
  
  // Get program list
  fastify.get('/program', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          tahun: { type: 'integer' },
          kode_skpd: { type: 'string' },
          search: { type: 'string' }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const { tahun, kode_skpd, search } = request.query;
      
      let whereConditions = [];
      let queryParams = [];
      
      if (tahun) {
        whereConditions.push('tahun = ?');
        queryParams.push(tahun);
      }
      
      if (kode_skpd) {
        whereConditions.push('kode_skpd = ?');
        queryParams.push(kode_skpd);
      }
      
      if (search) {
        whereConditions.push('(kode_program LIKE ? OR nama_program LIKE ?)');
        queryParams.push(`%${search}%`, `%${search}%`);
      }
      
      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
      
      const programQuery = `
        SELECT DISTINCT 
          kode_program,
          nama_program,
          COUNT(*) as jumlah_kegiatan,
          SUM(pagu_anggaran) as total_pagu
        FROM adk_data 
        ${whereClause}
        GROUP BY kode_program, nama_program
        ORDER BY nama_program
      `;
      
      const [programResults] = await fastify.db.query(programQuery, queryParams);
      
      return reply.send({
        success: true,
        message: 'Program list retrieved successfully',
        data: programResults
      });
      
    } catch (error) {
      fastify.log.error('Get program list error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve program list'
      });
    }
  });
  
  // Get detailed ADK item
  fastify.get('/detail/:id', {
    schema: {
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string' }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const { id } = request.params;
      
      const detailQuery = `
        SELECT *
        FROM adk_data 
        WHERE id = ?
      `;
      
      const [detailResults] = await fastify.db.query(detailQuery, [id]);
      
      if (detailResults.length === 0) {
        return reply.code(404).send({
          success: false,
          message: 'ADK data not found'
        });
      }
      
      const detail = detailResults[0];
      
      // Get related transactions if available
      const transactionQuery = `
        SELECT *
        FROM adk_transactions 
        WHERE adk_id = ?
        ORDER BY tanggal_transaksi DESC
      `;
      
      const [transactions] = await fastify.db.query(transactionQuery, [id]);
      
      return reply.send({
        success: true,
        message: 'ADK detail retrieved successfully',
        data: {
          detail,
          transactions
        }
      });
      
    } catch (error) {
      fastify.log.error('Get ADK detail error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve ADK detail'
      });
    }
  });
  
  // Export ADK data
  fastify.post('/export', {
    schema: {
      body: {
        type: 'object',
        properties: {
          format: { type: 'string', enum: ['csv', 'excel'], default: 'csv' },
          tahun: { type: 'integer' },
          kode_skpd: { type: 'string' },
          kode_program: { type: 'string' },
          includeTransactions: { type: 'boolean', default: false }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const { format, tahun, kode_skpd, kode_program, includeTransactions } = request.body;
      
      let whereConditions = [];
      let queryParams = [];
      
      if (tahun) {
        whereConditions.push('tahun = ?');
        queryParams.push(tahun);
      }
      
      if (kode_skpd) {
        whereConditions.push('kode_skpd = ?');
        queryParams.push(kode_skpd);
      }
      
      if (kode_program) {
        whereConditions.push('kode_program = ?');
        queryParams.push(kode_program);
      }
      
      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
      
      const exportQuery = `
        SELECT *
        FROM adk_data 
        ${whereClause}
        ORDER BY kode_skpd, kode_program, kode_kegiatan
      `;
      
      const [exportResults] = await fastify.db.query(exportQuery, queryParams);
      
      if (format === 'csv') {
        const csv = convertToCSV(exportResults);
        reply.type('text/csv');
        reply.header('Content-Disposition', `attachment; filename="adk_export_${Date.now()}.csv"`);
        return reply.send(csv);
      }
      
      // For Excel or other formats, return JSON for now
      return reply.send({
        success: true,
        message: 'ADK data exported successfully',
        data: exportResults
      });
      
    } catch (error) {
      fastify.log.error('ADK export error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to export ADK data'
      });
    }
  });
}

// Helper function to convert results to CSV
function convertToCSV(results) {
  if (!results || results.length === 0) {
    return '';
  }

  const headers = Object.keys(results[0]).join(',');
  const rows = results.map(row => 
    Object.values(row).map(value => {
      if (value === null || value === undefined) {
        return '';
      }
      const stringValue = String(value);
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    }).join(',')
  );

  return [headers, ...rows].join('\n');
}