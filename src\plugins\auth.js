import fp from "fastify-plugin";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";

async function authPlugin(fastify, options) {
  // JWT configuration
  const jwtConfig = {
    secret: process.env.JWT_SECRET || "sintesa-jwt-secret-key-2024",
    refreshSecret:
      process.env.JWT_REFRESH_SECRET || "sintesa-refresh-secret-2024",
    accessTokenExpiry: process.env.JWT_ACCESS_EXPIRY || "8h",
    refreshTokenExpiry: process.env.JWT_REFRESH_EXPIRY || "7d",
  };

  // Log JWT configuration for debugging
  fastify.log.info("Auth plugin JWT secret: " + jwtConfig.secret);
  fastify.log.info(
    "Environment JWT_SECRET: " + (process.env.JWT_SECRET || "not set")
  );

  // Authentication utilities
  const auth = {
    // Generate access token
    generateAccessToken(payload) {
      return jwt.sign(payload, jwtConfig.secret, {
        expiresIn: jwtConfig.accessTokenExpiry,
        issuer: "sintesa-backend",
        audience: "sintesa-frontend",
      });
    },

    // Generate refresh token
    generateRefreshToken(payload) {
      return jwt.sign(payload, jwtConfig.refreshSecret, {
        expiresIn: jwtConfig.refreshTokenExpiry,
        issuer: "sintesa-backend",
        audience: "sintesa-frontend",
      });
    },

    // Verify access token
    verifyAccessToken(token) {
      try {
        return jwt.verify(token, jwtConfig.secret);
      } catch (error) {
        fastify.log.error("JWT verification error: " + error.message);
        fastify.log.error("JWT error name: " + error.name);
        fastify.log.error(
          "Token being verified: " + token.substring(0, 100) + "..."
        );
        throw new Error("Invalid or expired access token: " + error.message);
      }
    },

    // Verify refresh token
    verifyRefreshToken(token) {
      try {
        return jwt.verify(token, jwtConfig.refreshSecret);
      } catch (error) {
        throw new Error("Invalid or expired refresh token");
      }
    },

    // Hash password
    async hashPassword(password) {
      const saltRounds = 12;
      return await bcrypt.hash(password, saltRounds);
    },

    // Compare password
    async comparePassword(password, hashedPassword) {
      return await bcrypt.compare(password, hashedPassword);
    },

    // Generate token pair
    generateTokenPair(user) {
      const payload = {
        id: user.id,
        username: user.username,
        role: user.role,
        kdkanwil: user.kdkanwil,
        kdkppn: user.kdkppn,
        kdlokasi: user.kdlokasi,
        active: user.active,
      };

      const accessToken = this.generateAccessToken(payload);
      const refreshToken = this.generateRefreshToken({
        id: user.id,
        username: user.username,
      });

      return { accessToken, refreshToken };
    },

    // Store refresh token
    async storeRefreshToken(userId, refreshToken) {
      try {
        if (fastify.cache) {
          const key = `refresh_token:${userId}`;
          const ttl = 7 * 24 * 60 * 60; // 7 days in seconds
          await fastify.cache.set(key, refreshToken, ttl);
        }
        return true;
      } catch (error) {
        fastify.log.error("Error storing refresh token:", error);
        return false;
      }
    },

    // Get stored refresh token
    async getStoredRefreshToken(userId) {
      try {
        if (fastify.cache) {
          const key = `refresh_token:${userId}`;
          return await fastify.cache.get(key);
        }
        return null;
      } catch (error) {
        fastify.log.error("Error getting refresh token:", error);
        return null;
      }
    },

    // Remove refresh token
    async removeRefreshToken(userId) {
      try {
        if (fastify.cache) {
          const key = `refresh_token:${userId}`;
          await fastify.cache.del(key);
        }
        return true;
      } catch (error) {
        fastify.log.error("Error removing refresh token:", error);
        return false;
      }
    },

    // Create user session
    async createSession(sessionId, sessionData, ttl = 28800) {
      try {
        // Store session in Redis cache if available
        if (fastify.session) {
          await fastify.session.create(sessionId, sessionData, ttl);
        }

        // Also store session in database for persistence
        try {
          const expiresAt = new Date(Date.now() + ttl * 1000);
          await fastify.db.query(
            `INSERT INTO user_sessions (session_id, user_id, refresh_token, ip_address, user_agent, expires_at) 
             VALUES (?, ?, ?, ?, ?, ?) 
             ON DUPLICATE KEY UPDATE 
             refresh_token = VALUES(refresh_token), 
             ip_address = VALUES(ip_address), 
             user_agent = VALUES(user_agent), 
             expires_at = VALUES(expires_at), 
             updated_at = CURRENT_TIMESTAMP`,
            [
              sessionId,
              sessionData.userId,
              sessionData.refreshToken || "",
              sessionData.ip || sessionData.ipAddress,
              sessionData.userAgent,
              expiresAt,
            ]
          );
        } catch (dbError) {
          fastify.log.warn(
            "Failed to store session in database:",
            dbError.message
          );
          // Continue even if database storage fails, Redis is primary
        }

        return sessionId;
      } catch (error) {
        fastify.log.error("Error creating session:", error);
        return null;
      }
    },

    // Update session activity
    async updateSessionActivity(sessionId) {
      try {
        // Update Redis cache if available
        if (fastify.session) {
          const session = await fastify.session.get(sessionId);
          if (session) {
            session.lastActivity = new Date().toISOString();
            await fastify.session.update(sessionId, session, 28800);
          }
        }

        // Also update database
        try {
          await fastify.db.query(
            "UPDATE user_sessions SET updated_at = CURRENT_TIMESTAMP WHERE session_id = ? AND is_active = 1",
            [sessionId]
          );
        } catch (dbError) {
          fastify.log.warn(
            "Failed to update session activity in database:",
            dbError.message
          );
        }

        return true;
      } catch (error) {
        fastify.log.error("Error updating session activity:", error);
        return false;
      }
    },

    // Get active sessions for user
    async getUserSessions(userId) {
      try {
        const sessions = [];

        // Check Redis cache if available
        if (fastify.cache) {
          try {
            const pattern = `session:${userId}_*`;
            const keys = await fastify.cache.keys(pattern);

            for (const key of keys) {
              const session = await fastify.cache.get(key);
              if (session && session.userId === userId) {
                sessions.push({
                  sessionId: key.replace("session:", ""),
                  ...session,
                });
              }
            }
          } catch (cacheError) {
            fastify.log.warn("Error getting sessions from cache:", cacheError);
          }
        }

        // If no sessions found in cache, check database as fallback
        if (sessions.length === 0) {
          try {
            const dbSessions = await fastify.db.query(
              "SELECT * FROM user_sessions WHERE user_id = ? AND is_active = 1 AND expires_at > NOW()",
              [userId]
            );

            for (const dbSession of dbSessions.rows) {
              sessions.push({
                sessionId: dbSession.session_id,
                userId: dbSession.user_id,
                ipAddress: dbSession.ip_address,
                userAgent: dbSession.user_agent,
                lastActivity: dbSession.updated_at,
                loginTime: dbSession.created_at,
              });
            }
          } catch (dbError) {
            fastify.log.warn("Database session check failed:", dbError.message);
          }
        }

        return sessions;
      } catch (error) {
        fastify.log.error("Error getting user sessions:", error);
        return [];
      }
    },

    // Destroy user session
    async destroySession(sessionId) {
      try {
        // Remove from Redis cache if available
        if (fastify.session) {
          await fastify.session.destroy(sessionId);
        }

        // Also remove from database
        try {
          await fastify.db.query(
            "UPDATE user_sessions SET is_active = 0 WHERE session_id = ?",
            [sessionId]
          );
        } catch (dbError) {
          fastify.log.warn(
            "Failed to deactivate session in database:",
            dbError.message
          );
        }

        return true;
      } catch (error) {
        fastify.log.error("Error destroying session:", error);
        return false;
      }
    },

    // Destroy all user sessions
    async destroyAllUserSessions(userId) {
      try {
        // Remove from Redis cache if available
        if (fastify.cache) {
          try {
            const pattern = `session:${userId}_*`;
            const keys = await fastify.cache.keys(pattern);

            for (const key of keys) {
              await fastify.cache.del(key);
            }
          } catch (cacheError) {
            fastify.log.warn(
              "Error destroying sessions from cache:",
              cacheError
            );
          }
        }

        // Also deactivate all user sessions in database
        try {
          await fastify.db.query(
            "UPDATE user_sessions SET is_active = 0 WHERE user_id = ?",
            [userId]
          );
        } catch (dbError) {
          fastify.log.warn(
            "Failed to deactivate user sessions in database:",
            dbError.message
          );
        }

        return true;
      } catch (error) {
        fastify.log.error("Error destroying all user sessions:", error);
        return false;
      }
    },

    // Check if user is logged in
    async isUserLoggedIn(userId) {
      try {
        const sessions = await this.getUserSessions(userId);
        return sessions.length > 0;
      } catch (error) {
        fastify.log.error("Error checking user login status:", error);
        return false;
      }
    },

    // Rate limiting for login attempts
    async checkLoginAttempts(identifier) {
      try {
        const key = `login_attempts:${identifier}`;
        const attempts = (await fastify.cache.get(key)) || 0;
        const maxAttempts = 5;
        const lockoutTime = 15 * 60; // 15 minutes

        if (attempts >= maxAttempts) {
          const ttl = await fastify.cache.ttl(key);
          throw new Error(
            `Too many login attempts. Try again in ${Math.ceil(ttl / 60)} minutes.`
          );
        }

        return true;
      } catch (error) {
        if (error.message.includes("Too many login attempts")) {
          throw error;
        }
        fastify.log.error("Error checking login attempts:", error);
        return true; // Allow login if cache fails
      }
    },

    // Record failed login attempt
    async recordFailedLogin(identifier) {
      try {
        const key = `login_attempts:${identifier}`;
        const lockoutTime = 15 * 60; // 15 minutes
        await fastify.cache.incr(key, lockoutTime);
      } catch (error) {
        fastify.log.error("Error recording failed login:", error);
      }
    },

    // Clear login attempts
    async clearLoginAttempts(identifier) {
      try {
        const key = `login_attempts:${identifier}`;
        await fastify.cache.del(key);
      } catch (error) {
        fastify.log.error("Error clearing login attempts:", error);
      }
    },
  };

  // Authentication middleware
  fastify.decorate("authenticate", async function (request, reply) {
    try {
      // Get token from header or cookie
      let token = null;

      if (request.headers.authorization) {
        token = request.headers.authorization.replace("Bearer ", "");
      } else if (request.cookies.token) {
        token = request.cookies.token;
      }

      if (!token) {
        throw new Error("No token provided");
      }

      // Try to decrypt token if it's encrypted
      let decryptedToken = token;
      try {
        // Check if token is encrypted (frontend sends encrypted tokens)
        decryptedToken = fastify.encryption.decryptString(token);
        fastify.log.info("Token decrypted successfully");
      } catch (decryptError) {
        // If decryption fails, assume token is already plain text
        fastify.log.info("Token appears to be plain text, using as-is");
        decryptedToken = token;
      }

      // Verify token
      const decoded = auth.verifyAccessToken(decryptedToken);

      // Get user session
      const sessions = await auth.getUserSessions(decoded.id);
      if (sessions.length === 0) {
        throw new Error("No active session found");
      }

      // Update session activity
      const activeSession = sessions[0];
      await auth.updateSessionActivity(activeSession.sessionId);

      // Attach user to request
      request.user = decoded;
      request.sessionId = activeSession.sessionId;
    } catch (error) {
      reply.status(401).send({
        success: false,
        message: "Authentication failed",
        code: "UNAUTHORIZED",
      });
    }
  });

  // Optional authentication middleware
  fastify.decorate("optionalAuth", async function (request, reply) {
    try {
      let token = null;

      if (request.headers.authorization) {
        token = request.headers.authorization.replace("Bearer ", "");
      } else if (request.cookies.token) {
        token = request.cookies.token;
      }

      if (token) {
        // Try to decrypt token if it's encrypted
        let decryptedToken = token;
        try {
          decryptedToken = fastify.encryption.decryptString(token);
        } catch (decryptError) {
          // If decryption fails, assume token is already plain text
          decryptedToken = token;
        }

        const decoded = auth.verifyAccessToken(decryptedToken);
        request.user = decoded;
      }
    } catch (error) {
      // Ignore authentication errors for optional auth
      request.user = null;
    }
  });

  // Role-based authorization middleware
  fastify.decorate("authorize", (roles = []) => {
    return async function (request, reply) {
      if (!request.user) {
        return reply.status(401).send({
          success: false,
          message: "Authentication required",
          code: "UNAUTHORIZED",
        });
      }

      if (roles.length > 0 && !roles.includes(request.user.role)) {
        return reply.status(403).send({
          success: false,
          message: "Insufficient permissions",
          code: "FORBIDDEN",
        });
      }
    };
  });

  // Simple JWT verification middleware (for backward compatibility with old Express routes)
  fastify.decorate("verifyToken", async function (request, reply) {
    try {
      // Get token from header or cookie
      let token = null;

      if (request.headers.authorization) {
        token = request.headers.authorization.replace("Bearer ", "");
      } else if (request.cookies.token) {
        token = request.cookies.token;
      }

      if (!token) {
        return reply.status(401).send({
          success: false,
          message: "No token provided",
          code: "UNAUTHORIZED",
        });
      }

      // Log the received token for debugging
      fastify.log.info(
        "Received token (first 50 chars): " + token.substring(0, 50) + "..."
      );
      fastify.log.info("Token length: " + token.length);

      // Try to decrypt token if it's encrypted
      let decryptedToken = token;
      try {
        // Check if token is encrypted (frontend sends encrypted tokens)
        decryptedToken = fastify.encryption.decryptString(token);
        fastify.log.info("Token decrypted successfully for verifyToken");

        // Remove quotes if the decrypted token is wrapped in quotes
        if (decryptedToken.startsWith('"') && decryptedToken.endsWith('"')) {
          decryptedToken = decryptedToken.slice(1, -1);
          fastify.log.info("Removed quotes from decrypted token");
        }

        fastify.log.info(
          "Decrypted token (first 50 chars): " +
            decryptedToken.substring(0, 50) +
            "..."
        );
      } catch (decryptError) {
        // If decryption fails, assume token is already plain text
        fastify.log.info("Token decryption failed: " + decryptError.message);
        fastify.log.info(
          "Token appears to be plain text for verifyToken, using as-is"
        );
        decryptedToken = token;
      }

      // Verify token (without session management for backward compatibility)
      fastify.log.info(
        "Attempting to verify decrypted token:",
        decryptedToken.substring(0, 50) + "..."
      );
      const decoded = auth.verifyAccessToken(decryptedToken);
      fastify.log.info(
        "Token verified successfully, decoded payload:",
        JSON.stringify(decoded)
      );

      // Attach user to request (Express-style)
      request.user = decoded;
      request.userId = decoded.id;
    } catch (error) {
      fastify.log.error("Simple token verification failed: " + error.message);
      fastify.log.error("Error stack: " + error.stack);
      return reply.status(401).send({
        success: false,
        message: "Authentication failed",
        code: "UNAUTHORIZED",
      });
    }
  });

  // Register auth utilities
  fastify.decorate("auth", auth);
}

export default fp(authPlugin, {
  name: "auth",
  dependencies: [],
});
