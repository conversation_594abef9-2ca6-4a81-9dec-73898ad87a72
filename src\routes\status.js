// Status and health check routes
export default async function statusRoutes(fastify, options) {
  // Basic health check
  fastify.get('/health', {
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            timestamp: { type: 'string' },
            uptime: { type: 'number' },
            version: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    return reply.send({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0'
    });
  });
  
  // Detailed system status
  fastify.get('/system', {
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const systemInfo = {
        status: 'operational',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        memory: {
          used: process.memoryUsage().heapUsed,
          total: process.memoryUsage().heapTotal,
          external: process.memoryUsage().external,
          rss: process.memoryUsage().rss
        },
        cpu: {
          loadAverage: process.loadavg ? process.loadavg() : null
        }
      };
      
      // Check database connection
      try {
        await fastify.db.query('SELECT 1 as test');
        systemInfo.database = {
          status: 'connected',
          type: 'mysql'
        };
      } catch (dbError) {
        systemInfo.database = {
          status: 'disconnected',
          type: 'mysql',
          error: dbError.message
        };
        systemInfo.status = 'degraded';
      }
      
      // Check Redis connection
      if (fastify.redis) {
        try {
          await fastify.redis.ping();
          systemInfo.redis = {
            status: 'connected'
          };
        } catch (redisError) {
          systemInfo.redis = {
            status: 'disconnected',
            error: redisError.message
          };
          systemInfo.status = 'degraded';
        }
      } else {
        systemInfo.redis = {
          status: 'not_configured'
        };
      }
      
      // Check Socket.IO status
      if (fastify.io) {
        systemInfo.socketio = {
          status: 'active',
          connectedClients: fastify.io.engine.clientsCount || 0
        };
      } else {
        systemInfo.socketio = {
          status: 'not_configured'
        };
      }
      
      return reply.send({
        success: true,
        message: 'System status retrieved successfully',
        data: systemInfo
      });
      
    } catch (error) {
      fastify.log.error('System status error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve system status',
        data: {
          status: 'error',
          timestamp: new Date().toISOString(),
          error: error.message
        }
      });
    }
  });
  
  // Database status and statistics
  fastify.get('/database', {
    preHandler: [fastify.authenticate, fastify.authorize(['admin'])]
  }, async (request, reply) => {
    try {
      const dbStats = {
        status: 'unknown',
        timestamp: new Date().toISOString()
      };
      
      // Test connection
      try {
        await fastify.db.query('SELECT 1 as test');
        dbStats.status = 'connected';
      } catch (error) {
        dbStats.status = 'disconnected';
        dbStats.error = error.message;
        return reply.send({
          success: false,
          message: 'Database connection failed',
          data: dbStats
        });
      }
      
      // Get database version and info
      try {
        const [versionResult] = await fastify.db.query('SELECT VERSION() as version');
        dbStats.version = versionResult[0].version;
      } catch (error) {
        fastify.log.warn('Could not get database version:', error.message);
      }
      
      // Get table statistics
      try {
        const [tableStats] = await fastify.db.query(`
          SELECT 
            table_name,
            table_rows,
            data_length,
            index_length,
            (data_length + index_length) as total_size
          FROM information_schema.tables 
          WHERE table_schema = DATABASE()
          ORDER BY total_size DESC
        `);
        dbStats.tables = tableStats;
      } catch (error) {
        fastify.log.warn('Could not get table statistics:', error.message);
      }
      
      // Get connection statistics
      try {
        const [connectionStats] = await fastify.db.query(`
          SHOW STATUS WHERE Variable_name IN (
            'Connections', 'Max_used_connections', 'Threads_connected', 
            'Threads_running', 'Uptime', 'Questions'
          )
        `);
        
        dbStats.connections = {};
        connectionStats.forEach(stat => {
          dbStats.connections[stat.Variable_name.toLowerCase()] = stat.Value;
        });
      } catch (error) {
        fastify.log.warn('Could not get connection statistics:', error.message);
      }
      
      return reply.send({
        success: true,
        message: 'Database status retrieved successfully',
        data: dbStats
      });
      
    } catch (error) {
      fastify.log.error('Database status error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve database status'
      });
    }
  });
  
  // Application metrics
  fastify.get('/metrics', {
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const metrics = {
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        requests: {
          total: 0,
          success: 0,
          errors: 0
        },
        performance: {
          avgResponseTime: 0,
          maxResponseTime: 0
        },
        users: {
          active: 0,
          total: 0
        },
        queries: {
          total: 0,
          successful: 0,
          failed: 0,
          avgExecutionTime: 0
        }
      };
      
      // Get request statistics from logs (if available)
      try {
        const [requestStats] = await fastify.db.query(`
          SELECT 
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_requests,
            COUNT(CASE WHEN status = 'error' THEN 1 END) as error_requests,
            AVG(execution_time) as avg_response_time,
            MAX(execution_time) as max_response_time
          FROM query_logs 
          WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        `);
        
        if (requestStats[0]) {
          metrics.requests.total = requestStats[0].total_requests || 0;
          metrics.requests.success = requestStats[0].successful_requests || 0;
          metrics.requests.errors = requestStats[0].error_requests || 0;
          metrics.performance.avgResponseTime = requestStats[0].avg_response_time || 0;
          metrics.performance.maxResponseTime = requestStats[0].max_response_time || 0;
        }
      } catch (error) {
        fastify.log.warn('Could not get request statistics:', error.message);
      }
      
      // Get user statistics
      try {
        const [userStats] = await fastify.db.query(`
          SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as active_users
          FROM users
        `);
        
        if (userStats[0]) {
          metrics.users.total = userStats[0].total_users || 0;
          metrics.users.active = userStats[0].active_users || 0;
        }
      } catch (error) {
        fastify.log.warn('Could not get user statistics:', error.message);
      }
      
      // Get query statistics
      try {
        const [queryStats] = await fastify.db.query(`
          SELECT 
            COUNT(*) as total_queries,
            COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_queries,
            COUNT(CASE WHEN status = 'error' THEN 1 END) as failed_queries,
            AVG(execution_time) as avg_execution_time
          FROM query_logs 
          WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        `);
        
        if (queryStats[0]) {
          metrics.queries.total = queryStats[0].total_queries || 0;
          metrics.queries.successful = queryStats[0].successful_queries || 0;
          metrics.queries.failed = queryStats[0].failed_queries || 0;
          metrics.queries.avgExecutionTime = queryStats[0].avg_execution_time || 0;
        }
      } catch (error) {
        fastify.log.warn('Could not get query statistics:', error.message);
      }
      
      // Add Socket.IO metrics if available
      if (fastify.io) {
        metrics.socketio = {
          connectedClients: fastify.io.engine.clientsCount || 0,
          totalConnections: fastify.io.engine.generateId ? fastify.io.engine.generateId.toString().length : 0
        };
      }
      
      return reply.send({
        success: true,
        message: 'Application metrics retrieved successfully',
        data: metrics
      });
      
    } catch (error) {
      fastify.log.error('Metrics error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve application metrics'
      });
    }
  });
  
  // Server configuration info (admin only)
  fastify.get('/config', {
    preHandler: [fastify.authenticate, fastify.authorize(['admin'])]
  }, async (request, reply) => {
    try {
      const config = {
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        server: {
          port: process.env.PORT || 88,
          host: process.env.HOST || '0.0.0.0',
          trustProxy: process.env.TRUST_PROXY === 'true'
        },
        database: {
          host: process.env.DB_HOST || 'localhost',
          port: process.env.DB_PORT || 3306,
          database: process.env.DB_NAME || 'sintesa_db',
          // Don't expose sensitive info
          connectionLimit: process.env.DB_CONNECTION_LIMIT || 10
        },
        redis: {
          enabled: !!fastify.redis,
          host: process.env.REDIS_HOST || 'localhost',
          port: process.env.REDIS_PORT || 6379
        },
        jwt: {
          accessTokenExpiry: process.env.JWT_ACCESS_EXPIRY || '15m',
          refreshTokenExpiry: process.env.JWT_REFRESH_EXPIRY || '7d'
        },
        features: {
          encryption: true,
          socketio: !!fastify.io,
          rateLimit: true,
          cors: true,
          helmet: true
        },
        limits: {
          bodyLimit: process.env.BODY_LIMIT || '10mb',
          connectionTimeout: process.env.CONNECTION_TIMEOUT || 30000,
          keepAliveTimeout: process.env.KEEP_ALIVE_TIMEOUT || 5000
        }
      };
      
      return reply.send({
        success: true,
        message: 'Server configuration retrieved successfully',
        data: config
      });
      
    } catch (error) {
      fastify.log.error('Config error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve server configuration'
      });
    }
  });
  
  // Ping endpoint for load balancers
  fastify.get('/ping', async (request, reply) => {
    return reply.send({ pong: true, timestamp: Date.now() });
  });
  
  // Ready endpoint for container orchestration
  fastify.get('/ready', async (request, reply) => {
    try {
      // Check if all critical services are ready
      let ready = true;
      const checks = {};
      
      // Database check
      try {
        await fastify.db.query('SELECT 1');
        checks.database = 'ready';
      } catch (error) {
        checks.database = 'not_ready';
        ready = false;
      }
      
      // Redis check (optional)
      if (fastify.redis) {
        try {
          await fastify.redis.ping();
          checks.redis = 'ready';
        } catch (error) {
          checks.redis = 'not_ready';
          // Redis is optional, don't mark as not ready
        }
      } else {
        checks.redis = 'not_configured';
      }
      
      const status = ready ? 200 : 503;
      
      return reply.code(status).send({
        ready,
        timestamp: new Date().toISOString(),
        checks
      });
      
    } catch (error) {
      fastify.log.error('Readiness check error:', error);
      return reply.code(503).send({
        ready: false,
        timestamp: new Date().toISOString(),
        error: error.message
      });
    }
  });
  
  // Live endpoint for container orchestration
  fastify.get('/live', async (request, reply) => {
    return reply.send({
      alive: true,
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    });
  });
}