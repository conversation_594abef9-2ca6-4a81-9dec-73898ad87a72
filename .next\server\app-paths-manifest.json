{"/api/auth/token/route": "app/api/auth/token/route.js", "/api/belanja/query/route": "app/api/belanja/query/route.js", "/api/belanja/stats/route": "app/api/belanja/stats/route.js", "/api/captcha-mode/route": "app/api/captcha-mode/route.js", "/api/captcha-mode/switch/route": "app/api/captcha-mode/switch/route.js", "/api/dashboard/route": "app/api/dashboard/route.js", "/api/referensi/route": "app/api/referensi/route.js", "/api/test/route": "app/api/test/route.js", "/_not-found/page": "app/_not-found/page.js", "/(auth)/register/page": "app/(auth)/register/page.js", "/(dashboard)/accounts/page": "app/(dashboard)/accounts/page.js", "/(auth)/offline/page": "app/(auth)/offline/page.js", "/(auth)/login/page": "app/(auth)/login/page.js", "/(dashboard)/balances/banks/page": "app/(dashboard)/balances/banks/page.js", "/(dashboard)/balances/credit-cards/page": "app/(dashboard)/balances/credit-cards/page.js", "/(dashboard)/balances/loans/page": "app/(dashboard)/balances/loans/page.js", "/(dashboard)/dashboard/page": "app/(dashboard)/dashboard/page.js", "/(dashboard)/inquiry-data/kontrak/page": "app/(dashboard)/inquiry-data/kontrak/page.js", "/(dashboard)/epa/summary/page": "app/(dashboard)/epa/summary/page.js", "/(dashboard)/inquiry-data/bansos/page": "app/(dashboard)/inquiry-data/bansos/page.js", "/(dashboard)/balances/page": "app/(dashboard)/balances/page.js", "/(dashboard)/inquiry-data/belanja/page": "app/(dashboard)/inquiry-data/belanja/page.js", "/(dashboard)/epa/analisa/page": "app/(dashboard)/epa/analisa/page.js", "/(dashboard)/inquiry-data/deviasi/page": "app/(dashboard)/inquiry-data/deviasi/page.js", "/(dashboard)/inquiry-data/tematik/page": "app/(dashboard)/inquiry-data/tematik/page.js", "/(dashboard)/mbg/data-update/page": "app/(dashboard)/mbg/data-update/page.js", "/(dashboard)/inquiry-data/rkakl-detail/page": "app/(dashboard)/inquiry-data/rkakl-detail/page.js", "/(dashboard)/mbg/dashboard-mbg/page": "app/(dashboard)/mbg/dashboard-mbg/page.js", "/(dashboard)/inquiry-data/up-tup/page": "app/(dashboard)/inquiry-data/up-tup/page.js", "/(dashboard)/mbg/kertas-kerja/page": "app/(dashboard)/mbg/kertas-kerja/page.js", "/(dashboard)/profil-user/page": "app/(dashboard)/profil-user/page.js", "/(dashboard)/page": "app/(dashboard)/page.js", "/(dashboard)/satker/[id]/page": "app/(dashboard)/satker/[id]/page.js", "/(dashboard)/profilkl/page": "app/(dashboard)/profilkl/page.js", "/(dashboard)/tkd/dau-2025/page": "app/(dashboard)/tkd/dau-2025/page.js", "/(dashboard)/tkd/upload-laporan/page": "app/(dashboard)/tkd/upload-laporan/page.js", "/(dashboard)/tentang-kami/page": "app/(dashboard)/tentang-kami/page.js"}