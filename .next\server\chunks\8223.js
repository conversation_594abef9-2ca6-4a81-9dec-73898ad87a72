"use strict";exports.id=8223,exports.ids=[8223],exports.modules={7236:(a,b,c)=>{c.d(b,{$:()=>f,q:()=>g});var d=c(68762);let e=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,f=(a,b)=>c=>!!("string"==typeof c&&e.test(c)&&c.startsWith(a)||b&&null!=c&&Object.prototype.hasOwnProperty.call(c,b)),g=(a,b,c)=>e=>{if("string"!=typeof e)return e;let[f,g,h,i]=e.match(d.S);return{[a]:parseFloat(f),[b]:parseFloat(g),[c]:parseFloat(h),alpha:void 0!==i?parseFloat(i):1}}},7504:(a,b,c)=>{c.d(b,{y:()=>g});var d=c(95444),e=c(12742),f=c(21874);let g={test:a=>f.B.test(a)||d.u.test(a)||e.V.test(a),parse:a=>f.B.test(a)?f.B.parse(a):e.V.test(a)?e.V.parse(a):d.u.parse(a),transform:a=>"string"==typeof a?a:a.hasOwnProperty("red")?f.B.transform(a):e.V.transform(a),getAnimatableNone:a=>{let b=g.parse(a);return b.alpha=0,g.transform(b)}}},12441:(a,b,c)=>{c.d(b,{V:()=>d});let d=a=>b=>b<=.5?a(2*b)/2:(2-a(2*(1-b)))/2},12742:(a,b,c)=>{c.d(b,{V:()=>h});var d=c(73063),e=c(32874),f=c(97095),g=c(7236);let h={test:(0,g.$)("hsl","hue"),parse:(0,g.q)("hue","saturation","lightness"),transform:({hue:a,saturation:b,lightness:c,alpha:g=1})=>"hsla("+Math.round(a)+", "+e.KN.transform((0,f.a)(b))+", "+e.KN.transform((0,f.a)(c))+", "+(0,f.a)(d.X4.transform(g))+")"}},21874:(a,b,c)=>{c.d(b,{B:()=>i});var d=c(97758),e=c(73063),f=c(97095),g=c(7236);let h={...e.ai,transform:a=>Math.round((0,d.q)(0,255,a))},i={test:(0,g.$)("rgb","red"),parse:(0,g.q)("red","green","blue"),transform:({red:a,green:b,blue:c,alpha:d=1})=>"rgba("+h.transform(a)+", "+h.transform(b)+", "+h.transform(c)+", "+(0,f.a)(e.X4.transform(d))+")"}},26181:(a,b,c)=>{c.d(b,{D:()=>e,I:()=>d});let d={x:!1,y:!1};function e(){return d.x||d.y}},27100:(a,b,c)=>{c.d(b,{F:()=>f,e:()=>e});var d=c(28328);function e(a){return{point:{x:a.pageX,y:a.pageY}}}let f=a=>b=>(0,d.M)(b)&&a(b,e(b))},28328:(a,b,c)=>{c.d(b,{M:()=>d});let d=a=>"mouse"===a.pointerType?"number"!=typeof a.button||a.button<=0:!1!==a.isPrimary},28830:(a,b,c)=>{c.d(b,{G:()=>d});let d=a=>b=>1-a(1-b)},32572:(a,b,c)=>{function d({top:a,left:b,right:c,bottom:d}){return{x:{min:b,max:c},y:{min:a,max:d}}}function e({x:a,y:b}){return{top:b.min,right:a.max,bottom:b.max,left:a.min}}function f(a,b){if(!b)return a;let c=b({x:a.left,y:a.top}),d=b({x:a.right,y:a.bottom});return{top:c.y,left:c.x,bottom:d.y,right:d.x}}c.d(b,{FY:()=>d,bS:()=>f,pA:()=>e})},39664:(a,b,c)=>{c.d(b,{V:()=>k,f:()=>o});var d=c(7504);let e=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var f=c(68762),g=c(97095);let h="number",i="color",j=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function k(a){let b=a.toString(),c=[],e={color:[],number:[],var:[]},f=[],g=0,k=b.replace(j,a=>(d.y.test(a)?(e.color.push(g),f.push(i),c.push(d.y.parse(a))):a.startsWith("var(")?(e.var.push(g),f.push("var"),c.push(a)):(e.number.push(g),f.push(h),c.push(parseFloat(a))),++g,"${}")).split("${}");return{values:c,split:k,indexes:e,types:f}}function l(a){return k(a).values}function m(a){let{split:b,types:c}=k(a),e=b.length;return a=>{let f="";for(let j=0;j<e;j++)if(f+=b[j],void 0!==a[j]){let b=c[j];b===h?f+=(0,g.a)(a[j]):b===i?f+=d.y.transform(a[j]):f+=a[j]}return f}}let n=a=>"number"==typeof a?0:d.y.test(a)?d.y.getAnimatableNone(a):a,o={test:function(a){return isNaN(a)&&"string"==typeof a&&(a.match(f.S)?.length||0)+(a.match(e)?.length||0)>0},parse:l,createTransformer:m,getAnimatableNone:function(a){let b=l(a);return m(a)(b.map(n))}}},41299:(a,b,c)=>{c.d(b,{W:()=>D});var d=c(56570),e=c(28337);function f(a,b,c){let d=a.getProps();return(0,e.a)(d,b,void 0!==c?c:d.custom,a)}var g=c(62923),h=c(23671),i=c(91043),j=c(24342);let k=a=>Array.isArray(a);var l=c(67283),m=c(80722),n=c(96259);function o(a,b,{delay:c=0,transitionOverride:d,type:e}={}){let{transition:p=a.getDefaultTransition(),transitionEnd:q,...r}=b;d&&(p=d);let s=[],t=e&&a.animationState&&a.animationState.getState()[e];for(let b in r){let d=a.getValue(b,a.latestValues[b]??null),e=r[b];if(void 0===e||t&&function({protectedKeys:a,needsAnimating:b},c){let d=a.hasOwnProperty(c)&&!0!==b[c];return b[c]=!1,d}(t,b))continue;let f={delay:c,...(0,g.r)(p||{},b)},j=d.get();if(void 0!==j&&!d.isAnimating&&!Array.isArray(e)&&e===j&&!f.velocity)continue;let k=!1;if(window.MotionHandoffAnimation){let c=(0,m.P)(a);if(c){let a=window.MotionHandoffAnimation(c,b,h.Gt);null!==a&&(f.startTime=a,k=!0)}}(0,l.g)(a,b),d.start((0,n.f)(b,d,e,a.shouldReduceMotion&&i.$.has(b)?{type:!1}:f,a,k));let o=d.animation;o&&s.push(o)}return q&&Promise.all(s).then(()=>{h.Gt.update(()=>{q&&function(a,b){let{transitionEnd:c={},transition:d={},...e}=f(a,b)||{};for(let b in e={...e,...c}){var g;let c=k(g=e[b])?g[g.length-1]||0:g;a.hasValue(b)?a.getValue(b).set(c):a.addValue(b,(0,j.OQ)(c))}}(a,q)})}),s}function p(a,b,c,d=0,e=1){let f=Array.from(a).sort((a,b)=>a.sortNodePosition(b)).indexOf(b),g=a.size,h=(g-1)*d;return"function"==typeof c?c(f,g):1===e?f*d:h-f*d}function q(a,b,c={}){let d=f(a,b,"exit"===c.type?a.presenceContext?.custom:void 0),{transition:e=a.getDefaultTransition()||{}}=d||{};c.transitionOverride&&(e=c.transitionOverride);let g=d?()=>Promise.all(o(a,d,c)):()=>Promise.resolve(),h=a.variantChildren&&a.variantChildren.size?(d=0)=>{let{delayChildren:f=0,staggerChildren:g,staggerDirection:h}=e;return function(a,b,c=0,d=0,e=0,f=1,g){let h=[];for(let i of a.variantChildren)i.notify("AnimationStart",b),h.push(q(i,b,{...g,delay:c+("function"==typeof d?0:d)+p(a.variantChildren,i,d,e,f)}).then(()=>i.notify("AnimationComplete",b)));return Promise.all(h)}(a,b,d,f,g,h,c)}:()=>Promise.resolve(),{when:i}=e;if(!i)return Promise.all([g(),h(c.delay)]);{let[a,b]="beforeChildren"===i?[g,h]:[h,g];return a().then(()=>b())}}function r(a,b){if(!Array.isArray(b))return!1;let c=b.length;if(c!==a.length)return!1;for(let d=0;d<c;d++)if(b[d]!==a[d])return!1;return!0}var s=c(90567),t=c(61328);let u=t._.length,v=[...t.U].reverse(),w=t.U.length;function x(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function y(){return{animate:x(!0),whileInView:x(),whileHover:x(),whileTap:x(),whileDrag:x(),whileFocus:x(),exit:x()}}var z=c(64496);class A extends z.X{constructor(a){super(a),a.animationState||(a.animationState=function(a){let b=b=>Promise.all(b.map(({animation:b,options:c})=>(function(a,b,c={}){let d;if(a.notify("AnimationStart",b),Array.isArray(b))d=Promise.all(b.map(b=>q(a,b,c)));else if("string"==typeof b)d=q(a,b,c);else{let e="function"==typeof b?f(a,b,c.custom):b;d=Promise.all(o(a,e,c))}return d.then(()=>{a.notify("AnimationComplete",b)})})(a,b,c))),c=y(),e=!0,g=b=>(c,d)=>{let e=f(a,d,"exit"===b?a.presenceContext?.custom:void 0);if(e){let{transition:a,transitionEnd:b,...d}=e;c={...c,...d,...b}}return c};function h(h){let{props:i}=a,j=function a(b){if(!b)return;if(!b.isControllingVariants){let c=b.parent&&a(b.parent)||{};return void 0!==b.props.initial&&(c.initial=b.props.initial),c}let c={};for(let a=0;a<u;a++){let d=t._[a],e=b.props[d];((0,s.w)(e)||!1===e)&&(c[d]=e)}return c}(a.parent)||{},l=[],m=new Set,n={},o=1/0;for(let b=0;b<w;b++){var q,x;let t=v[b],u=c[t],w=void 0!==i[t]?i[t]:j[t],y=(0,s.w)(w),z=t===h?u.isActive:null;!1===z&&(o=b);let A=w===j[t]&&w!==i[t]&&y;if(A&&e&&a.manuallyAnimateOnMount&&(A=!1),u.protectedKeys={...n},!u.isActive&&null===z||!w&&!u.prevProp||(0,d.N)(w)||"boolean"==typeof w)continue;let B=(q=u.prevProp,"string"==typeof(x=w)?x!==q:!!Array.isArray(x)&&!r(x,q)),C=B||t===h&&u.isActive&&!A&&y||b>o&&y,D=!1,E=Array.isArray(w)?w:[w],F=E.reduce(g(t),{});!1===z&&(F={});let{prevResolvedValues:G={}}=u,H={...G,...F},I=b=>{C=!0,m.has(b)&&(D=!0,m.delete(b)),u.needsAnimating[b]=!0;let c=a.getValue(b);c&&(c.liveStyle=!1)};for(let a in H){let b=F[a],c=G[a];if(!n.hasOwnProperty(a))(k(b)&&k(c)?r(b,c):b===c)?void 0!==b&&m.has(a)?I(a):u.protectedKeys[a]=!0:null!=b?I(a):m.add(a)}u.prevProp=w,u.prevResolvedValues=F,u.isActive&&(n={...n,...F}),e&&a.blockInitialAnimation&&(C=!1);let J=A&&B,K=!J||D;C&&K&&l.push(...E.map(b=>{let c={type:t};if("string"==typeof b&&e&&!J&&a.manuallyAnimateOnMount&&a.parent){let{parent:d}=a,e=f(d,b);if(d.enteringChildren&&e){let{delayChildren:b}=e.transition||{};c.delay=p(d.enteringChildren,a,b)}}return{animation:b,options:c}}))}if(m.size){let b={};if("boolean"!=typeof i.initial){let c=f(a,Array.isArray(i.initial)?i.initial[0]:i.initial);c&&c.transition&&(b.transition=c.transition)}m.forEach(c=>{let d=a.getBaseTarget(c),e=a.getValue(c);e&&(e.liveStyle=!0),b[c]=d??null}),l.push({animation:b})}let y=!!l.length;return e&&(!1===i.initial||i.initial===i.animate)&&!a.manuallyAnimateOnMount&&(y=!1),e=!1,y?b(l):Promise.resolve()}return{animateChanges:h,setActive:function(b,d){if(c[b].isActive===d)return Promise.resolve();a.variantChildren?.forEach(a=>a.animationState?.setActive(b,d)),c[b].isActive=d;let e=h(b);for(let a in c)c[a].protectedKeys={};return e},setAnimateFunction:function(c){b=c(a)},getState:()=>c,reset:()=>{c=y(),e=!0}}}(a))}updateAnimationControlsSubscription(){let{animate:a}=this.node.getProps();(0,d.N)(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:a}=this.node.getProps(),{animate:b}=this.node.prevProps||{};a!==b&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let B=0;class C extends z.X{constructor(){super(...arguments),this.id=B++}update(){if(!this.node.presenceContext)return;let{isPresent:a,onExitComplete:b}=this.node.presenceContext,{isPresent:c}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===c)return;let d=this.node.animationState.setActive("exit",!a);b&&!a&&d.then(()=>{b(this.id)})}mount(){let{register:a,onExitComplete:b}=this.node.presenceContext||{};b&&b(this.id),a&&(this.unmount=a(this.id))}unmount(){}}let D={animation:{Feature:A},exit:{Feature:C}}},42485:(a,b,c)=>{c.d(b,{OU:()=>j,Ql:()=>k,Ww:()=>m,hq:()=>f,o4:()=>i});var d=c(68028),e=c(67606);function f(a,b,c){return c+b*(a-c)}function g(a,b,c,d,e){return void 0!==e&&(a=d+e*(a-d)),d+c*(a-d)+b}function h(a,b=0,c=1,d,e){a.min=g(a.min,b,c,d,e),a.max=g(a.max,b,c,d,e)}function i(a,{x:b,y:c}){h(a.x,b.translate,b.scale,b.originPoint),h(a.y,c.translate,c.scale,c.originPoint)}function j(a,b,c,d=!1){let f,g,h=c.length;if(h){b.x=b.y=1;for(let j=0;j<h;j++){g=(f=c[j]).projectionDelta;let{visualElement:h}=f.options;(!h||!h.props.style||"contents"!==h.props.style.display)&&(d&&f.options.layoutScroll&&f.scroll&&f!==f.root&&m(a,{x:-f.scroll.offset.x,y:-f.scroll.offset.y}),g&&(b.x*=g.x.scale,b.y*=g.y.scale,i(a,g)),d&&(0,e.HD)(f.latestValues)&&m(a,f.latestValues))}b.x<1.0000000000001&&b.x>.999999999999&&(b.x=1),b.y<1.0000000000001&&b.y>.999999999999&&(b.y=1)}}function k(a,b){a.min=a.min+b,a.max=a.max+b}function l(a,b,c,e,f=.5){let g=(0,d.k)(a.min,a.max,f);h(a,b,c,g,e)}function m(a,b){l(a.x,b.x,b.scaleX,b.scale,b.originX),l(a.y,b.y,b.scaleY,b.scale,b.originY)}},52716:(a,b,c)=>{c.d(b,{po:()=>f,tn:()=>h,yT:()=>g});var d=c(12441),e=c(28830);let f=a=>1-Math.sin(Math.acos(a)),g=(0,e.G)(f),h=(0,d.V)(f)},54538:(a,b,c)=>{c.d(b,{ge:()=>g,xU:()=>e});let d=()=>({translate:0,scale:1,origin:0,originPoint:0}),e=()=>({x:d(),y:d()}),f=()=>({min:0,max:0}),g=()=>({x:f(),y:f()})},57211:(a,b,c)=>{c.d(b,{X:()=>e,f:()=>d});let d=a=>1e3*a,e=a=>a/1e3},58875:(a,b,c)=>{c.d(b,{n:()=>E});var d=c(23671),e=c(26181);function f(a,b){let c=function(a,b,c){if(a instanceof EventTarget)return[a];if("string"==typeof a){let b=document,c=(void 0)??b.querySelectorAll(a);return c?Array.from(c):[]}return Array.from(a)}(a),d=new AbortController;return[c,{passive:!0,...b,signal:d.signal},()=>d.abort()]}function g(a){return!("touch"===a.pointerType||(0,e.D)())}var h=c(27100),i=c(64496);function j(a,b,c){let{props:e}=a;a.animationState&&e.whileHover&&a.animationState.setActive("whileHover","Start"===c);let f=e["onHover"+c];f&&d.Gt.postRender(()=>f(b,(0,h.e)(b)))}class k extends i.X{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,h]=f(a,c),i=a=>{if(!g(a))return;let{target:c}=a,d=b(c,a);if("function"!=typeof d||!c)return;let f=a=>{g(a)&&(d(a),c.removeEventListener("pointerleave",f))};c.addEventListener("pointerleave",f,e)};return d.forEach(a=>{a.addEventListener("pointerenter",i,e)}),h}(a,(a,b)=>(j(this.node,b,"Start"),a=>j(this.node,a,"End"))))}unmount(){}}var l=c(78205),m=c(58902);class n extends i.X{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch(b){a=!0}a&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,l.F)((0,m.k)(this.node.current,"focus",()=>this.onFocus()),(0,m.k)(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var o=c(18171);let p=(a,b)=>!!b&&(a===b||p(a,b.parentElement));var q=c(28328);let r=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),s=new WeakSet;function t(a){return b=>{"Enter"===b.key&&a(b)}}function u(a,b){a.dispatchEvent(new PointerEvent("pointer"+b,{isPrimary:!0,bubbles:!0}))}function v(a){return(0,q.M)(a)&&!(0,e.D)()}function w(a,b,c){let{props:e}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&e.whileTap&&a.animationState.setActive("whileTap","Start"===c);let f=e["onTap"+("End"===c?"":c)];f&&d.Gt.postRender(()=>f(b,(0,h.e)(b)))}class x extends i.X{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,g]=f(a,c),h=a=>{let d=a.currentTarget;if(!v(a))return;s.add(d);let f=b(d,a),g=(a,b)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",i),s.has(d)&&s.delete(d),v(a)&&"function"==typeof f&&f(a,{success:b})},h=a=>{g(a,d===window||d===document||c.useGlobalTarget||p(d,a.target))},i=a=>{g(a,!1)};window.addEventListener("pointerup",h,e),window.addEventListener("pointercancel",i,e)};return d.forEach(a=>{((c.useGlobalTarget?window:a).addEventListener("pointerdown",h,e),(0,o.s)(a))&&(a.addEventListener("focus",a=>((a,b)=>{let c=a.currentTarget;if(!c)return;let d=t(()=>{if(s.has(c))return;u(c,"down");let a=t(()=>{u(c,"up")});c.addEventListener("keyup",a,b),c.addEventListener("blur",()=>u(c,"cancel"),b)});c.addEventListener("keydown",d,b),c.addEventListener("blur",()=>c.removeEventListener("keydown",d),b)})(a,e)),r.has(a.tagName)||-1!==a.tabIndex||a.hasAttribute("tabindex")||(a.tabIndex=0))}),g}(a,(a,b)=>(w(this.node,b,"Start"),(a,{success:b})=>w(this.node,a,b?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let y=new WeakMap,z=new WeakMap,A=a=>{let b=y.get(a.target);b&&b(a)},B=a=>{a.forEach(A)},C={some:0,all:1};class D extends i.X{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:a={}}=this.node.getProps(),{root:b,margin:c,amount:d="some",once:e}=a,f={root:b?b.current:void 0,rootMargin:c,threshold:"number"==typeof d?d:C[d]},g=a=>{let{isIntersecting:b}=a;if(this.isInView===b||(this.isInView=b,e&&!b&&this.hasEnteredView))return;b&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",b);let{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=b?c:d;f&&f(a)};var h=this.node.current;let i=function({root:a,...b}){let c=a||document;z.has(c)||z.set(c,{});let d=z.get(c),e=JSON.stringify(b);return d[e]||(d[e]=new IntersectionObserver(B,{root:a,...b})),d[e]}(f);return y.set(h,g),i.observe(h),()=>{y.delete(h),i.unobserve(h)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:a,prevProps:b}=this.node;["amount","margin","root"].some(function({viewport:a={}},{viewport:b={}}={}){return c=>a[c]!==b[c]}(a,b))&&this.startObserver()}unmount(){}}let E={inView:{Feature:D},tap:{Feature:x},focus:{Feature:n},hover:{Feature:k}}},58902:(a,b,c)=>{c.d(b,{k:()=>d});function d(a,b,c,e={passive:!0}){return a.addEventListener(b,c,e),()=>a.removeEventListener(b,c)}},59039:(a,b,c)=>{c.d(b,{k:()=>d});let{schedule:d}=(0,c(69848).I)(queueMicrotask,!1)},62923:(a,b,c)=>{c.d(b,{r:()=>d});function d(a,b){return a?.[b]??a?.default??a}},64068:(a,b,c)=>{c.d(b,{q:()=>d});let d=(a,b,c)=>{let d=b-a;return 0===d?1:(c-a)/d}},64496:(a,b,c)=>{c.d(b,{X:()=>d});class d{constructor(a){this.isMounted=!1,this.node=a}update(){}}},66244:(a,b,c)=>{c.d(b,{$:()=>d,V:()=>e});let d=()=>{},e=()=>{}},67283:(a,b,c)=>{c.d(b,{g:()=>f});var d=c(97819),e=c(5927);function f(a,b){let c=a.getValue("willChange");if((0,e.S)(c)&&c.add)return c.add(b);if(!c&&d.W.WillChange){let c=new d.W.WillChange("auto");a.addValue("willChange",c),c.add(b)}}},67606:(a,b,c)=>{function d(a){return void 0===a||1===a}function e({scale:a,scaleX:b,scaleY:c}){return!d(a)||!d(b)||!d(c)}function f(a){return e(a)||g(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function g(a){var b,c;return(b=a.x)&&"0%"!==b||(c=a.y)&&"0%"!==c}c.d(b,{HD:()=>f,vF:()=>g,vk:()=>e})},68028:(a,b,c)=>{c.d(b,{k:()=>d});let d=(a,b,c)=>a+(b-a)*c},68762:(a,b,c)=>{c.d(b,{S:()=>d});let d=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},78205:(a,b,c)=>{c.d(b,{F:()=>e});let d=(a,b)=>c=>b(a(c)),e=(...a)=>a.reduce(d)},79602:(a,b,c)=>{c.d(b,{Ib:()=>m,ry:()=>l,zs:()=>k});let d=a=>180*a/Math.PI,e=a=>g(d(Math.atan2(a[1],a[0]))),f={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:e,rotateZ:e,skewX:a=>d(Math.atan(a[1])),skewY:a=>d(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},g=a=>((a%=360)<0&&(a+=360),a),h=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),i=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),j={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:h,scaleY:i,scale:a=>(h(a)+i(a))/2,rotateX:a=>g(d(Math.atan2(a[6],a[5]))),rotateY:a=>g(d(Math.atan2(-a[2],a[0]))),rotateZ:e,rotate:e,skewX:a=>d(Math.atan(a[4])),skewY:a=>d(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function k(a){return+!!a.includes("scale")}function l(a,b){let c,d;if(!a||"none"===a)return k(b);let e=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(e)c=j,d=e;else{let b=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);c=f,d=b}if(!d)return k(b);let g=c[b],h=d[1].split(",").map(n);return"function"==typeof g?g(h):h[g]}let m=(a,b)=>{let{transform:c="none"}=getComputedStyle(a);return l(c,b)};function n(a){return parseFloat(a.trim())}},80722:(a,b,c)=>{c.d(b,{P:()=>e});var d=c(51756);function e(a){return a.props[d.n]}},82319:(a,b,c)=>{c.d(b,{J:()=>ah});var d=c(43210),e=c(55726),f=c(79602),g=c(22238),h=c(92953),i=c(91043),j=c(73063),k=c(32874);let l=a=>b=>b.test(a),m=[j.ai,k.px,k.KN,k.uj,k.vw,k.vh,{test:a=>"auto"===a,parse:a=>a}],n=a=>m.find(l(a));var o=c(66244);let p=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a),q=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;var r=c(90736);let s=a=>/^0[^.\s]+$/u.test(a);var t=c(39664),u=c(68762);let v=new Set(["brightness","contrast","saturate","opacity"]);function w(a){let[b,c]=a.slice(0,-1).split("(");if("drop-shadow"===b)return a;let[d]=c.match(u.S)||[];if(!d)return a;let e=c.replace(d,""),f=+!!v.has(b);return d!==c&&(f*=100),b+"("+f+e+")"}let x=/\b([a-z-]*)\(.*?\)/gu,y={...t.f,getAnimatableNone:a=>{let b=a.match(x);return b?b.map(w).join(" "):a}};var z=c(7504);let A={...c(748).W,color:z.y,backgroundColor:z.y,outlineColor:z.y,fill:z.y,stroke:z.y,borderColor:z.y,borderTopColor:z.y,borderRightColor:z.y,borderBottomColor:z.y,borderLeftColor:z.y,filter:y,WebkitFilter:y},B=a=>A[a];function C(a,b){let c=B(a);return c!==y&&(c=t.f),c.getAnimatableNone?c.getAnimatableNone(b):void 0}let D=new Set(["auto","none","0"]);var E=c(99076);class F extends r.h{constructor(a,b,c,d,e){super(a,b,c,d,e,!0)}readKeyframes(){let{unresolvedKeyframes:a,element:b,name:c}=this;if(!b||!b.current)return;super.readKeyframes();for(let c=0;c<a.length;c++){let d=a[c];if("string"==typeof d&&(d=d.trim(),(0,g.p)(d))){let e=function a(b,c,d=1){(0,o.V)(d<=4,`Max CSS variable fallback depth detected in property "${b}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[e,f]=function(a){let b=q.exec(a);if(!b)return[,];let[,c,d,e]=b;return[`--${c??d}`,e]}(b);if(!e)return;let h=window.getComputedStyle(c).getPropertyValue(e);if(h){let a=h.trim();return p(a)?parseFloat(a):a}return(0,g.p)(f)?a(f,c,d+1):f}(d,b.current);void 0!==e&&(a[c]=e),c===a.length-1&&(this.finalKeyframe=d)}}if(this.resolveNoneKeyframes(),!i.$.has(c)||2!==a.length)return;let[d,e]=a,f=n(d),h=n(e);if(f!==h)if((0,E.E4)(f)&&(0,E.E4)(h))for(let b=0;b<a.length;b++){let c=a[b];"string"==typeof c&&(a[b]=parseFloat(c))}else E.Hr[c]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:a,name:b}=this,c=[];for(let b=0;b<a.length;b++){var d;(null===a[b]||("number"==typeof(d=a[b])?0===d:null===d||"none"===d||"0"===d||s(d)))&&c.push(b)}c.length&&function(a,b,c){let d,e=0;for(;e<a.length&&!d;){let b=a[e];"string"==typeof b&&!D.has(b)&&(0,t.V)(b).values.length&&(d=a[e]),e++}if(d&&c)for(let e of b)a[e]=C(c,d)}(a,c,b)}measureInitialState(){let{element:a,unresolvedKeyframes:b,name:c}=this;if(!a||!a.current)return;"height"===c&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=E.Hr[c](a.measureViewportBox(),window.getComputedStyle(a.current)),b[0]=this.measuredOrigin;let d=b[b.length-1];void 0!==d&&a.getValue(c,d).jump(d,!1)}measureEndState(){let{element:a,name:b,unresolvedKeyframes:c}=this;if(!a||!a.current)return;let d=a.getValue(b);d&&d.jump(this.measuredOrigin,!1);let e=c.length-1,f=c[e];c[e]=E.Hr[b](a.measureViewportBox(),window.getComputedStyle(a.current)),null!==f&&void 0===this.finalKeyframe&&(this.finalKeyframe=f),this.removedTransforms?.length&&this.removedTransforms.forEach(([b,c])=>{a.getValue(b).set(c)}),this.resolveNoneKeyframes()}}var G=c(5927),H=c(24325),I=c(23671),J=c(24342);let K=[...m,z.y,t.f];var L=c(59039),M=c(14296),N=c(29240),O=c(54538),P=c(7044);let Q={current:null},R={current:!1},S=new WeakMap;var T=c(57529),U=c(28337);let V=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class W{scrapeMotionValuesFromProps(a,b,c){return{}}constructor({parent:a,props:b,presenceContext:c,reducedMotionConfig:d,blockInitialAnimation:e,visualState:f},g={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=r.h,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let a=H.k.now();this.renderScheduledAt<a&&(this.renderScheduledAt=a,I.Gt.render(this.render,!1,!0))};let{latestValues:h,renderState:i}=f;this.latestValues=h,this.baseTarget={...h},this.initialValues=b.initial?{...h}:{},this.renderState=i,this.parent=a,this.props=b,this.presenceContext=c,this.depth=a?a.depth+1:0,this.reducedMotionConfig=d,this.options=g,this.blockInitialAnimation=!!e,this.isControllingVariants=(0,T.e)(b),this.isVariantNode=(0,T.O)(b),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);let{willChange:j,...k}=this.scrapeMotionValuesFromProps(b,{},this);for(let a in k){let b=k[a];void 0!==h[a]&&(0,G.S)(b)&&b.set(h[a])}}mount(a){this.current=a,S.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((a,b)=>this.bindToMotionValue(b,a)),R.current||function(){if(R.current=!0,P.B)if(window.matchMedia){let a=window.matchMedia("(prefers-reduced-motion)"),b=()=>Q.current=a.matches;a.addEventListener("change",b),b()}else Q.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Q.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let a in this.projection&&this.projection.unmount(),(0,I.WG)(this.notifyUpdate),(0,I.WG)(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[a].clear();for(let a in this.features){let b=this.features[a];b&&(b.unmount(),b.isMounted=!1)}this.current=null}addChild(a){this.children.add(a),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(a)}removeChild(a){this.children.delete(a),this.enteringChildren&&this.enteringChildren.delete(a)}bindToMotionValue(a,b){let c;this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();let d=e.f.has(a);d&&this.onBindTransform&&this.onBindTransform();let f=b.on("change",b=>{this.latestValues[a]=b,this.props.onUpdate&&I.Gt.preRender(this.notifyUpdate),d&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(c=window.MotionCheckAppearSync(this,a,b)),this.valueSubscriptions.set(a,()=>{f(),c&&c(),b.owner&&b.stop()})}sortNodePosition(a){return this.current&&this.sortInstanceNodePosition&&this.type===a.type?this.sortInstanceNodePosition(this.current,a.current):0}updateFeatures(){let a="animation";for(a in N.B){let b=N.B[a];if(!b)continue;let{isEnabled:c,Feature:d}=b;if(!this.features[a]&&d&&c(this.props)&&(this.features[a]=new d(this)),this.features[a]){let b=this.features[a];b.isMounted?b.update():(b.mount(),b.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):(0,O.ge)()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,b){this.latestValues[a]=b}update(a,b){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=b;for(let b=0;b<V.length;b++){let c=V[b];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);let d=a["on"+c];d&&(this.propEventSubscriptions[c]=this.on(c,d))}this.prevMotionValues=function(a,b,c){for(let d in b){let e=b[d],f=c[d];if((0,G.S)(e))a.addValue(d,e);else if((0,G.S)(f))a.addValue(d,(0,J.OQ)(e,{owner:a}));else if(f!==e)if(a.hasValue(d)){let b=a.getValue(d);!0===b.liveStyle?b.jump(e):b.hasAnimated||b.set(e)}else{let b=a.getStaticValue(d);a.addValue(d,(0,J.OQ)(void 0!==b?b:e,{owner:a}))}}for(let d in c)void 0===b[d]&&a.removeValue(d);return b}(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){let b=this.getClosestVariantNode();if(b)return b.variantChildren&&b.variantChildren.add(a),()=>b.variantChildren.delete(a)}addValue(a,b){let c=this.values.get(a);b!==c&&(c&&this.removeValue(a),this.bindToMotionValue(a,b),this.values.set(a,b),this.latestValues[a]=b.get())}removeValue(a){this.values.delete(a);let b=this.valueSubscriptions.get(a);b&&(b(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,b){if(this.props.values&&this.props.values[a])return this.props.values[a];let c=this.values.get(a);return void 0===c&&void 0!==b&&(c=(0,J.OQ)(null===b?void 0:b,{owner:this}),this.addValue(a,c)),c}readValue(a,b){let c=void 0===this.latestValues[a]&&this.current?this.getBaseTargetFromProps(this.props,a)??this.readValueFromInstance(this.current,a,this.options):this.latestValues[a];if(null!=c){if("string"==typeof c&&(p(c)||s(c)))c=parseFloat(c);else{let d;d=c,!K.find(l(d))&&t.f.test(b)&&(c=C(a,b))}this.setBaseTarget(a,(0,G.S)(c)?c.get():c)}return(0,G.S)(c)?c.get():c}setBaseTarget(a,b){this.baseTarget[a]=b}getBaseTarget(a){let b,{initial:c}=this.props;if("string"==typeof c||"object"==typeof c){let d=(0,U.a)(this.props,c,this.presenceContext?.custom);d&&(b=d[a])}if(c&&void 0!==b)return b;let d=this.getBaseTargetFromProps(this.props,a);return void 0===d||(0,G.S)(d)?void 0!==this.initialValues[a]&&void 0===b?void 0:this.baseTarget[a]:d}on(a,b){return this.events[a]||(this.events[a]=new M.v),this.events[a].add(b)}notify(a,...b){this.events[a]&&this.events[a].notify(...b)}scheduleRenderMicrotask(){L.k.render(this.render)}}class X extends W{constructor(){super(...arguments),this.KeyframeResolver=F}sortInstanceNodePosition(a,b){return 2&a.compareDocumentPosition(b)?1:-1}getBaseTargetFromProps(a,b){return a.style?a.style[b]:void 0}removeValueFromRenderState(a,{vars:b,style:c}){delete b[a],delete c[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:a}=this.props;(0,G.S)(a)&&(this.childSubscription=a.on("change",a=>{this.current&&(this.current.textContent=`${a}`)}))}}var Y=c(58744);function Z(a,{style:b,vars:c},d,e){let f,g=a.style;for(f in b)g[f]=b[f];for(f in e?.applyProjectionStyles(g,d),c)g.setProperty(f,c[f])}var $=c(65934);class _ extends X{constructor(){super(...arguments),this.type="html",this.renderInstance=Z}readValueFromInstance(a,b){if(e.f.has(b))return this.projection?.isProjecting?(0,f.zs)(b):(0,f.Ib)(a,b);{let c=window.getComputedStyle(a),d=((0,g.j)(b)?c.getPropertyValue(b):c[b])||0;return"string"==typeof d?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:b}){return(0,h.m)(a,b)}build(a,b,c){(0,Y.O)(a,b,c.transformTemplate)}scrapeMotionValuesFromProps(a,b,c){return(0,$.x)(a,b,c)}}var aa=c(67886),ab=c(82702);let ac=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);var ad=c(9197),ae=c(98605);class af extends X{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=O.ge}getBaseTargetFromProps(a,b){return a[b]}readValueFromInstance(a,b){if(e.f.has(b)){let a=B(b);return a&&a.default||0}return b=ac.has(b)?b:(0,aa.I)(b),a.getAttribute(b)}scrapeMotionValuesFromProps(a,b,c){return(0,ae.x)(a,b,c)}build(a,b,c){(0,ab.B)(a,b,this.isSVGTag,c.transformTemplate,c.style)}renderInstance(a,b,c,d){for(let c in Z(a,b,void 0,d),b.attrs)a.setAttribute(ac.has(c)?c:(0,aa.I)(c),b.attrs[c])}mount(a){this.isSVGTag=(0,ad.n)(a.tagName),super.mount(a)}}var ag=c(5963);let ah=(a,b)=>(0,ag.Q)(a)?new af(b):new _(b,{allowProjection:a!==d.Fragment})},90736:(a,b,c)=>{c.d(b,{h:()=>m,q:()=>l});var d=c(99076),e=c(23671);let f=new Set,g=!1,h=!1,i=!1;function j(){if(h){let a=Array.from(f).filter(a=>a.needsMeasurement),b=new Set(a.map(a=>a.element)),c=new Map;b.forEach(a=>{let b=(0,d.W9)(a);b.length&&(c.set(a,b),a.render())}),a.forEach(a=>a.measureInitialState()),b.forEach(a=>{a.render();let b=c.get(a);b&&b.forEach(([b,c])=>{a.getValue(b)?.set(c)})}),a.forEach(a=>a.measureEndState()),a.forEach(a=>{void 0!==a.suspendedScrollY&&window.scrollTo(0,a.suspendedScrollY)})}h=!1,g=!1,f.forEach(a=>a.complete(i)),f.clear()}function k(){f.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(h=!0)})}function l(){i=!0,k(),j(),i=!1}class m{constructor(a,b,c,d,e,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=b,this.name=c,this.motionValue=d,this.element=e,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(f.add(this),g||(g=!0,e.Gt.read(k),e.Gt.resolveKeyframes(j))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:a,name:b,element:c,motionValue:d}=this;if(null===a[0]){let e=d?.get(),f=a[a.length-1];if(void 0!==e)a[0]=e;else if(c&&b){let d=c.readValue(b,f);null!=d&&(a[0]=d)}void 0===a[0]&&(a[0]=f),d&&void 0===e&&d.set(a[0])}for(let b=1;b<a.length;b++)a[b]??(a[b]=a[b-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),f.delete(this)}cancel(){"scheduled"===this.state&&(f.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}},91043:(a,b,c)=>{c.d(b,{$:()=>d});let d=new Set(["width","height","top","left","right","bottom",...c(55726).U])},92953:(a,b,c)=>{c.d(b,{L:()=>g,m:()=>f});var d=c(32572),e=c(42485);function f(a,b){return(0,d.FY)((0,d.bS)(a.getBoundingClientRect(),b))}function g(a,b,c){let d=f(a,c),{scroll:g}=b;return g&&((0,e.Ql)(d.x,g.offset.x),(0,e.Ql)(d.y,g.offset.y)),d}},95444:(a,b,c)=>{c.d(b,{u:()=>e});var d=c(21874);let e={test:(0,c(7236).$)("#"),parse:function(a){let b="",c="",d="",e="";return a.length>5?(b=a.substring(1,3),c=a.substring(3,5),d=a.substring(5,7),e=a.substring(7,9)):(b=a.substring(1,2),c=a.substring(2,3),d=a.substring(3,4),e=a.substring(4,5),b+=b,c+=c,d+=d,e+=e),{red:parseInt(b,16),green:parseInt(c,16),blue:parseInt(d,16),alpha:e?parseInt(e,16)/255:1}},transform:d.B.transform}},96184:(a,b,c)=>{c.d(b,{q:()=>d});let d={layout:0,mainThread:0,waapi:0}},96259:(a,b,c)=>{c.d(b,{f:()=>aL});var d=c(62923);function e(a){a.duration=0,a.type}var f=c(23671),g=c(78205),h=c(97758),i=c(57211),j=c(24325),k=c(96184),l=c(66244),m=c(22238),n=c(7504),o=c(39664),p=c(95444),q=c(12742);function r(a,b,c){return(c<0&&(c+=1),c>1&&(c-=1),c<1/6)?a+(b-a)*6*c:c<.5?b:c<2/3?a+(b-a)*(2/3-c)*6:a}var s=c(21874);function t(a,b){return c=>c>0?b:a}var u=c(68028);let v=(a,b,c)=>{let d=a*a,e=c*(b*b-d)+d;return e<0?0:Math.sqrt(e)},w=[p.u,s.B,q.V];function x(a){let b=w.find(b=>b.test(a));if((0,l.$)(!!b,`'${a}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!b)return!1;let c=b.parse(a);return b===q.V&&(c=function({hue:a,saturation:b,lightness:c,alpha:d}){a/=360,c/=100;let e=0,f=0,g=0;if(b/=100){let d=c<.5?c*(1+b):c+b-c*b,h=2*c-d;e=r(h,d,a+1/3),f=r(h,d,a),g=r(h,d,a-1/3)}else e=f=g=c;return{red:Math.round(255*e),green:Math.round(255*f),blue:Math.round(255*g),alpha:d}}(c)),c}let y=(a,b)=>{let c=x(a),d=x(b);if(!c||!d)return t(a,b);let e={...c};return a=>(e.red=v(c.red,d.red,a),e.green=v(c.green,d.green,a),e.blue=v(c.blue,d.blue,a),e.alpha=(0,u.k)(c.alpha,d.alpha,a),s.B.transform(e))},z=new Set(["none","hidden"]);function A(a,b){return c=>(0,u.k)(a,b,c)}function B(a){return"number"==typeof a?A:"string"==typeof a?(0,m.p)(a)?t:n.y.test(a)?y:E:Array.isArray(a)?C:"object"==typeof a?n.y.test(a)?y:D:t}function C(a,b){let c=[...a],d=c.length,e=a.map((a,c)=>B(a)(a,b[c]));return a=>{for(let b=0;b<d;b++)c[b]=e[b](a);return c}}function D(a,b){let c={...a,...b},d={};for(let e in c)void 0!==a[e]&&void 0!==b[e]&&(d[e]=B(a[e])(a[e],b[e]));return a=>{for(let b in d)c[b]=d[b](a);return c}}let E=(a,b)=>{let c=o.f.createTransformer(b),d=(0,o.V)(a),e=(0,o.V)(b);return d.indexes.var.length===e.indexes.var.length&&d.indexes.color.length===e.indexes.color.length&&d.indexes.number.length>=e.indexes.number.length?z.has(a)&&!e.values.length||z.has(b)&&!d.values.length?function(a,b){return z.has(a)?c=>c<=0?a:b:c=>c>=1?b:a}(a,b):(0,g.F)(C(function(a,b){let c=[],d={color:0,var:0,number:0};for(let e=0;e<b.values.length;e++){let f=b.types[e],g=a.indexes[f][d[f]],h=a.values[g]??0;c[e]=h,d[f]++}return c}(d,e),e.values),c):((0,l.$)(!0,`Complex values '${a}' and '${b}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),t(a,b))};function F(a,b,c){return"number"==typeof a&&"number"==typeof b&&"number"==typeof c?(0,u.k)(a,b,c):B(a)(a,b)}let G=a=>{let b=({timestamp:b})=>a(b);return{start:(a=!0)=>f.Gt.update(b,a),stop:()=>(0,f.WG)(b),now:()=>f.uv.isProcessing?f.uv.timestamp:j.k.now()}},H=(a,b,c=10)=>{let d="",e=Math.max(Math.round(b/c),2);for(let b=0;b<e;b++)d+=Math.round(1e4*a(b/(e-1)))/1e4+", ";return`linear(${d.substring(0,d.length-2)})`};function I(a){let b=0,c=a.next(b);for(;!c.done&&b<2e4;)b+=50,c=a.next(b);return b>=2e4?1/0:b}var J=c(15547);function K(a,b,c){let d=Math.max(b-5,0);return(0,J.f)(c-a(d),b-d)}let L={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function M(a,b){return a*Math.sqrt(1-b*b)}let N=["duration","bounce"],O=["stiffness","damping","mass"];function P(a,b){return b.some(b=>void 0!==a[b])}function Q(a=L.visualDuration,b=L.bounce){let c,d="object"!=typeof a?{visualDuration:a,keyframes:[0,1],bounce:b}:a,{restSpeed:e,restDelta:f}=d,g=d.keyframes[0],j=d.keyframes[d.keyframes.length-1],k={done:!1,value:g},{stiffness:m,damping:n,mass:o,duration:p,velocity:q,isResolvedFromDuration:r}=function(a){let b={velocity:L.velocity,stiffness:L.stiffness,damping:L.damping,mass:L.mass,isResolvedFromDuration:!1,...a};if(!P(a,O)&&P(a,N))if(a.visualDuration){let c=2*Math.PI/(1.2*a.visualDuration),d=c*c,e=2*(0,h.q)(.05,1,1-(a.bounce||0))*Math.sqrt(d);b={...b,mass:L.mass,stiffness:d,damping:e}}else{let c=function({duration:a=L.duration,bounce:b=L.bounce,velocity:c=L.velocity,mass:d=L.mass}){let e,f;(0,l.$)(a<=(0,i.f)(L.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let g=1-b;g=(0,h.q)(L.minDamping,L.maxDamping,g),a=(0,h.q)(L.minDuration,L.maxDuration,(0,i.X)(a)),g<1?(e=b=>{let d=b*g,e=d*a;return .001-(d-c)/M(b,g)*Math.exp(-e)},f=b=>{let d=b*g*a,f=Math.pow(g,2)*Math.pow(b,2)*a,h=Math.exp(-d),i=M(Math.pow(b,2),g);return(d*c+c-f)*h*(-e(b)+.001>0?-1:1)/i}):(e=b=>-.001+Math.exp(-b*a)*((b-c)*a+1),f=b=>a*a*(c-b)*Math.exp(-b*a));let j=function(a,b,c){let d=c;for(let c=1;c<12;c++)d-=a(d)/b(d);return d}(e,f,5/a);if(a=(0,i.f)(a),isNaN(j))return{stiffness:L.stiffness,damping:L.damping,duration:a};{let b=Math.pow(j,2)*d;return{stiffness:b,damping:2*g*Math.sqrt(d*b),duration:a}}}(a);(b={...b,...c,mass:L.mass}).isResolvedFromDuration=!0}return b}({...d,velocity:-(0,i.X)(d.velocity||0)}),s=q||0,t=n/(2*Math.sqrt(m*o)),u=j-g,v=(0,i.X)(Math.sqrt(m/o)),w=5>Math.abs(u);if(e||(e=w?L.restSpeed.granular:L.restSpeed.default),f||(f=w?L.restDelta.granular:L.restDelta.default),t<1){let a=M(v,t);c=b=>j-Math.exp(-t*v*b)*((s+t*v*u)/a*Math.sin(a*b)+u*Math.cos(a*b))}else if(1===t)c=a=>j-Math.exp(-v*a)*(u+(s+v*u)*a);else{let a=v*Math.sqrt(t*t-1);c=b=>{let c=Math.exp(-t*v*b),d=Math.min(a*b,300);return j-c*((s+t*v*u)*Math.sinh(d)+a*u*Math.cosh(d))/a}}let x={calculatedDuration:r&&p||null,next:a=>{let b=c(a);if(r)k.done=a>=p;else{let d=0===a?s:0;t<1&&(d=0===a?(0,i.f)(s):K(c,a,b));let g=Math.abs(j-b)<=f;k.done=Math.abs(d)<=e&&g}return k.value=k.done?j:b,k},toString:()=>{let a=Math.min(I(x),2e4),b=H(b=>x.next(a*b).value,a,30);return a+"ms "+b},toTransition:()=>{}};return x}function R({keyframes:a,velocity:b=0,power:c=.8,timeConstant:d=325,bounceDamping:e=10,bounceStiffness:f=500,modifyTarget:g,min:h,max:i,restDelta:j=.5,restSpeed:k}){let l,m,n=a[0],o={done:!1,value:n},p=c*b,q=n+p,r=void 0===g?q:g(q);r!==q&&(p=r-n);let s=a=>-p*Math.exp(-a/d),t=a=>r+s(a),u=a=>{let b=s(a),c=t(a);o.done=Math.abs(b)<=j,o.value=o.done?r:c},v=a=>{let b;if(b=o.value,void 0!==h&&b<h||void 0!==i&&b>i){var c;l=a,m=Q({keyframes:[o.value,(c=o.value,void 0===h?i:void 0===i||Math.abs(h-c)<Math.abs(i-c)?h:i)],velocity:K(t,a,o.value),damping:e,stiffness:f,restDelta:j,restSpeed:k})}};return v(0),{calculatedDuration:null,next:a=>{let b=!1;return(m||void 0!==l||(b=!0,u(a),v(a)),void 0!==l&&a>=l)?m.next(a-l):(b||u(a),o)}}}Q.applyToOptions=a=>{let b=function(a,b=100,c){let d=c({...a,keyframes:[0,b]}),e=Math.min(I(d),2e4);return{type:"keyframes",ease:a=>d.next(e*a).value/b,duration:(0,i.X)(e)}}(a,100,Q);return a.ease=b.ease,a.duration=(0,i.f)(b.duration),a.type="keyframes",a};var S=c(83361);let T=(a,b,c)=>(((1-3*c+3*b)*a+(3*c-6*b))*a+3*b)*a;function U(a,b,c,d){return a===b&&c===d?S.l:e=>0===e||1===e?e:T(function(a,b,c,d,e){let f,g,h=0;do(f=T(g=b+(c-b)/2,d,e)-a)>0?c=g:b=g;while(Math.abs(f)>1e-7&&++h<12);return g}(e,0,1,a,c),b,d)}let V=U(.42,0,1,1),W=U(0,0,.58,1),X=U(.42,0,.58,1);var Y=c(12441),Z=c(28830);let $=U(.33,1.53,.69,.99),_=(0,Z.G)($),aa=(0,Y.V)(_),ab=a=>(a*=2)<1?.5*_(a):.5*(2-Math.pow(2,-10*(a-1)));var ac=c(52716);let ad=a=>Array.isArray(a)&&"number"==typeof a[0],ae={linear:S.l,easeIn:V,easeInOut:X,easeOut:W,circIn:ac.po,circInOut:ac.tn,circOut:ac.yT,backIn:_,backInOut:aa,backOut:$,anticipate:ab},af=a=>{if(ad(a)){(0,l.V)(4===a.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[b,c,d,e]=a;return U(b,c,d,e)}return"string"==typeof a?((0,l.V)(void 0!==ae[a],`Invalid easing type '${a}'`,"invalid-easing-type"),ae[a]):a};var ag=c(97819),ah=c(64068);function ai({duration:a=300,keyframes:b,times:c,ease:d="easeInOut"}){var e;let f=Array.isArray(d)&&"number"!=typeof d[0]?d.map(af):af(d),i={done:!1,value:b[0]},j=function(a,b,{clamp:c=!0,ease:d,mixer:e}={}){let f=a.length;if((0,l.V)(f===b.length,"Both input and output ranges must be the same length","range-length"),1===f)return()=>b[0];if(2===f&&b[0]===b[1])return()=>b[1];let i=a[0]===a[1];a[0]>a[f-1]&&(a=[...a].reverse(),b=[...b].reverse());let j=function(a,b,c){let d=[],e=c||ag.W.mix||F,f=a.length-1;for(let c=0;c<f;c++){let f=e(a[c],a[c+1]);if(b){let a=Array.isArray(b)?b[c]||S.l:b;f=(0,g.F)(a,f)}d.push(f)}return d}(b,d,e),k=j.length,m=c=>{if(i&&c<a[0])return b[0];let d=0;if(k>1)for(;d<a.length-2&&!(c<a[d+1]);d++);let e=(0,ah.q)(a[d],a[d+1],c);return j[d](e)};return c?b=>m((0,h.q)(a[0],a[f-1],b)):m}((e=c&&c.length===b.length?c:function(a){let b=[0];return!function(a,b){let c=a[a.length-1];for(let d=1;d<=b;d++){let e=(0,ah.q)(0,b,d);a.push((0,u.k)(c,1,e))}}(b,a.length-1),b}(b),e.map(b=>b*a)),b,{ease:Array.isArray(f)?f:b.map(()=>f||X).splice(0,b.length-1)});return{calculatedDuration:a,next:b=>(i.value=j(b),i.done=b>=a,i)}}let aj=a=>null!==a;function ak(a,{repeat:b,repeatType:c="loop"},d,e=1){let f=a.filter(aj),g=e<0||b&&"loop"!==c&&b%2==1?0:f.length-1;return g&&void 0!==d?d:f[g]}let al={decay:R,inertia:R,tween:ai,keyframes:ai,spring:Q};function am(a){"string"==typeof a.type&&(a.type=al[a.type])}class an{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,b){return this.finished.then(a,b)}}let ao=a=>a/100;class ap extends an{constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:a}=this.options;a&&a.updatedAt!==j.k.now()&&this.tick(j.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},k.q.mainThread++,this.options=a,this.initAnimation(),this.play(),!1===a.autoplay&&this.pause()}initAnimation(){let{options:a}=this;am(a);let{type:b=ai,repeat:c=0,repeatDelay:d=0,repeatType:e,velocity:f=0}=a,{keyframes:h}=a,i=b||ai;i!==ai&&"number"!=typeof h[0]&&(this.mixKeyframes=(0,g.F)(ao,F(h[0],h[1])),h=[0,100]);let j=i({...a,keyframes:h});"mirror"===e&&(this.mirroredGenerator=i({...a,keyframes:[...h].reverse(),velocity:-f})),null===j.calculatedDuration&&(j.calculatedDuration=I(j));let{calculatedDuration:k}=j;this.calculatedDuration=k,this.resolvedDuration=k+d,this.totalDuration=this.resolvedDuration*(c+1)-d,this.generator=j}updateTime(a){let b=Math.round(a-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=b}tick(a,b=!1){let{generator:c,totalDuration:d,mixKeyframes:e,mirroredGenerator:f,resolvedDuration:g,calculatedDuration:i}=this;if(null===this.startTime)return c.next(0);let{delay:j=0,keyframes:k,repeat:l,repeatType:m,repeatDelay:n,type:o,onUpdate:p,finalKeyframe:q}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-d/this.speed,this.startTime)),b?this.currentTime=a:this.updateTime(a);let r=this.currentTime-j*(this.playbackSpeed>=0?1:-1),s=this.playbackSpeed>=0?r<0:r>d;this.currentTime=Math.max(r,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=d);let t=this.currentTime,u=c;if(l){let a=Math.min(this.currentTime,d)/g,b=Math.floor(a),c=a%1;!c&&a>=1&&(c=1),1===c&&b--,(b=Math.min(b,l+1))%2&&("reverse"===m?(c=1-c,n&&(c-=n/g)):"mirror"===m&&(u=f)),t=(0,h.q)(0,1,c)*g}let v=s?{done:!1,value:k[0]}:u.next(t);e&&(v.value=e(v.value));let{done:w}=v;s||null===i||(w=this.playbackSpeed>=0?this.currentTime>=d:this.currentTime<=0);let x=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return x&&o!==R&&(v.value=ak(k,this.options,q,this.speed)),p&&p(v.value),x&&this.finish(),v}then(a,b){return this.finished.then(a,b)}get duration(){return(0,i.X)(this.calculatedDuration)}get time(){return(0,i.X)(this.currentTime)}set time(a){a=(0,i.f)(a),this.currentTime=a,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(j.k.now());let b=this.playbackSpeed!==a;this.playbackSpeed=a,b&&(this.time=(0,i.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:a=G,startTime:b}=this.options;this.driver||(this.driver=a(a=>this.tick(a))),this.options.onPlay?.();let c=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=c):null!==this.holdTime?this.startTime=c-this.holdTime:this.startTime||(this.startTime=b??c),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(j.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,k.q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),a.observe(this)}}var aq=c(90736);function ar(a){let b;return()=>(void 0===b&&(b=a()),b)}let as=ar(()=>void 0!==window.ScrollTimeline);var at=c(82082);let au={},av=function(a,b){let c=ar(a);return()=>au[b]??c()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(a){return!1}return!0},"linearEasing"),aw=([a,b,c,d])=>`cubic-bezier(${a}, ${b}, ${c}, ${d})`,ax={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:aw([0,.65,.55,1]),circOut:aw([.55,0,1,.45]),backIn:aw([.31,.01,.66,-.59]),backOut:aw([.33,1.53,.69,.99])};function ay(a){return"function"==typeof a&&"applyToOptions"in a}class az extends an{constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;let{element:b,name:c,keyframes:d,pseudoElement:e,allowFlatten:f=!1,finalKeyframe:g,onComplete:h}=a;this.isPseudoElement=!!e,this.allowFlatten=f,this.options=a,(0,l.V)("string"!=typeof a.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let i=function({type:a,...b}){return ay(a)&&av()?a.applyToOptions(b):(b.duration??(b.duration=300),b.ease??(b.ease="easeOut"),b)}(a);this.animation=function(a,b,c,{delay:d=0,duration:e=300,repeat:f=0,repeatType:g="loop",ease:h="easeOut",times:i}={},j){let l={[b]:c};i&&(l.offset=i);let m=function a(b,c){if(b)return"function"==typeof b?av()?H(b,c):"ease-out":ad(b)?aw(b):Array.isArray(b)?b.map(b=>a(b,c)||ax.easeOut):ax[b]}(h,e);Array.isArray(m)&&(l.easing=m),at.Q.value&&k.q.waapi++;let n={delay:d,duration:e,easing:Array.isArray(m)?"linear":m,fill:"both",iterations:f+1,direction:"reverse"===g?"alternate":"normal"};j&&(n.pseudoElement=j);let o=a.animate(l,n);return at.Q.value&&o.finished.finally(()=>{k.q.waapi--}),o}(b,c,d,i,e),!1===i.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!e){let a=ak(d,this.options,g,this.speed);this.updateMotionValue?this.updateMotionValue(a):function(a,b,c){b.startsWith("--")?a.style.setProperty(b,c):a.style[b]=c}(b,c,a),this.animation.cancel()}h?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(a){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:a}=this;"idle"!==a&&"finished"!==a&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let a=this.animation.effect?.getComputedTiming?.().duration||0;return(0,i.X)(Number(a))}get time(){return(0,i.X)(Number(this.animation.currentTime)||0)}set time(a){this.finishedTime=null,this.animation.currentTime=(0,i.f)(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:b}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,a&&as())?(this.animation.timeline=a,S.l):b(this)}}let aA={anticipate:ab,backInOut:aa,circInOut:ac.tn};class aB extends az{constructor(a){!function(a){"string"==typeof a.ease&&a.ease in aA&&(a.ease=aA[a.ease])}(a),am(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){let{motionValue:b,onUpdate:c,onComplete:d,element:e,...f}=this.options;if(!b)return;if(void 0!==a)return void b.set(a);let g=new ap({...f,autoplay:!1}),h=(0,i.f)(this.finishedTime??this.time);b.setWithVelocity(g.sample(h-10).value,g.sample(h).value,10),g.stop()}}let aC=(a,b)=>"zIndex"!==b&&!!("number"==typeof a||Array.isArray(a)||"string"==typeof a&&(o.f.test(a)||"0"===a)&&!a.startsWith("url(")),aD=new Set(["opacity","clipPath","filter","transform"]),aE=ar(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class aF extends an{constructor({autoplay:a=!0,delay:b=0,type:c="keyframes",repeat:d=0,repeatDelay:e=0,repeatType:f="loop",keyframes:g,name:h,motionValue:i,element:k,...l}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=j.k.now();let m={autoplay:a,delay:b,type:c,repeat:d,repeatDelay:e,repeatType:f,name:h,motionValue:i,element:k,...l},n=k?.KeyframeResolver||aq.h;this.keyframeResolver=new n(g,(a,b,c)=>this.onKeyframesResolved(a,b,m,!c),h,i,k),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(a,b,c,d){this.keyframeResolver=void 0;let{name:f,type:g,velocity:h,delay:i,isHandoff:k,onUpdate:m}=c;this.resolvedAt=j.k.now(),!function(a,b,c,d){let e=a[0];if(null===e)return!1;if("display"===b||"visibility"===b)return!0;let f=a[a.length-1],g=aC(e,b),h=aC(f,b);return(0,l.$)(g===h,`You are trying to animate ${b} from "${e}" to "${f}". "${g?f:e}" is not an animatable value.`,"value-not-animatable"),!!g&&!!h&&(function(a){let b=a[0];if(1===a.length)return!0;for(let c=0;c<a.length;c++)if(a[c]!==b)return!0}(a)||("spring"===c||ay(c))&&d)}(a,f,g,h)&&((ag.W.instantAnimations||!i)&&m?.(ak(a,c,b)),a[0]=a[a.length-1],e(c),c.repeat=0);let n={startTime:d?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:b,...c,keyframes:a},o=!k&&function(a){let{motionValue:b,name:c,repeatDelay:d,repeatType:e,damping:f,type:g}=a;if(!(b?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:h,transformTemplate:i}=b.owner.getProps();return aE()&&c&&aD.has(c)&&("transform"!==c||!i)&&!h&&!d&&"mirror"!==e&&0!==f&&"inertia"!==g}(n)?new aB({...n,element:n.motionValue.owner.current}):new ap(n);o.finished.then(()=>this.notifyFinished()).catch(S.l),this.pendingTimeline&&(this.stopTimeline=o.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=o}get finished(){return this._animation?this.animation.finished:this._finished}then(a,b){return this.finished.finally(a).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),(0,aq.q)()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let aG=a=>null!==a;var aH=c(55726);let aI={type:"spring",stiffness:500,damping:25,restSpeed:10},aJ={type:"keyframes",duration:.8},aK={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},aL=(a,b,c,g={},h,j)=>k=>{let l=(0,d.r)(g,a)||{},m=l.delay||g.delay||0,{elapsed:n=0}=g;n-=(0,i.f)(m);let o={keyframes:Array.isArray(c)?c:[null,c],ease:"easeOut",velocity:b.getVelocity(),...l,delay:-n,onUpdate:a=>{b.set(a),l.onUpdate&&l.onUpdate(a)},onComplete:()=>{k(),l.onComplete&&l.onComplete()},name:a,motionValue:b,element:j?void 0:h};!function({when:a,delay:b,delayChildren:c,staggerChildren:d,staggerDirection:e,repeat:f,repeatType:g,repeatDelay:h,from:i,elapsed:j,...k}){return!!Object.keys(k).length}(l)&&Object.assign(o,((a,{keyframes:b})=>b.length>2?aJ:aH.f.has(a)?a.startsWith("scale")?{type:"spring",stiffness:550,damping:0===b[1]?2*Math.sqrt(550):30,restSpeed:10}:aI:aK)(a,o)),o.duration&&(o.duration=(0,i.f)(o.duration)),o.repeatDelay&&(o.repeatDelay=(0,i.f)(o.repeatDelay)),void 0!==o.from&&(o.keyframes[0]=o.from);let p=!1;if(!1!==o.type&&(0!==o.duration||o.repeatDelay)||(e(o),0===o.delay&&(p=!0)),(ag.W.instantAnimations||ag.W.skipAnimations)&&(p=!0,e(o),o.delay=0),o.allowFlatten=!l.type&&!l.ease,p&&!j&&void 0!==b.get()){let a=function(a,{repeat:b,repeatType:c="loop"},d){let e=a.filter(aG),f=b&&"loop"!==c&&b%2==1?0:e.length-1;return e[f]}(o.keyframes,l);if(void 0!==a)return void f.Gt.update(()=>{o.onUpdate(a),o.onComplete()})}return l.isSync?new ap(o):new aF(o)}},97095:(a,b,c)=>{c.d(b,{a:()=>d});let d=a=>Math.round(1e5*a)/1e5},99076:(a,b,c)=>{c.d(b,{E4:()=>h,Hr:()=>l,W9:()=>k});var d=c(79602),e=c(55726),f=c(73063),g=c(32874);let h=a=>a===f.ai||a===g.px,i=new Set(["x","y","z"]),j=e.U.filter(a=>!i.has(a));function k(a){let b=[];return j.forEach(c=>{let d=a.getValue(c);void 0!==d&&(b.push([c,d.get()]),d.set(+!!c.startsWith("scale")))}),b}let l={width:({x:a},{paddingLeft:b="0",paddingRight:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),height:({y:a},{paddingTop:b="0",paddingBottom:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),top:(a,{top:b})=>parseFloat(b),left:(a,{left:b})=>parseFloat(b),bottom:({y:a},{top:b})=>parseFloat(b)+(a.max-a.min),right:({x:a},{left:b})=>parseFloat(b)+(a.max-a.min),x:(a,{transform:b})=>(0,d.ry)(b,"x"),y:(a,{transform:b})=>(0,d.ry)(b,"y")};l.translateX=l.x,l.translateY=l.y}};