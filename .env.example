# Server Configuration
PORT=88
HOST=0.0.0.0
NODE_ENV=development
TRUST_PROXY=false

# Database Configuration
DB_HOST=localhost
DB_PORT=3399
DB_USER=root
DB_PASSWORD=pdpsipa2025
DB_NAME=v3_next
DB_CONNECTION_LIMIT=10
DB_TIMEOUT=30000

# Redis Configuration (Optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=sintesa:

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_ACCESS_EXPIRY=15m
JWT_REFRESH_EXPIRY=7d
JWT_ISSUER=sintesa-backend
JWT_AUDIENCE=sintesa-frontend

# Encryption Configuration
ENCRYPTION_SECRET=mebe23
ENCRYPTION_ALGORITHM=aes-256-cbc
HMAC_SECRET=your-hmac-secret-key

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_SKIP_SUCCESSFUL=false

# File Upload
UPLOAD_MAX_FILE_SIZE=10485760
UPLOAD_MAX_FILES=5
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf,text/csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

# Logging
LOG_LEVEL=info
LOG_FILE=logs/sintesa-backend.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Security
BODY_LIMIT=10mb
CONNECTION_TIMEOUT=30000
KEEP_ALIVE_TIMEOUT=5000
REQUEST_TIMEOUT=30000

# Session Configuration
SESSION_SECRET=your-session-secret-key
SESSION_EXPIRY=86400
SESSION_CLEANUP_INTERVAL=3600

# Query Configuration
QUERY_TIMEOUT=30000
QUERY_MAX_ROWS=10000
QUERY_CACHE_TTL=300

# Socket.IO Configuration
SOCKET_CORS_ORIGIN=http://localhost:3000,http://localhost:3001
SOCKET_PING_TIMEOUT=60000
SOCKET_PING_INTERVAL=25000

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=Sintesa Backend <<EMAIL>>

# Backup Configuration (Optional)
BACKUP_ENABLED=false
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=./backups

# Monitoring Configuration (Optional)
MONITORING_ENABLED=false
MONITORING_INTERVAL=60000
HEALTH_CHECK_INTERVAL=30000

# Development Configuration
DEV_ENABLE_PLAYGROUND=true
DEV_ENABLE_INTROSPECTION=true
DEV_ENABLE_DEBUG=true

# Production Configuration
PROD_ENABLE_COMPRESSION=true
PROD_ENABLE_HELMET=true
PROD_TRUST_PROXY=true

# API Documentation
API_DOCS_ENABLED=true
API_DOCS_PATH=/docs
API_DOCS_TITLE=Sintesa Backend API
API_DOCS_VERSION=1.0.0

# Cache Configuration
CACHE_DEFAULT_TTL=300
CACHE_MAX_KEYS=10000
CACHE_CHECK_PERIOD=600

# Webhook Configuration (Optional)
WEBHOOK_SECRET=your-webhook-secret
WEBHOOK_TIMEOUT=5000

# External API Configuration
EXTERNAL_API_TIMEOUT=10000
EXTERNAL_API_RETRIES=3
EXTERNAL_API_RETRY_DELAY=1000

# Feature Flags
FEATURE_ENCRYPTION=true
FEATURE_RATE_LIMITING=true
FEATURE_CACHING=true
FEATURE_WEBSOCKETS=true
FEATURE_FILE_UPLOAD=true
FEATURE_EMAIL_NOTIFICATIONS=false
FEATURE_BACKUP=false
FEATURE_MONITORING=false

# Timezone
TZ=Asia/Jakarta

# Application Metadata
APP_NAME=Sintesa Backend
APP_VERSION=1.0.0
APP_DESCRIPTION=Modern and fast backend for Sintesa financial dashboard
APP_AUTHOR=Sintesa Development Team
APP_LICENSE=MIT