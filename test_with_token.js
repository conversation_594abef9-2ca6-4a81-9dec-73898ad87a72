import jwt from "jsonwebtoken";
import CryptoJS from "crypto-js";
import http from "http";

// JWT configuration (matching the backend)
const JWT_SECRET = "sintesa-super-secret-jwt-key-2024-change-in-production";
const ENCRYPTION_SECRET = "mebe23";

// Create a test JWT token
const payload = {
  id: 1,
  username: "testuser",
  role: "admin",
  kdkanwil: "01",
  kdkppn: "001",
  kdlokasi: "001",
  active: 1,
  // Don't set iat and exp manually, let jwt.sign() handle it
};

const accessToken = jwt.sign(payload, JWT_SECRET, {
  expiresIn: "8h",
  issuer: "sintesa-backend",
  audience: "sintesa-frontend",
});
console.log("Generated JWT token:", accessToken);

// Encrypt the token (like the frontend does - matching Encrypt.jsx)
const encJson = CryptoJS.AES.encrypt(accessToken, ENCRYPTION_SECRET).toString();
const encryptedToken = CryptoJS.enc.Base64.stringify(
  CryptoJS.enc.Utf8.parse(encJson)
);
console.log("Encrypted token:", encryptedToken);

// Test query (encrypted - matching frontend format)
const testQuery =
  "SELECT kddept, nmdept FROM dbref.t_dept_2025 ORDER BY kddept ASC LIMIT 5";
const encQueryJson = CryptoJS.AES.encrypt(
  testQuery,
  ENCRYPTION_SECRET
).toString();
const encryptedQuery = CryptoJS.enc.Base64.stringify(
  CryptoJS.enc.Utf8.parse(encQueryJson)
);

const postData = JSON.stringify({
  query: encryptedQuery,
});

const options = {
  hostname: "localhost",
  port: 88,
  path: "/next/referensi",
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    Authorization: `Bearer ${encryptedToken}`,
    Accept: "application/json, text/plain, */*",
    "Content-Length": Buffer.byteLength(postData),
  },
};

console.log("\n=== Testing /next/referensi with encrypted token ===");
console.log("Request headers:", options.headers);

const req = http.request(options, (res) => {
  console.log(`\nStatus: ${res.statusCode}`);
  console.log(`Headers:`, res.headers);

  let data = "";
  res.on("data", (chunk) => {
    data += chunk;
  });

  res.on("end", () => {
    console.log("\nResponse:", data);

    if (res.statusCode === 200) {
      console.log("\n✅ SUCCESS: Authentication and query execution worked!");
    } else {
      console.log("\n❌ FAILED: Status code", res.statusCode);
    }
  });
});

req.on("error", (e) => {
  console.error(`\n❌ Request error: ${e.message}`);
});

req.write(postData);
req.end();
