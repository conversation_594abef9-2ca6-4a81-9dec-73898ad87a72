import <PERSON><PERSON> from 'joi';
import bcrypt from 'bcryptjs';

// User management routes
export default async function userRoutes(fastify, options) {
  // Get current user profile
  fastify.get('/profile', {
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                userId: { type: 'integer' },
                username: { type: 'string' },
                email: { type: 'string' },
                fullName: { type: 'string' },
                role: { type: 'string' },
                department: { type: 'string' },
                lastLogin: { type: 'string' },
                isActive: { type: 'boolean' },
                createdAt: { type: 'string' },
                preferences: { type: 'object' }
              }
            }
          }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const userId = request.user.userId;
      
      const [userResults] = await fastify.db.query(`
        SELECT 
          user_id,
          username,
          email,
          full_name,
          role,
          department,
          last_login,
          is_active,
          created_at,
          preferences
        FROM users 
        WHERE user_id = ?
      `, [userId]);
      
      if (userResults.length === 0) {
        return reply.code(404).send({
          success: false,
          message: 'User not found'
        });
      }
      
      const user = userResults[0];
      
      // Parse preferences if it's a JSON string
      let preferences = {};
      if (user.preferences) {
        try {
          preferences = typeof user.preferences === 'string' 
            ? JSON.parse(user.preferences) 
            : user.preferences;
        } catch (error) {
          fastify.log.warn('Failed to parse user preferences:', error.message);
        }
      }
      
      return reply.send({
        success: true,
        message: 'User profile retrieved successfully',
        data: {
          userId: user.user_id,
          username: user.username,
          email: user.email,
          fullName: user.full_name,
          role: user.role,
          department: user.department,
          lastLogin: user.last_login,
          isActive: user.is_active === 1,
          createdAt: user.created_at,
          preferences
        }
      });
      
    } catch (error) {
      fastify.log.error('Get profile error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve user profile'
      });
    }
  });
  
  // Update current user profile
  fastify.put('/profile', {
    schema: {
      body: {
        type: 'object',
        properties: {
          email: { type: 'string', format: 'email' },
          fullName: { type: 'string', minLength: 1, maxLength: 255 },
          department: { type: 'string', maxLength: 100 },
          preferences: { type: 'object' }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const userId = request.user.userId;
      const { email, fullName, department, preferences } = request.body;
      
      // Build update query dynamically
      const updateFields = [];
      const updateValues = [];
      
      if (email !== undefined) {
        // Check if email is already taken by another user
        const [emailCheck] = await fastify.db.query(
          'SELECT user_id FROM users WHERE email = ? AND user_id != ?',
          [email, userId]
        );
        
        if (emailCheck.length > 0) {
          return reply.code(400).send({
            success: false,
            message: 'Email is already taken by another user'
          });
        }
        
        updateFields.push('email = ?');
        updateValues.push(email);
      }
      
      if (fullName !== undefined) {
        updateFields.push('full_name = ?');
        updateValues.push(fullName);
      }
      
      if (department !== undefined) {
        updateFields.push('department = ?');
        updateValues.push(department);
      }
      
      if (preferences !== undefined) {
        updateFields.push('preferences = ?');
        updateValues.push(JSON.stringify(preferences));
      }
      
      if (updateFields.length === 0) {
        return reply.code(400).send({
          success: false,
          message: 'No fields to update'
        });
      }
      
      updateFields.push('updated_at = NOW()');
      updateValues.push(userId);
      
      const updateQuery = `
        UPDATE users 
        SET ${updateFields.join(', ')}
        WHERE user_id = ?
      `;
      
      await fastify.db.query(updateQuery, updateValues);
      
      fastify.log.info(`User profile updated for user ${request.user.username}`);
      
      return reply.send({
        success: true,
        message: 'Profile updated successfully'
      });
      
    } catch (error) {
      fastify.log.error('Update profile error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to update profile'
      });
    }
  });
  
  // Change password
  fastify.put('/password', {
    schema: {
      body: {
        type: 'object',
        required: ['currentPassword', 'newPassword'],
        properties: {
          currentPassword: { type: 'string', minLength: 1 },
          newPassword: { type: 'string', minLength: 6, maxLength: 128 }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const userId = request.user.userId;
      const { currentPassword, newPassword } = request.body;
      
      // Get current password hash
      const [userResults] = await fastify.db.query(
        'SELECT password_hash FROM users WHERE user_id = ?',
        [userId]
      );
      
      if (userResults.length === 0) {
        return reply.code(404).send({
          success: false,
          message: 'User not found'
        });
      }
      
      // Verify current password
      const isCurrentPasswordValid = await fastify.auth.comparePassword(
        currentPassword, 
        userResults[0].password_hash
      );
      
      if (!isCurrentPasswordValid) {
        return reply.code(400).send({
          success: false,
          message: 'Current password is incorrect'
        });
      }
      
      // Hash new password
      const newPasswordHash = await fastify.auth.hashPassword(newPassword);
      
      // Update password
      await fastify.db.query(
        'UPDATE users SET password_hash = ?, updated_at = NOW() WHERE user_id = ?',
        [newPasswordHash, userId]
      );
      
      fastify.log.info(`Password changed for user ${request.user.username}`);
      
      return reply.send({
        success: true,
        message: 'Password changed successfully'
      });
      
    } catch (error) {
      fastify.log.error('Change password error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to change password'
      });
    }
  });
  
  // Get all users (admin only)
  fastify.get('/', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          search: { type: 'string' },
          role: { type: 'string', enum: ['admin', 'user', 'viewer'] },
          department: { type: 'string' },
          isActive: { type: 'boolean' },
          sortBy: { type: 'string', enum: ['username', 'email', 'full_name', 'role', 'created_at', 'last_login'], default: 'created_at' },
          sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'desc' }
        }
      }
    },
    preHandler: [fastify.authenticate, fastify.authorize(['admin'])]
  }, async (request, reply) => {
    try {
      const { page, limit, search, role, department, isActive, sortBy, sortOrder } = request.query;
      const offset = (page - 1) * limit;
      
      // Build WHERE conditions
      let whereConditions = [];
      let queryParams = [];
      
      if (search) {
        whereConditions.push('(username LIKE ? OR email LIKE ? OR full_name LIKE ?)');
        const searchPattern = `%${search}%`;
        queryParams.push(searchPattern, searchPattern, searchPattern);
      }
      
      if (role) {
        whereConditions.push('role = ?');
        queryParams.push(role);
      }
      
      if (department) {
        whereConditions.push('department = ?');
        queryParams.push(department);
      }
      
      if (isActive !== undefined) {
        whereConditions.push('is_active = ?');
        queryParams.push(isActive ? 1 : 0);
      }
      
      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
      
      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`;
      const [countResults] = await fastify.db.query(countQuery, queryParams);
      const total = countResults[0].total;
      
      // Get users
      const usersQuery = `
        SELECT 
          user_id,
          username,
          email,
          full_name,
          role,
          department,
          is_active,
          last_login,
          created_at,
          updated_at
        FROM users 
        ${whereClause}
        ORDER BY ${sortBy} ${sortOrder.toUpperCase()}
        LIMIT ? OFFSET ?
      `;
      
      const [userResults] = await fastify.db.query(usersQuery, [...queryParams, limit, offset]);
      
      // Get user statistics
      const [statsResults] = await fastify.db.query(`
        SELECT 
          COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as active_24h,
          COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_7d,
          COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as active_30d,
          COUNT(CASE WHEN is_active = 1 THEN 1 END) as total_active,
          COUNT(CASE WHEN is_active = 0 THEN 1 END) as total_inactive
        FROM users
      `);
      
      const users = userResults.map(user => ({
        userId: user.user_id,
        username: user.username,
        email: user.email,
        fullName: user.full_name,
        role: user.role,
        department: user.department,
        isActive: user.is_active === 1,
        lastLogin: user.last_login,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }));
      
      return reply.send({
        success: true,
        message: 'Users retrieved successfully',
        data: {
          users,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          },
          statistics: statsResults[0]
        }
      });
      
    } catch (error) {
      fastify.log.error('Get users error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve users'
      });
    }
  });
  
  // Get user by ID (admin only)
  fastify.get('/:userId', {
    schema: {
      params: {
        type: 'object',
        required: ['userId'],
        properties: {
          userId: { type: 'integer', minimum: 1 }
        }
      }
    },
    preHandler: [fastify.authenticate, fastify.authorize(['admin'])]
  }, async (request, reply) => {
    try {
      const { userId } = request.params;
      
      const [userResults] = await fastify.db.query(`
        SELECT 
          user_id,
          username,
          email,
          full_name,
          role,
          department,
          is_active,
          last_login,
          created_at,
          updated_at,
          preferences
        FROM users 
        WHERE user_id = ?
      `, [userId]);
      
      if (userResults.length === 0) {
        return reply.code(404).send({
          success: false,
          message: 'User not found'
        });
      }
      
      const user = userResults[0];
      
      // Parse preferences
      let preferences = {};
      if (user.preferences) {
        try {
          preferences = typeof user.preferences === 'string' 
            ? JSON.parse(user.preferences) 
            : user.preferences;
        } catch (error) {
          fastify.log.warn('Failed to parse user preferences:', error.message);
        }
      }
      
      // Get user activity statistics
      const [activityResults] = await fastify.db.query(`
        SELECT 
          COUNT(*) as total_queries,
          COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_queries,
          AVG(execution_time) as avg_execution_time,
          MAX(created_at) as last_query
        FROM query_logs 
        WHERE user_id = ?
      `, [userId]);
      
      return reply.send({
        success: true,
        message: 'User retrieved successfully',
        data: {
          userId: user.user_id,
          username: user.username,
          email: user.email,
          fullName: user.full_name,
          role: user.role,
          department: user.department,
          isActive: user.is_active === 1,
          lastLogin: user.last_login,
          createdAt: user.created_at,
          updatedAt: user.updated_at,
          preferences,
          activity: activityResults[0]
        }
      });
      
    } catch (error) {
      fastify.log.error('Get user error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve user'
      });
    }
  });
  
  // Create new user (admin only)
  fastify.post('/', {
    schema: {
      body: {
        type: 'object',
        required: ['username', 'email', 'password', 'fullName', 'role'],
        properties: {
          username: { type: 'string', minLength: 3, maxLength: 50 },
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 6, maxLength: 128 },
          fullName: { type: 'string', minLength: 1, maxLength: 255 },
          role: { type: 'string', enum: ['admin', 'user', 'viewer'] },
          department: { type: 'string', maxLength: 100 },
          isActive: { type: 'boolean', default: true }
        }
      }
    },
    preHandler: [fastify.authenticate, fastify.authorize(['admin'])]
  }, async (request, reply) => {
    try {
      const { username, email, password, fullName, role, department, isActive } = request.body;
      
      // Check if username or email already exists
      const [existingUser] = await fastify.db.query(
        'SELECT user_id FROM users WHERE username = ? OR email = ?',
        [username, email]
      );
      
      if (existingUser.length > 0) {
        return reply.code(400).send({
          success: false,
          message: 'Username or email already exists'
        });
      }
      
      // Hash password
      const passwordHash = await fastify.auth.hashPassword(password);
      
      // Create user
      const [result] = await fastify.db.query(`
        INSERT INTO users (
          username, email, password_hash, full_name, role, department, is_active, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
      `, [username, email, passwordHash, fullName, role, department || null, isActive ? 1 : 0]);
      
      const userId = result.insertId;
      
      fastify.log.info(`New user created: ${username} by admin ${request.user.username}`);
      
      return reply.code(201).send({
        success: true,
        message: 'User created successfully',
        data: { userId }
      });
      
    } catch (error) {
      fastify.log.error('Create user error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to create user'
      });
    }
  });
  
  // Update user (admin only)
  fastify.put('/:userId', {
    schema: {
      params: {
        type: 'object',
        required: ['userId'],
        properties: {
          userId: { type: 'integer', minimum: 1 }
        }
      },
      body: {
        type: 'object',
        properties: {
          email: { type: 'string', format: 'email' },
          fullName: { type: 'string', minLength: 1, maxLength: 255 },
          role: { type: 'string', enum: ['admin', 'user', 'viewer'] },
          department: { type: 'string', maxLength: 100 },
          isActive: { type: 'boolean' }
        }
      }
    },
    preHandler: [fastify.authenticate, fastify.authorize(['admin'])]
  }, async (request, reply) => {
    try {
      const { userId } = request.params;
      const { email, fullName, role, department, isActive } = request.body;
      
      // Check if user exists
      const [userCheck] = await fastify.db.query(
        'SELECT user_id FROM users WHERE user_id = ?',
        [userId]
      );
      
      if (userCheck.length === 0) {
        return reply.code(404).send({
          success: false,
          message: 'User not found'
        });
      }
      
      // Build update query
      const updateFields = [];
      const updateValues = [];
      
      if (email !== undefined) {
        // Check if email is taken by another user
        const [emailCheck] = await fastify.db.query(
          'SELECT user_id FROM users WHERE email = ? AND user_id != ?',
          [email, userId]
        );
        
        if (emailCheck.length > 0) {
          return reply.code(400).send({
            success: false,
            message: 'Email is already taken by another user'
          });
        }
        
        updateFields.push('email = ?');
        updateValues.push(email);
      }
      
      if (fullName !== undefined) {
        updateFields.push('full_name = ?');
        updateValues.push(fullName);
      }
      
      if (role !== undefined) {
        updateFields.push('role = ?');
        updateValues.push(role);
      }
      
      if (department !== undefined) {
        updateFields.push('department = ?');
        updateValues.push(department);
      }
      
      if (isActive !== undefined) {
        updateFields.push('is_active = ?');
        updateValues.push(isActive ? 1 : 0);
      }
      
      if (updateFields.length === 0) {
        return reply.code(400).send({
          success: false,
          message: 'No fields to update'
        });
      }
      
      updateFields.push('updated_at = NOW()');
      updateValues.push(userId);
      
      const updateQuery = `
        UPDATE users 
        SET ${updateFields.join(', ')}
        WHERE user_id = ?
      `;
      
      await fastify.db.query(updateQuery, updateValues);
      
      fastify.log.info(`User ${userId} updated by admin ${request.user.username}`);
      
      return reply.send({
        success: true,
        message: 'User updated successfully'
      });
      
    } catch (error) {
      fastify.log.error('Update user error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to update user'
      });
    }
  });
  
  // Delete user (admin only)
  fastify.delete('/:userId', {
    schema: {
      params: {
        type: 'object',
        required: ['userId'],
        properties: {
          userId: { type: 'integer', minimum: 1 }
        }
      }
    },
    preHandler: [fastify.authenticate, fastify.authorize(['admin'])]
  }, async (request, reply) => {
    try {
      const { userId } = request.params;
      const currentUserId = request.user.userId;
      
      // Prevent self-deletion
      if (userId === currentUserId) {
        return reply.code(400).send({
          success: false,
          message: 'Cannot delete your own account'
        });
      }
      
      // Check if user exists
      const [userCheck] = await fastify.db.query(
        'SELECT username FROM users WHERE user_id = ?',
        [userId]
      );
      
      if (userCheck.length === 0) {
        return reply.code(404).send({
          success: false,
          message: 'User not found'
        });
      }
      
      const username = userCheck[0].username;
      
      // Soft delete by deactivating the user
      await fastify.db.query(
        'UPDATE users SET is_active = 0, updated_at = NOW() WHERE user_id = ?',
        [userId]
      );
      
      // Invalidate all user sessions
      if (fastify.redis) {
        const sessionKeys = await fastify.redis.keys(`session:*:${userId}`);
        if (sessionKeys.length > 0) {
          await fastify.redis.del(...sessionKeys);
        }
      }
      
      fastify.log.info(`User ${username} (ID: ${userId}) deactivated by admin ${request.user.username}`);
      
      return reply.send({
        success: true,
        message: 'User deactivated successfully'
      });
      
    } catch (error) {
      fastify.log.error('Delete user error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to delete user'
      });
    }
  });
  
  // Reset user password (admin only)
  fastify.post('/:userId/reset-password', {
    schema: {
      params: {
        type: 'object',
        required: ['userId'],
        properties: {
          userId: { type: 'integer', minimum: 1 }
        }
      },
      body: {
        type: 'object',
        required: ['newPassword'],
        properties: {
          newPassword: { type: 'string', minLength: 6, maxLength: 128 }
        }
      }
    },
    preHandler: [fastify.authenticate, fastify.authorize(['admin'])]
  }, async (request, reply) => {
    try {
      const { userId } = request.params;
      const { newPassword } = request.body;
      
      // Check if user exists
      const [userCheck] = await fastify.db.query(
        'SELECT username FROM users WHERE user_id = ?',
        [userId]
      );
      
      if (userCheck.length === 0) {
        return reply.code(404).send({
          success: false,
          message: 'User not found'
        });
      }
      
      // Hash new password
      const passwordHash = await fastify.auth.hashPassword(newPassword);
      
      // Update password
      await fastify.db.query(
        'UPDATE users SET password_hash = ?, updated_at = NOW() WHERE user_id = ?',
        [passwordHash, userId]
      );
      
      // Invalidate all user sessions
      if (fastify.redis) {
        const sessionKeys = await fastify.redis.keys(`session:*:${userId}`);
        if (sessionKeys.length > 0) {
          await fastify.redis.del(...sessionKeys);
        }
      }
      
      const username = userCheck[0].username;
      fastify.log.info(`Password reset for user ${username} by admin ${request.user.username}`);
      
      return reply.send({
        success: true,
        message: 'Password reset successfully'
      });
      
    } catch (error) {
      fastify.log.error('Reset password error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to reset password'
      });
    }
  });
  
  // Get user activity logs (admin only)
  fastify.get('/:userId/activity', {
    schema: {
      params: {
        type: 'object',
        required: ['userId'],
        properties: {
          userId: { type: 'integer', minimum: 1 }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          days: { type: 'integer', minimum: 1, maximum: 365, default: 30 }
        }
      }
    },
    preHandler: [fastify.authenticate, fastify.authorize(['admin'])]
  }, async (request, reply) => {
    try {
      const { userId } = request.params;
      const { page, limit, days } = request.query;
      const offset = (page - 1) * limit;
      
      // Check if user exists
      const [userCheck] = await fastify.db.query(
        'SELECT username FROM users WHERE user_id = ?',
        [userId]
      );
      
      if (userCheck.length === 0) {
        return reply.code(404).send({
          success: false,
          message: 'User not found'
        });
      }
      
      // Get activity logs
      const [activityResults] = await fastify.db.query(`
        SELECT 
          query_id,
          sql_query,
          status,
          execution_time,
          rows_affected,
          error_message,
          created_at
        FROM query_logs 
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `, [userId, days, limit, offset]);
      
      // Get total count
      const [countResults] = await fastify.db.query(`
        SELECT COUNT(*) as total 
        FROM query_logs 
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      `, [userId, days]);
      
      const total = countResults[0].total;
      
      return reply.send({
        success: true,
        message: 'User activity retrieved successfully',
        data: {
          username: userCheck[0].username,
          activities: activityResults,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        }
      });
      
    } catch (error) {
      fastify.log.error('Get user activity error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve user activity'
      });
    }
  });
}