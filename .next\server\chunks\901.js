exports.id=901,exports.ids=[901],exports.modules={8413:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(37413),e=c(93082);function f({children:a}){return(0,d.jsx)(e.LayoutJSX,{children:a})}c(35692)},48221:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(37413);function e(){return(0,d.jsxs)("div",{className:"h-full lg:px-6 pt-4",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-2 pt-2 px-4 lg:px-0 max-w-[90rem] mx-auto w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4",children:[(0,d.jsx)("div",{children:(0,d.jsx)("div",{className:"h-8 md:h-10 w-80 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"})}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-3",children:[(0,d.jsx)("div",{className:"w-full sm:w-44 lg:w-52 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"}),(0,d.jsx)("div",{className:"w-full sm:w-44 lg:w-52 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"})]})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 mt-4 md:mt-6",children:Array.from({length:4}).map((a,b)=>(0,d.jsx)("div",{className:"border-none shadow-sm bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 rounded-lg",children:(0,d.jsx)("div",{className:"p-3 md:p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 md:gap-3 min-w-0 flex-1",children:[(0,d.jsx)("div",{className:"w-10 h-10 md:w-12 md:h-12 bg-gray-200 dark:bg-gray-600 rounded-xl flex-shrink-0 animate-pulse"}),(0,d.jsxs)("div",{className:"min-w-0 flex-1 space-y-2",children:[(0,d.jsx)("div",{className:"h-3 w-20 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-5 w-24 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[(0,d.jsx)("div",{className:"h-4 w-4 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-3 w-8 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"})]})]})})},b))})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-6 pt-6 px-4 lg:px-0 max-w-[90rem] mx-auto w-full",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-6 xl:grid-cols-12 gap-6",children:[(0,d.jsx)("div",{className:"border-none shadow-sm bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900 dark:to-purple-900 lg:col-span-6 xl:col-span-2 rounded-lg",children:(0,d.jsx)("div",{className:"p-4 md:p-6",children:(0,d.jsxs)("div",{className:"text-center space-y-3 md:space-y-4",children:[(0,d.jsx)("div",{className:"mx-auto w-16 h-16 md:w-20 md:h-20 bg-gray-200 dark:bg-gray-600 rounded-full animate-pulse"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-6 md:h-8 w-16 mx-auto bg-gray-200 dark:bg-gray-600 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-3 md:h-4 w-32 mx-auto bg-gray-200 dark:bg-gray-600 rounded animate-pulse"})]}),(0,d.jsx)("div",{className:"h-2 w-full bg-gray-200 dark:bg-gray-600 rounded-full animate-pulse"}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-2 md:gap-4 pt-2",children:[(0,d.jsxs)("div",{className:"text-center space-y-1",children:[(0,d.jsx)("div",{className:"h-3 w-12 mx-auto bg-gray-200 dark:bg-gray-600 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-4 w-8 mx-auto bg-gray-200 dark:bg-gray-600 rounded animate-pulse"})]}),(0,d.jsxs)("div",{className:"text-center space-y-1",children:[(0,d.jsx)("div",{className:"h-3 w-16 mx-auto bg-gray-200 dark:bg-gray-600 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-4 w-12 mx-auto bg-gray-200 dark:bg-gray-600 rounded animate-pulse"})]})]})]})})}),(0,d.jsxs)("div",{className:"border-none shadow-sm bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 lg:col-span-6 xl:col-span-4 rounded-lg",children:[(0,d.jsx)("div",{className:"pb-3 md:pb-4 px-4 md:px-6 pt-4 md:pt-6",children:(0,d.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,d.jsx)("div",{className:"h-5 md:h-6 w-40 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-4 w-20 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"})]})}),(0,d.jsx)("div",{className:"pt-0 px-4 md:px-6 pb-4 md:pb-6",children:(0,d.jsx)("div",{className:"space-y-2 md:space-y-3",children:Array.from({length:4}).map((a,b)=>(0,d.jsxs)("div",{className:"p-2 md:p-3 bg-slate-100 dark:bg-slate-600 rounded-lg space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("div",{className:"h-4 w-40 bg-gray-200 dark:bg-gray-500 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-6 w-12 bg-gray-200 dark:bg-gray-500 rounded-full animate-pulse"})]}),(0,d.jsx)("div",{className:"h-3 w-32 bg-gray-200 dark:bg-gray-500 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-2 w-full bg-gray-200 dark:bg-gray-500 rounded-full animate-pulse"})]},b))})})]}),(0,d.jsxs)("div",{className:"border-none shadow-sm bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 lg:col-span-12 xl:col-span-6 rounded-lg",children:[(0,d.jsx)("div",{className:"pb-2 px-4 md:px-6 pt-4 md:pt-6",children:(0,d.jsxs)("div",{className:"flex flex-col gap-2 w-full",children:[(0,d.jsx)("div",{className:"h-5 md:h-6 w-48 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2",children:[(0,d.jsx)("div",{className:"h-3 md:h-4 w-40 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-6 w-20 bg-gray-200 dark:bg-gray-600 rounded-full animate-pulse"})]})]})}),(0,d.jsx)("div",{className:"pt-0 px-2 pb-2",children:(0,d.jsx)("div",{className:"h-[200px] md:h-[280px] w-full flex items-center justify-center",children:(0,d.jsx)("div",{className:"h-full w-full bg-gray-200 dark:bg-gray-600 rounded-lg animate-pulse"})})})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"border-none shadow-sm bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 rounded-lg",children:[(0,d.jsx)("div",{className:"pb-3 md:pb-4 px-4 md:px-6 pt-4 md:pt-6",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:"h-4 w-4 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-5 md:h-6 w-32 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"})]})}),(0,d.jsxs)("div",{className:"pt-0 px-4 md:px-6 pb-4 md:pb-6",children:[(0,d.jsx)("div",{className:"space-y-3 md:space-y-4",children:Array.from({length:3}).map((a,b)=>(0,d.jsxs)("div",{className:"flex gap-3",children:[(0,d.jsx)("div",{className:"h-4 w-4 bg-gray-200 dark:bg-gray-600 rounded flex-shrink-0 mt-1 animate-pulse"}),(0,d.jsxs)("div",{className:"flex-1 min-w-0 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 w-48 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-3 w-24 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("div",{className:"h-3 w-20 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-3 w-16 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"})]})]})]},b))}),(0,d.jsx)("div",{className:"my-4",children:(0,d.jsx)("div",{className:"h-px w-full bg-gray-200 dark:bg-gray-600 rounded animate-pulse"})}),(0,d.jsx)("div",{className:"h-8 w-full bg-gray-200 dark:bg-gray-600 rounded-lg animate-pulse"})]})]}),(0,d.jsxs)("div",{className:"border-none shadow-sm bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 rounded-lg",children:[(0,d.jsx)("div",{className:"pb-3 md:pb-4 px-4 md:px-6 pt-4 md:pt-6",children:(0,d.jsx)("div",{className:"h-5 md:h-6 w-24 bg-gray-200 dark:bg-gray-600 rounded animate-pulse"})}),(0,d.jsx)("div",{className:"pt-0 space-y-3 px-4 md:px-6 pb-4 md:pb-6",children:Array.from({length:3}).map((a,b)=>(0,d.jsx)("div",{className:"h-8 w-full bg-gray-200 dark:bg-gray-600 rounded-lg animate-pulse"},b))})]})]})]})]})}},55310:(a,b,c)=>{Promise.resolve().then(c.bind(c,93082))},78335:()=>{},93082:(a,b,c)=>{"use strict";c.d(b,{LayoutJSX:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call LayoutJSX() from the server but LayoutJSX is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\components\\layout\\layoutJSX.jsx","LayoutJSX")},95062:(a,b,c)=>{Promise.resolve().then(c.bind(c,57420))},96487:()=>{}};