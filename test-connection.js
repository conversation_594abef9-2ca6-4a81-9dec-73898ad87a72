import dotenv from 'dotenv';
import mysql from 'mysql2/promise';
import { createClient } from 'redis';

// Load environment variables
dotenv.config();

async function testConnections() {
  console.log('🔍 Testing Sintesa Backend Configuration...');
  console.log('=' .repeat(50));
  
  // Test environment variables
  console.log('📋 Environment Configuration:');
  console.log(`   Server Port: ${process.env.PORT || '88'}`);
  console.log(`   Database Host: ${process.env.DB_HOST || 'localhost'}`);
  console.log(`   Database Port: ${process.env.DB_PORT || '3399'}`);
  console.log(`   Database Name: ${process.env.DB_NAME || 'sintesa'}`);
  console.log(`   Database User: ${process.env.DB_USER || 'root'}`);
  console.log(`   Redis Host: ${process.env.REDIS_HOST || 'localhost'}`);
  console.log(`   Redis Port: ${process.env.REDIS_PORT || '6379'}`);
  console.log('');

  // Test MySQL connection
  console.log('🗄️  Testing MySQL Connection...');
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3399,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'pdpsipa2025',
      database: process.env.DB_NAME || 'sintesa'
    });
    
    await connection.execute('SELECT 1 as test');
    await connection.end();
    console.log('   ✅ MySQL connection successful!');
  } catch (error) {
    console.log('   ❌ MySQL connection failed:', error.message);
    console.log('   💡 Make sure MySQL is running and credentials are correct');
  }

  // Test Redis connection
  console.log('');
  console.log('🔴 Testing Redis Connection...');
  try {
    const redis = createClient({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || undefined
    });
    
    await redis.connect();
    await redis.ping();
    await redis.disconnect();
    console.log('   ✅ Redis connection successful!');
  } catch (error) {
    console.log('   ❌ Redis connection failed:', error.message);
    console.log('   💡 Make sure Redis is running');
  }

  console.log('');
  console.log('🌐 Frontend Integration:');
  console.log('   Your frontend is configured to connect to: http://localhost:88');
  console.log('   Backend will run on: http://localhost:88');
  console.log('   ✅ Port configuration matches!');
  
  console.log('');
  console.log('🚀 Next Steps:');
  console.log('   1. Start MySQL server');
  console.log('   2. Start Redis server (optional)');
  console.log('   3. Run: npm run dev');
  console.log('   4. Test frontend connection');
  console.log('');
}

testConnections().catch(console.error);