(()=>{var a={};a.id=5355,a.ids=[5355],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:a=>{"use strict";a.exports=require("module")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31105:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\sintesaNEXT2\\\\src\\\\app\\\\(dashboard)\\\\inquiry-data\\\\tematik\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\inquiry-data\\tematik\\page.jsx","default")},33873:a=>{"use strict";a.exports=require("path")},34606:(a,b,c)=>{Promise.resolve().then(c.bind(c,31105))},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},59e3:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(4765),e=c.n(d);let f="mebe23",g=(a,b=f)=>{let c=e().AES.encrypt(JSON.stringify(a),b).toString();return e().enc.Base64.stringify(e().enc.Utf8.parse(c))}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81410:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["inquiry-data",{children:["tematik",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,31105)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\inquiry-data\\tematik\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,8413)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\layout.jsx"],loading:[()=>Promise.resolve().then(c.bind(c,48221)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\loading.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,53361)),"D:\\sintesaNEXT2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\sintesaNEXT2\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["D:\\sintesaNEXT2\\src\\app\\(dashboard)\\inquiry-data\\tematik\\page.jsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/inquiry-data/tematik/page",pathname:"/inquiry-data/tematik",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/inquiry-data/tematik/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},93509:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>b4});var d=c(60687),e=c(67401),f=c(43210),g=c.n(f),h=c(14221),i=c(21875),j=c(56093),k=c(55110),l=c(49995),m=c(75378),n=c(27580),o=c(11860),p=c(13964),q=c(70615);let r=({isOpen:a,onClose:b,query:c,title:e})=>{let[g,h]=(0,f.useState)(!1),r=async()=>{if(c)try{await navigator.clipboard.writeText(c),h(!0),setTimeout(()=>h(!1),1500)}catch(a){h(!1)}};return(0,d.jsx)(i.Y,{isOpen:a,onClose:b,size:"3xl",scrollBehavior:"inside",backdrop:"blur",hideCloseButton:!0,classNames:{header:"bg-gradient-to-r from-gray-200 to-zinc-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(j.g,{children:[(0,d.jsx)(k.c,{className:"flex justify-between items-center m-6",children:(0,d.jsx)("div",{className:"text-lg font-semibold",children:e||"SQL Preview"})}),(0,d.jsx)(l.h,{children:(0,d.jsx)("div",{className:"bg-gray-100 p-8 rounded-xl overflow-x-auto max-h-[60vh]",children:(0,d.jsx)("pre",{className:"whitespace-pre-wrap break-words text-sm font-mono text-gray-800 text-center",children:c&&c.replace(/\s+/g," ").trim()})})}),(0,d.jsxs)(m.q,{className:"flex justify-between",children:[(0,d.jsx)(n.T,{color:"danger",variant:"light",onPress:b,startContent:(0,d.jsx)(o.A,{size:16}),children:"Tutup"}),(0,d.jsx)(n.T,{color:"default",variant:"ghost",onPress:r,startContent:g?(0,d.jsx)(p.A,{size:16}):(0,d.jsx)(q.A,{size:16}),children:g?"Tersalin!":"Salin ke Clipboard"})]})]})})};var s=c(41871),t=c(44301),u=c(21988),v=c(69087),w=c(61611),x=c(8819),y=c(40611),z=c(30485),A=c(62085);let B=({isOpen:a,onClose:b,query:c,thang:e,queryType:g="INQUIRY"})=>{let[p,q]=(0,f.useState)(!1),{axiosJWT:r,token:B,name:C}=(0,f.useContext)(h.A),{showToast:D}=(0,y.d)(),E=z.Ik().shape({queryName:z.Yj().required("Nama Query harus diisi"),queryType:z.Yj().required("Tipe Query harus dipilih")}),F={queryName:"",queryType:g,thang:e||new Date().getFullYear().toString()},G=async(a,{resetForm:d})=>{q(!0);try{let e={tipe:a.queryType,nama:a.queryName,name:C,query:c,thang:a.thang};await r.post("http://localhost:88/user/simpanquery",e,{headers:{Authorization:`Bearer ${B}`,"Content-Type":"application/json"}}),D("Query berhasil disimpan","success"),d(),b()}catch(a){D(a.response?.data?.error||"Gagal menyimpan query","error")}finally{q(!1)}};return(0,d.jsx)(i.Y,{isOpen:a,onClose:b,size:"3xl",scrollBehavior:"inside",backdrop:"blur",hideCloseButton:!0,classNames:{header:"bg-gradient-to-r from-yellow-200 to-amber-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(j.g,{children:[(0,d.jsx)(k.c,{className:"flex justify-between items-center m-6",children:(0,d.jsxs)("div",{className:"text-lg font-semibold flex items-center",children:[(0,d.jsx)(w.A,{className:"mr-2 text-blue-600",size:20}),"Simpan Query"]})}),(0,d.jsx)(A.l1,{initialValues:F,validationSchema:E,onSubmit:G,children:({values:a,errors:c,touched:e,handleChange:f,isSubmitting:g})=>(0,d.jsxs)(A.lV,{children:[(0,d.jsx)(l.h,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tahun Anggaran"}),(0,d.jsx)(s.r,{name:"thang",value:a.thang,onChange:f,disabled:!0,className:"bg-gray-100"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tipe Query"}),(0,d.jsxs)(t.d,{name:"queryType",value:a.queryType,onChange:f,disabled:p,children:[(0,d.jsx)(u.y,{value:"INQUIRY",children:"Inquiry"},"INQUIRY"),(0,d.jsx)(u.y,{value:"BELANJA",children:"Belanja"},"BELANJA"),(0,d.jsx)(u.y,{value:"PENERIMAAN",children:"Penerimaan"},"PENERIMAAN"),(0,d.jsx)(u.y,{value:"BLOKIR",children:"Blokir"},"BLOKIR")]}),c.queryType&&e.queryType&&(0,d.jsx)("div",{className:"text-red-500 text-xs mt-1",children:c.queryType})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nama Query"}),(0,d.jsx)(s.r,{name:"queryName",value:a.queryName,onChange:f,placeholder:"Masukkan nama untuk query ini...",disabled:p}),c.queryName&&e.queryName&&(0,d.jsx)("div",{className:"text-red-500 text-xs mt-1",children:c.queryName})]}),(0,d.jsx)("div",{className:"text-xs text-gray-500 italic",children:"*) Query yang tersimpan dapat diakses di menu Profile, tab Query Data"})]})}),(0,d.jsxs)(m.q,{className:"flex justify-between",children:[(0,d.jsx)(n.T,{color:"danger",variant:"light",onPress:b,disabled:p,startContent:(0,d.jsx)(o.A,{size:16}),children:"Tutup"}),(0,d.jsx)(n.T,{color:"warning",variant:"ghost",type:"submit",disabled:p,className:"w-[160px]",startContent:p?(0,d.jsx)(v.o,{size:"sm"}):(0,d.jsx)(x.A,{size:16}),children:p?"Menyimpan...":"Simpan Query"})]})]})})]})})};var C=c(17985),D=c(84292),E=c(53823),F=c(88977),G=c(14229),H=c(37911),I=c(10022),J=c(5066),K=c(16023);async function L(a,b="data.xlsx"){if(!a||!a.length)return;let d=await c.e(3103).then(c.bind(c,33103)),e=d.utils.json_to_sheet(a),f=d.utils.book_new();d.utils.book_append_sheet(f,e,"Sheet1");let g=new Blob([d.write(f,{bookType:"xlsx",type:"array"})],{type:"application/octet-stream"}),h=URL.createObjectURL(g),i=document.createElement("a");i.setAttribute("href",h),i.setAttribute("download",b),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(h)}async function M(a,b="data.pdf"){if(!a||!a.length)return;let d=(await c.e(4403).then(c.bind(c,4403))).default,e=(await c.e(8848).then(c.bind(c,88848))).default,f=new d,g=Object.keys(a[0]),h=a.map(a=>g.map(b=>a[b]));e(f,{head:[g],body:h,styles:{fontSize:8},headStyles:{fillColor:[41,128,185]}}),f.save(b)}let N=({showModalPDF:a,setShowModalPDF:b,selectedFormat:c,setSelectedFormat:e,fetchExportData:f,filename:g="data_export",loading:h})=>{let p=async()=>{try{let a=await f();if(!a||0===a.length)return;switch(c){case"pdf":await M(a,`${g}.pdf`);break;case"excel":await L(a,`${g}.xlsx`);break;case"json":!function(a,b="data.json"){if(!a||!a.length)return;let c=new Blob([JSON.stringify(a,null,2)],{type:"application/json"}),d=URL.createObjectURL(c),e=document.createElement("a");e.setAttribute("href",d),e.setAttribute("download",b),e.style.visibility="hidden",document.body.appendChild(e),e.click(),document.body.removeChild(e),URL.revokeObjectURL(d)}(a,`${g}.json`);break;case"text":!function(a,b="data.txt"){if(!a||!a.length)return;let c=new Blob([JSON.stringify(a,null,2)],{type:"text/plain"}),d=URL.createObjectURL(c),e=document.createElement("a");e.setAttribute("href",d),e.setAttribute("download",b),e.style.visibility="hidden",document.body.appendChild(e),e.click(),document.body.removeChild(e),URL.revokeObjectURL(d)}(a,`${g}.txt`)}b(!1)}catch(a){console.error("Export failed",a)}};return(0,d.jsx)(i.Y,{isOpen:a,onClose:()=>b(!1),size:"3xl",scrollBehavior:"inside",backdrop:"blur",hideCloseButton:!0,classNames:{header:"bg-gradient-to-r from-green-200 to-emerald-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(j.g,{children:[(0,d.jsx)(k.c,{className:"flex justify-between items-center m-6",children:(0,d.jsxs)("div",{className:"text-lg font-semibold flex items-center",children:[(0,d.jsx)(F.A,{className:"mr-2 text-success",size:20}),"Kirim Data ke WhatsApp"]})}),(0,d.jsx)(l.h,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Pilih format file untuk dikirim:"}),(0,d.jsxs)(C.U,{value:c,onValueChange:e,orientation:"horizontal",className:"flex flex-row gap-8 justify-center h-16 items-center",classNames:{wrapper:"gap-8 justify-center h-16 items-center"},children:[(0,d.jsx)(D.O,{value:"pdf",color:"danger",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(G.A,{className:"mr-2 text-red-600",size:18}),(0,d.jsx)("span",{children:"PDF"})]})}),(0,d.jsx)(D.O,{value:"excel",color:"success",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(H.A,{className:"mr-2 text-green-600",size:18}),(0,d.jsx)("span",{children:"Excel (.xlsx)"})]})}),(0,d.jsx)(D.O,{value:"json",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(I.A,{className:"mr-2 text-blue-600",size:18}),(0,d.jsx)("span",{children:"JSON"})]})}),(0,d.jsx)(D.O,{value:"text",color:"default",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(J.A,{className:"mr-2 text-gray-600",size:18}),(0,d.jsx)("span",{children:"Text (.txt)"})]})})]}),(0,d.jsx)(E.y,{className:"my-2"}),(0,d.jsx)("div",{className:"text-xs text-gray-500",children:(0,d.jsxs)("p",{children:["Nama file: ",g,".",c]})})]})}),(0,d.jsxs)(m.q,{className:"flex justify-between",children:[(0,d.jsx)(n.T,{color:"danger",variant:"light",onPress:()=>b(!1),disabled:h,startContent:(0,d.jsx)(o.A,{size:16}),children:"Tutup"}),(0,d.jsx)(n.T,{color:"success",variant:"ghost",onPress:p,disabled:h,className:"w-[160px]",startContent:h?(0,d.jsx)(v.o,{size:"sm"}):(0,d.jsx)(K.A,{size:16}),children:h?"Mengirim...":"Kirim"})]})]})})};var O=c(7192),P=c(51034),Q=c.n(P),R=c(99270),S=c(98564),T=c(85015),U=c(55327),V=c(80273),W=c(92241),X=c(98e3),Y=c(76142),Z=c(18445),$=c(42817),_=c(59e3);let aa=({isOpen:a,onClose:b,sql:c,from:e,thang:p,pembulatan:q})=>{let{axiosJWT:r,token:t,statusLogin:u}=(0,f.useContext)(h.A);(0,f.useEffect)(()=>{},[q]);let[w,x]=(0,f.useState)(!1),[y,z]=(0,f.useState)(""),[A,B]=(0,f.useState)(null),[C,D]=(0,f.useState)(!1),[E,F]=(0,f.useState)(0),[G,H]=(0,f.useState)(null),[I,J]=(0,f.useState)(null),[K,L]=(0,f.useState)(!1),[M,N]=(0,f.useState)(!0),[P,aa]=(0,f.useState)(!1),[ab,ac]=(0,f.useState)(null),ad=(0,f.useRef)(""),ae=(0,f.useRef)({column:null,direction:null}),[af,ag]=(0,f.useState)({column:null,direction:null}),[ah,ai]=(0,f.useState)([]),[aj,ak]=(0,f.useState)(null),[al,am]=(0,f.useState)(!1),an=(0,f.useRef)(1),ao=async(a=1,b=!1)=>{if(!u||!c)return;let d=1===a;d&&!b?(x(!0),N(!0),ai([]),an.current=1):b&&(N(!1),aa(!0)),am(!0),J(null);let e=performance.now();try{let f=c;if(ae.current.column&&ae.current.direction){let a=ae.current.column,b="ascending"===ae.current.direction?"ASC":"DESC";if(/\bORDER\s+BY\b/i.test(c))f=c.replace(/ORDER\s+BY\s+[^;]*/i,`ORDER BY ${a} ${b}`);else{let d=c.match(/(\s+LIMIT\s+)/i);f=d?c.replace(d[0],` ORDER BY ${a} ${b}${d[0]}`):`${c} ORDER BY ${a} ${b}`}}if(ad.current&&ad.current.trim()){let a=ad.current.trim().replace(/'/g,"''"),b=/\bWHERE\b/i.test(c),d=c.match(/SELECT\s+(.*?)\s+FROM/i);if(d){let e=d[1],g=[];if("*"===e.trim());else if((g=e.split(",").map(a=>{let b=a.trim().split(/\s+AS\s+/i)[0].trim();return b=b.replace(/["`\[\]]/g,"")}).filter(a=>{let b=a.trim();return!(b.includes("(")||b.includes("*")||b.match(/^(COUNT|SUM|AVG|MAX|MIN|DISTINCT|CASE|IF|CONCAT|SUBSTRING|DATE|YEAR|MONTH|DAY)/i)||b.match(/^[0-9]+$/)||b.match(/^['"`].*['"`]$/)||b.match(/^NULL$/i)||0===b.length||b.includes("+")||b.includes("-")||b.includes("*")||b.includes("/")||b.includes("=")||b.includes("<")||b.includes(">"))&&b.match(/^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$/)})).length>0){let d=g.filter(a=>{let b=a.toUpperCase();return"PAGU"!==b&&"PAGU_APBN"!==b&&"PAGU_DIPA"!==b&&"REALISASI"!==b&&"BLOKIR"!==b});if(d.length>0){let e=d.map(b=>`(LOWER(CAST(${b} AS CHAR)) LIKE LOWER('%${a}%'))`).join(" OR "),g=`(${e})`;if(b){let a=c.match(/(\s+(GROUP\s+BY|ORDER\s+BY|HAVING|LIMIT)\s+)/i);f=a?c.replace(a[0],` AND ${g}${a[0]}`):`${c} AND ${g}`}else{let a=c.match(/(\s+(GROUP\s+BY|ORDER\s+BY|HAVING|LIMIT)\s+)/i);f=a?c.replace(a[0],` WHERE ${g}${a[0]}`):`${c} WHERE ${g}`}}}}}let g=encodeURIComponent(f),h=(0,_.A)(g),i=await r.post("http://localhost:88/next/inquiry",{sql:h,page:a},{timeout:3e4}),j=performance.now();if(B((j-e)/1e3),i.data){let c=i.data.data||[],e=i.data.total||0,f=i.data.totalPages||0,g=i.data.grandTotals||null;F(e),d&&g&&H(g);let h=!1;if(f>0)h=a<f;else if(e>0){let b=Math.ceil(e/100);h=a<b}else h=c.length>=100;ak(h?(a+1).toString():null),an.current=a,b?ai(a=>[...a,...c]):ai(c)}else F(0),ai([]),ak(null)}catch(e){let{status:a,data:c}=e.response||{},d=c&&c.error||e.message||"Terjadi Permasalahan Koneksi atau Server Backend";J(d),(0,O.t)(a,d),F(0),b||(ai([]),ak(null))}finally{am(!1),d&&!b?x(!1):b&&aa(!1)}},[ap,aq]=(0,$.X)({hasMore:!!aj,isEnabled:a&&u,shouldUseLoader:!0,onLoadMore:()=>{aj&&!al&&ao(parseInt(aj),!0)}});(0,f.useEffect)(()=>{if(a&&u&&c){let a=setTimeout(()=>{z(""),ad.current="",ag({column:null,direction:null}),ae.current={column:null,direction:null},J(null),N(!0),ao(1,!1)},100);return()=>{clearTimeout(a)}}},[a,u,c]),(0,f.useEffect)(()=>{!a&&(J(null),z(""),ad.current="",F(0),B(null),N(!0),aa(!1),ag({column:null,direction:null}),ae.current={column:null,direction:null},ak(null),ab&&(clearTimeout(ab),ac(null)))},[a,ab]),(0,f.useEffect)(()=>{w||al||N(!1)},[w,al]);let ar=a=>{let b=Number(a);return isNaN(b)?"0":"1000000000000"===q?new Intl.NumberFormat("de-DE",{minimumFractionDigits:0,maximumFractionDigits:2}).format(b):new Intl.NumberFormat("de-DE",{minimumFractionDigits:0,maximumFractionDigits:0}).format(b)},as={kddept:a=>String(a),kdsatker:a=>String(a)},at=(0,f.useMemo)(()=>0===ah.length?[]:Object.keys(ah[0]),[ah]),au=(0,f.useMemo)(()=>0===ah.length?{}:at.reduce((a,b)=>(["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(b.toUpperCase())&&ah.reduce((a,c)=>{let d=c[b];return isNaN(Number(d))||""===d||"boolean"==typeof d?a:a+1},0)/ah.length>.7&&(a[b]=!0),a),{}),[ah,at]);g().useEffect(()=>{},[]);let av=at.length>0;return(0,d.jsx)(i.Y,{backdrop:"blur",isOpen:a,onClose:b,size:C?"full":"6xl",scrollBehavior:"inside",hideCloseButton:!0,className:C?"max-h-full":"h-[80vh] w-[80vw]",classNames:{header:"bg-gradient-to-r from-sky-200 to-cyan-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(j.g,{children:[(0,d.jsxs)(k.c,{className:"flex justify-between items-center m-6",children:[(0,d.jsx)("div",{className:"text-lg font-semibold",children:"Hasil Inquiry"}),(0,d.jsx)("div",{className:"flex items-center space-x-2",children:(0,d.jsx)(S.A,{isSelected:C,onValueChange:D,onChange:a=>{D(a.target.checked)},size:"sm",children:(0,d.jsx)("span",{className:"text-sm",children:"Layar Penuh"})})})]}),(0,d.jsxs)(l.h,{className:"flex flex-col h-full min-h-0 p-0",children:[(0,d.jsx)("div",{className:"flex justify-end items-center px-6",children:(0,d.jsx)("div",{className:"flex space-x-2",children:(0,d.jsx)(s.r,{placeholder:"Ketik untuk mencari Kode atau Nama",value:y,onChange:a=>{let b=a.target.value;if(z(b),ad.current=b,J(null),ab&&clearTimeout(ab),""===b){ao(1,!1);let a=aq.current;a&&a.scrollTo({top:0,behavior:"smooth"}),ac(null);return}let c=setTimeout(()=>{ao(1,!1);let a=aq.current;a&&a.scrollTo({top:0,behavior:"smooth"}),ac(null)},300);ac(c)},startContent:(0,d.jsx)(R.A,{size:16}),size:"md",className:"w-96"})})}),I?(0,d.jsxs)("div",{className:"text-center p-8 text-red-500",children:[(0,d.jsxs)("p",{children:["Error loading data: ",I]}),(0,d.jsxs)("div",{className:"mt-2 space-x-2",children:[(0,d.jsx)(n.T,{color:"primary",size:"sm",onClick:()=>{J(null),L(!0),setTimeout(()=>{ao(1,!1),L(!1)},100)},isLoading:K||w,children:"Retry"}),(0,d.jsx)(n.T,{color:"default",size:"sm",variant:"bordered",onClick:b,children:"Close"})]})]}):0!==ah.length||w||al?0===at.length?(0,d.jsx)("div",{className:"flex items-center justify-center h-full py-8",children:w||al?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(v.o,{color:"primary",size:"lg",variant:"simple"}),(0,d.jsx)("span",{className:"text-lg text-gray-600 ml-6 flex gap-0.5",children:"Memproses query data...".split("").map((a,b)=>(0,d.jsx)("span",{style:{display:"inline-block",animation:"wave 1.2s infinite",animationDelay:`${.08*b}s`},children:" "===a?"\xa0":a},b))}),(0,d.jsx)("style",{children:`
                    @keyframes wave {
                      0%, 60%, 100% { transform: translateY(0); }
                      30% { transform: translateY(-8px); }
                    }
                  `})]}):(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"No data available"})}):(0,d.jsx)("div",{className:"h-full overflow-auto px-6 py-1",ref:aq,children:(0,d.jsx)(T.Z,{className:"h-full p-4 shadow-none border-2",children:(0,d.jsxs)(U.j,{"aria-label":"Inquiry results table",removeWrapper:!0,sortDescriptor:af,onSortChange:a=>{ag(a),ae.current=a,ao(1,!1);let b=aq.current;b&&b.scrollTo({top:0,behavior:"smooth"})},classNames:{base:"h-full overflow-auto",table:"h-full",th:"position: sticky top-0 z-20",wrapper:"h-full w-full "},children:[(0,d.jsxs)(V.X,{children:[av&&(0,d.jsx)(W.e,{className:"text-center w-12 uppercase",children:"No"},"index"),at.map(a=>{au[a];let b=["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(a.toUpperCase()),c={};return["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(a.toUpperCase())&&(c={width:"160px",minWidth:"160px",maxWidth:"260px"}),(0,d.jsx)(W.e,{allowsSorting:b,className:"text-center uppercase",style:c,children:a},a)})]}),(0,d.jsxs)(X.E,{isLoading:!1,emptyContent:"No data to display",children:[0===ah.length?(0,d.jsx)(Y.s,{children:(0,d.jsx)(Z.w,{colSpan:at.length+ +!!av,className:"text-center",children:y?`Tidak ada hasil untuk pencarian: "${y}"`:"No data available"})}):ah.map((a,b)=>(0,d.jsxs)(Y.s,{children:[av&&(0,d.jsx)(Z.w,{className:"text-center",children:b+1}),at.map(b=>(0,d.jsx)(Z.w,{className:au[b]?"text-right":"text-center",children:as[b]?as[b](a[b]):au[b]&&!isNaN(Number(a[b]))?ar(a[b]):a[b]},b))]},`${a.id||b}`)),ah.length>0&&(0,d.jsx)(Y.s,{children:(0,d.jsx)(Z.w,{colSpan:at.length+ +!!av,className:`text-center ${P?"py-4":"py-2"}`,style:{minHeight:"40px"},children:(0,d.jsx)("div",{ref:ap,className:"w-full",children:P?(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,d.jsx)(v.o,{color:"primary",size:"md",variant:"simple"}),(0,d.jsx)("span",{className:"text-sm text-default-600",children:"Memuat data selanjutnya..."})]}):(0,d.jsx)("div",{className:"h-1 w-full flex items-center justify-center",children:!1})})})}),ah.length>0&&(0,d.jsxs)(Y.s,{className:"sticky bottom-0 bg-default-100 z-20 rounded-lg",children:[av&&(0,d.jsx)(Z.w,{className:"text-center font-medium text-foreground-600 bg-default-100 first:rounded-l-lg"}),at.map((a,b)=>{let c=au[a],e=a.toUpperCase(),f=0;c&&["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(e)&&(f=ah.reduce((b,c)=>{let d=Number(c[a]);return isNaN(d)?b:b+d},0));let g=at.findLastIndex(a=>!au[a]);return(0,d.jsx)(Z.w,{className:`${c?"text-right":"text-center"} font-medium text-foreground-600 bg-default-100 uppercase ${0===b&&!av?"first:rounded-l-lg":""} ${b===at.length-1?"last:rounded-r-lg":""}`,children:c&&["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(e)?ar(f):b===g?"GRAND TOTAL":""},a)})]})]})]})})}):(0,d.jsx)("div",{className:"text-center p-8 text-gray-500",children:y?(0,d.jsxs)("div",{children:[(0,d.jsxs)("p",{children:['Tidak ada hasil ditemukan untuk pencarian: "',y,'"']}),(0,d.jsx)("p",{className:"text-sm mt-2",children:"Coba gunakan kata kunci yang berbeda"})]}):(0,d.jsxs)("div",{children:["No data available",!1]})})]}),(0,d.jsx)(m.q,{children:(0,d.jsxs)("div",{className:"flex justify-between items-center gap-8 w-full",children:[(0,d.jsx)("div",{className:"flex text-sm",children:E>0?(0,d.jsxs)(d.Fragment,{children:["Total Baris: ",Q()(E).format("0,0"),", Ditampilkan:"," ",ah.length," item",y&&` (hasil pencarian: "${y}")`]}):y?`Tidak ada hasil untuk pencarian: "${y}"`:"No data"}),(0,d.jsx)(n.T,{color:"danger",variant:"ghost",className:"w-[120px]",onPress:b,startContent:(0,d.jsx)(o.A,{size:16}),children:"Tutup"})]})})]})})};var ab=c(80505);let ac=({id:a,checked:b,onChange:c,label:e,size:f="sm",disabled:g=!1})=>(0,d.jsxs)("div",{className:`flex items-center gap-3 px-2 py-1 rounded-2xl shadow-sm transition-all duration-300 group ${g?"opacity-50":""}`,children:[(0,d.jsx)(ab.Z,{id:a,isSelected:b,onValueChange:g?void 0:c,size:f,isDisabled:g,"aria-label":e,"aria-labelledby":`${a}-label`,classNames:{wrapper:"group-data-[selected=true]:bg-gradient-to-r group-data-[selected=true]:from-purple-400 group-data-[selected=true]:to-blue-400",thumb:"group-data-[selected=true]:bg-white shadow-lg"}}),(0,d.jsx)("label",{id:`${a}-label`,htmlFor:a,className:`text-sm font-medium transition-colors duration-200 flex-1 ${g?"text-gray-400 cursor-not-allowed":"text-gray-700 group-hover:text-purple-600 cursor-pointer"}`,children:e})]});var ad=c(77611),ae=c(71018),af=c(79410),ag=c(96882);let ah=({inquiryState:a,status:b})=>{let{dept:c,setDept:e,deptradio:f,setDeptradio:g,deptkondisi:h,setDeptkondisi:i,katadept:j,setKatadept:k}=a||{},l=j&&""!==j.trim(),m=h&&""!==h.trim(),o=c&&"XXX"!==c&&"000"!==c&&"XX"!==c,p=l||m,q=l||o,r=m||o;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(af.A,{size:20,className:"ml-4 text-secondary"}),"Kementerian"]})," ",(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[" ",(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${p?"text-gray-400":"text-gray-700"}`,children:"Pilih Kementerian"}),o&&!p&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>e&&e("000"),children:"Clear"})]}),(0,d.jsx)(ae.A,{value:c,onChange:e,className:"w-full min-w-0 max-w-full",size:"sm",status:b,isDisabled:p})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),m&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>i&&i(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:h||"",isDisabled:q,onChange:a=>{let b=a.target.value;i&&i(b)}})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),l&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>k&&k(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: keuangan",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:r,onChange:a=>{let b=a.target.value;k&&k(b)}})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"})," ",(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:[f||"1"],onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),!b){g&&g("1");return}g&&g(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var ai=c(79300),aj=c(8753);let ak=({inquiryState:a})=>{let{dept:b,kdunit:c,setKdunit:e,unitkondisi:f,setUnitkondisi:h,kataunit:i,setKataunit:j,unitradio:k,setUnitradio:l}=a||{},m=i&&""!==i.trim(),o=f&&""!==f.trim(),p=c&&"XXX"!==c&&"XX"!==c,q=m||o,r=m||p,v=o||p;return g().useEffect(()=>{e&&e("XX")},[b,e]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(ai.A,{size:20,className:"ml-4 text-secondary"}),"Eselon I"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Pilih Eselon I"}),p&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>e&&e("XX"),children:"Clear"})]}),(0,d.jsx)(aj.A,{value:c,onChange:e,kddept:b,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihunit",isDisabled:q})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"ml-1 cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),o&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 01,02,03, dst",className:"w-full min-w-0",size:"sm",value:f||"",isDisabled:r,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),m&&!v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: sekretariat",className:"w-full min-w-0",size:"sm",value:i||"",isDisabled:v,onChange:a=>j&&j(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([k||"1"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&l&&l(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var al=c(97992),am=c(47820);let an=({inquiryState:a})=>{let{prov:b,setProv:c,locradio:e,setLocradio:f,lokasikondisi:g,setLokasikondisi:h,katalokasi:i,setKatalokasi:j}=a,k=i&&""!==i.trim(),l=g&&""!==g.trim(),m=b&&"XXX"!==b&&"XX"!==b&&"XX"!==b,o=k||l,p=k||m,q=l||m;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(al.A,{size:20,className:"ml-4 text-secondary"}),"Provinsi"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${o?"text-gray-400":"text-gray-700"}`,children:"Pilih Provinsi"}),m&&!o&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(am.A,{value:b,onChange:c,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihprov",isDisabled:o})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${p?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),l&&!p&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 31,32,33, dst",className:"w-full min-w-0",size:"sm",value:g||"",isDisabled:p,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),k&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: jawa",className:"w-full min-w-0",size:"sm",value:i||"",isDisabled:q,onChange:a=>j&&j(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([e||"1"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&f&&f(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Uraian"},{value:"3",label:"Kode Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var ao=c(92693),ap=c(85019);let aq=({inquiryState:a})=>{let{fungsi:b,setFungsi:c,fungsiradio:e,setFungsiradio:f,fungsikondisi:g,setFungsikondisi:h,katafungsi:i,setKatafungsi:j}=a,k=i&&""!==i.trim(),l=g&&""!==g.trim(),m=b&&"XXX"!==b&&"XX"!==b&&"00"!==b,o=k||l,p=k||m,q=l||m;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(ap.A,{size:20,className:"ml-4 text-secondary"}),"Fungsi"]})," ",(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[" ",(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${o?"text-gray-400":"text-gray-700"}`,children:"Pilih Fungsi"}),m&&!o&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("00"),children:"Clear"})]}),(0,d.jsx)(ao.A,{kdfungsi:b,onChange:c,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihfungsi",isDisabled:o})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${p?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),l&&!p&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{type:"text",placeholder:"misalkan: 01,02, dst atau !03",value:g,onChange:a=>h(a.target.value),className:"w-full",size:"sm",isDisabled:p})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Kata Kunci"}),k&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(s.r,{type:"text",placeholder:"misalkan: ekonomi",value:i,onChange:a=>j(a.target.value),className:"w-full",size:"sm",isDisabled:q})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"})," ",(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([e]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&f(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var ar=c(67498);let as=({inquiryState:a})=>{let{fungsi:b,sfungsi:c,setSfungsi:e,subfungsiradio:f,setSubfungsiradio:g,subfungsikondisi:h,setSubfungsikondisi:i,katasubfungsi:j,setKatasubfungsi:k}=a,l=j&&""!==j.trim(),m=h&&""!==h.trim(),o=c&&"XXX"!==c&&"XX"!==c&&"00"!==c,p=l||m,q=l||o,r=m||o;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(ai.A,{size:20,className:"ml-4 text-secondary"}),"Sub-Fungsi"]})," ",(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[" ",(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${p?"text-gray-400":"text-gray-700"}`,children:"Pilih Sub-Fungsi"}),o&&!p&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>e&&e("00"),children:"Clear"})]}),(0,d.jsx)(ar.A,{kdsfungsi:c,onChange:e,kdfungsi:b,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihsubfungsi",isDisabled:p})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),m&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>i&&i(""),children:"Clear"})]}),(0,d.jsx)(s.r,{type:"text",placeholder:"misalkan: 01,02, dst atau !01",value:h,onChange:a=>i(a.target.value),className:"w-full",size:"sm",isDisabled:q})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Kata Kunci"}),l&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>k&&k(""),children:"Clear"})]}),(0,d.jsx)(s.r,{type:"text",placeholder:"misalkan: industri",value:j,onChange:a=>k(a.target.value),className:"w-full",size:"sm",isDisabled:r})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"})," ",(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([f]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&g(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var at=c(84027),au=c(97163);!function(){var a=Error("Cannot find module '../../../data/Kdoutput.json'");throw a.code="MODULE_NOT_FOUND",a}();let av=(a,b,c)=>{let d=new Map;return a.forEach(a=>{a[b]&&!d.has(a[b])&&d.set(a[b],{value:a[b],label:a[c]||a[b]})}),Array.from(d.values())},aw=({inquiryState:a,type:b="program"})=>{let{value:c,setValue:e,kondisi:h,setKondisi:i,kata:j,setKata:k,radio:l,setRadio:m,filterProps:o,title:p,label:q}="activity"===b?{value:a?.giat,setValue:a?.setGiat,kondisi:a?.giatkondisi,setKondisi:a?.setGiatkondisi,kata:a?.katagiat,setKata:a?.setKatagiat,radio:a?.kegiatanradio,setRadio:a?.setKegiatanradio,filterProps:{kddept:a?.dept,kdunit:a?.kdunit,kdprogram:a?.program},title:"Kegiatan",label:"Pilih Kegiatan"}:{value:a?.program,setValue:a?.setProgram,kondisi:a?.programkondisi,setKondisi:a?.setProgramkondisi,kata:a?.kataprogram,setKata:a?.setKataprogram,radio:a?.programradio,setRadio:a?.setProgramradio,filterProps:{kddept:a?.dept,kdunit:a?.kdunit},title:"Program",label:"Pilih Program"},[r,v]=(0,f.useState)([]);(0,f.useEffect)(()=>{"program"===b&&v(((a,b)=>{let c=Object(function(){var a=Error("Cannot find module '../../../data/Kdoutput.json'");throw a.code="MODULE_NOT_FOUND",a}());return a&&"XX"!==a&&(c=c.filter(b=>b.kddept===a)),b&&"XX"!==b&&(c=c.filter(a=>a.kdunit===b)),av(c,"kdprogram","nmprogram")})(o.kddept,o.kdunit))},[b,o.kddept,o.kdunit]),g().useEffect(()=>{e&&e("XX")},[o.kddept,o.kdunit,o.kdprogram,e]);let w=()=>h&&""!==h||j&&""!==j||l&&"1"!==l,x=w(),y=w()&&!h,z=w()&&!j,A=h&&""!==h,B=j&&""!==j;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(at.A,{size:20,className:"ml-4 text-secondary"}),p]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:q}),(0,d.jsx)(au.A,{value:c,onChange:e,...o,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:q,status:"activity"===b?"pilihgiat":"pilihprogram",isDisabled:x})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${y?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),A&&!y&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>i&&i(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:h||"",isDisabled:y,onChange:a=>i&&i(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${z?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),B&&!z&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>k&&k(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: pendidikan",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:z,onChange:a=>k&&k(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{selectedKeys:l?[l]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];m&&m(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var ax=c(58559),ay=c(1732);let az=({inquiryState:a})=>{let{giat:b,setGiat:c,giatkondisi:e,setGiatkondisi:h,katagiat:i,setKatagiat:j,kegiatanradio:k,setKegiatanradio:l,dept:m,kdunit:o,program:p}=a,q=i&&""!==i.trim(),r=e&&""!==e.trim(),v=b&&"XXX"!==b&&"XX"!==b&&"XXX"!==b,w=q||r,x=q||v,y=r||v,[z,A]=(0,f.useState)([]);return(0,f.useEffect)(()=>{A(((a,b,c)=>{let d=Object(function(){var a=Error("Cannot find module '../../../data/Kdoutput.json'");throw a.code="MODULE_NOT_FOUND",a}());return a&&"XX"!==a&&(d=d.filter(b=>b.kddept===a)),b&&"XX"!==b&&(d=d.filter(a=>a.kdunit===b)),c&&"XX"!==c&&(d=d.filter(a=>a.kdprogram===c)),av(d,"kdgiat","nmgiat")})(m,o,p))},[m,o,p]),g().useEffect(()=>{c&&c("XX")},[m,o,p,c]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(ax.A,{size:20,className:"ml-4 text-secondary"}),"Kegiatan"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Pilih Kegiatan"}),(0,d.jsx)(ay.A,{value:b,onChange:c,kddept:m,kdunit:o,kdprogram:p,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Kegiatan",status:"pilihgiat",isDisabled:w})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${x?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),r&&!x&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 1001,1002,1003, dst",className:"w-full min-w-0",size:"sm",value:e||"",isDisabled:x,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${y?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),q&&!y&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: layanan",className:"w-full min-w-0",size:"sm",value:i||"",isDisabled:y,onChange:a=>j&&j(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{selectedKeys:k?[k]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];l&&l(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aA=c(28947),aB=c(95760),aC=c(11071);let aD=({inquiryState:a,type:b="output"})=>{let{value:c,setValue:e,kondisi:h,setKondisi:i,kata:j,setKata:k,radio:l,setRadio:m,filterProps:o,title:p,label:q,Component:r}="suboutput"===b?{value:a?.soutput,setValue:a?.setsOutput,kondisi:a?.soutputkondisi,setKondisi:a?.setSoutputkondisi,kata:a?.katasoutput,setKata:a?.setKatasoutput,radio:a?.soutputradio,setRadio:a?.setsOutputradio,filterProps:{kdgiat:a?.giat,kdoutput:a?.output},title:"Sub-output",label:"Pilih Sub-output",Component:aC.A}:{value:a?.output,setValue:a?.setOutput,kondisi:a?.outputkondisi,setKondisi:a?.setOutputkondisi,kata:a?.kataoutput,setKata:a?.setKataoutput,radio:a?.outputradio,setRadio:a?.setOutputradio,filterProps:{kddept:a?.dept,kdunit:a?.kdunit,kdprogram:a?.program,kdgiat:a?.giat},title:"Output",label:"Pilih Output",Component:aB.A},[v,w]=(0,f.useState)([]);(0,f.useEffect)(()=>{"output"===b&&w(((a,b,c,d)=>{let e=Object(function(){var a=Error("Cannot find module '../../../data/Kdoutput.json'");throw a.code="MODULE_NOT_FOUND",a}());return a&&"XX"!==a&&(e=e.filter(b=>b.kddept===a)),b&&"XX"!==b&&(e=e.filter(a=>a.kdunit===b)),c&&"XX"!==c&&(e=e.filter(a=>a.kdprogram===c)),d&&"XX"!==d&&(e=e.filter(a=>a.kdgiat===d)),av(e,"kdoutput","nmoutput")})(o.kddept||a?.dept,o.kdunit||a?.kdunit,o.kdprogram||a?.program,o.kdgiat||a?.giat))},[b,a?.dept,a?.kdunit,a?.program,a?.giat]),g().useEffect(()=>{e&&e("XX")},[o.kddept,o.kdunit,o.kdprogram,o.kdgiat,o.kdoutput,e]);let x=h&&""!==h.trim(),y=j&&""!==j.trim(),z=c&&"XX"!==c&&"XXX"!==c,A=x||y,B=y||z,C=x||z;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aA.A,{size:20,className:"ml-4 text-secondary"}),p]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:q}),(0,d.jsx)(r,{value:c,onChange:e,...o,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:q,status:"suboutput"===b?"pilihsoutput":"pilihoutput",isDisabled:A})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${B?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),x&&!B&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>i&&i(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: EAA,EAB,EAC, dst",className:"w-full min-w-0",size:"sm",value:h||"",isDisabled:B,onChange:a=>i&&i(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${C?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),y&&!C&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>k&&k(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: layanan",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:C,onChange:a=>k&&k(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{selectedKeys:l?[l]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];m&&m(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})},aE=({inquiryState:a})=>{let{soutput:b,setsOutput:c,soutputkondisi:e,setSoutputkondisi:f,katasoutput:h,setKatasoutput:i,soutputradio:j,setsOutputradio:k,giat:l,output:m}=a,o=h&&""!==h.trim(),p=e&&""!==e.trim(),q=b&&"XXX"!==b&&"XX"!==b&&"XXX"!==b,r=o||p,v=o||q,w=p||q;return g().useEffect(()=>{c&&c("XX")},[l,m,c]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-pink-100 to-rose-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aA.A,{size:20,className:"ml-4 text-secondary"}),"Sub-output"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Pilih Sub-output"}),q&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onClick:()=>c&&c(""),children:"Clear"})]}),(0,d.jsx)(aC.A,{value:b,onChange:c,kdgiat:l,kdoutput:m,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Sub-output",status:"pilihsoutput",isDisabled:r})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),p&&!v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onClick:()=>f&&f(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:e||"",onChange:a=>f&&f(a.target.value),isDisabled:v})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Mengandung Kata"}),o&&!w&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onClick:()=>i&&i(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: layanan",className:"w-full min-w-0",size:"sm",value:h||"",onChange:a=>i&&i(a.target.value),isDisabled:w})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{selectedKeys:j?[j]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];k&&k(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aF=c(26238);let aG=({inquiryState:a})=>{let{akun:b,setAkun:c,akunkondisi:e,setAkunkondisi:f,kataakun:g,setKataakun:h,akunradio:i,setAkunradio:j,jenlap:k,jenis:l,kdakun:m,setAkunType:o,setAkunValue:p,setAkunSql:q}=a,r=e&&""!==e.trim(),v=g&&""!==g.trim(),w=v||"10"===k;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(at.A,{size:20,className:"ml-4 text-secondary"}),"Akun"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",id:"label-pilih-akun",children:"Pilih Akun"})}),(0,d.jsx)(aF.A,{value:b&&b.type?b.type:b,onChange:a=>{c(a),o&&o(a.type),p&&p(a.value),q&&q(a.sql)},jenlap:k,jenis:l,kdakun:m,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Akun",status:"pilihakun",isDisabled:!1,"aria-labelledby":"label-pilih-akun"})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${w?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),r&&"10"!==k&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>f&&f(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:e||"",isDisabled:w,onChange:a=>f&&f(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Mengandung Kata"}),v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: gaji",className:"w-full min-w-0",size:"sm",value:g||"",isDisabled:r,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{selectedKeys:i?[i]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];j&&j(b)},disallowEmptySelection:!0,"aria-label":"Jenis Tampilan Akun",children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aH=c(42582);let aI=({inquiryState:a})=>{let{sdana:b,setSdana:c,sdanakondisi:e,setSdanakondisi:f,katasdana:g,setKatasdana:h,sdanaradio:i,setSdanaradio:j}=a,k=g&&""!==g.trim(),l=e&&""!==e.trim(),m=b&&"XXX"!==b&&"XX"!==b&&"XXX"!==b,o=k||l,p=k||m,q=l||m;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(at.A,{size:20,className:"ml-4 text-secondary"}),"Sumber Dana"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Pilih Sumber Dana"}),m&&!o&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onClick:()=>c&&c(""),children:"Clear"})]}),(0,d.jsx)(aH.A,{value:b,onChange:c,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Sumber Dana",status:"pilihsdana",isDisabled:o})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${p?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),l&&!p&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onClick:()=>f&&f(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:e||"",onChange:a=>f&&f(a.target.value),isDisabled:p})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Mengandung Kata"}),k&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onClick:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: rupiah",className:"w-full min-w-0",size:"sm",value:g||"",onChange:a=>h&&h(a.target.value),isDisabled:q})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{selectedKeys:i?[i]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];j&&j(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aJ=c(71850),aK=c(14320);let aL=({inquiryState:a})=>{let{dekon:b,setDekon:c,dekonkondisi:e,setDekonkondisi:f,katadekon:g,setKatadekon:h,dekonradio:i,setDekonradio:j}=a,k=g&&""!==g.trim(),l=e&&""!==e.trim(),m=b&&"XXX"!==b&&"XX"!==b&&"000"!==b&&""!==b.trim(),o=k||l,p=k||m,q=l||m;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aJ.A,{size:20,className:"ml-4 text-secondary"}),"Kewenangan"]})," ",(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${o?"text-gray-400":"text-gray-700"}`,children:"Pilih Kewenangan"}),m&&!o&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(aK.A,{value:b,onChange:c,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihdekon",isDisabled:o})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${p?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),l&&!p&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>f&&f(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: DK,TP,UB, dst",className:"w-full min-w-0",size:"sm",value:e||"",isDisabled:p,onChange:a=>f&&f(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),k&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: dekonsentrasi",className:"w-full min-w-0",size:"sm",value:g||"",isDisabled:q,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"})," ",(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([i]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&j(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aM=c(22982),aN=c(12198);let aO=({inquiryState:a})=>{let{kabkota:b,setKabkota:c,prov:e,kabkotakondisi:f,setKabkotakondisi:h,katakabkota:i,setKatakabkota:j,kabkotaradio:k,setKabkotaradio:l}=a,m=i&&""!==i.trim(),o=f&&""!==f.trim(),p=b&&"XXX"!==b&&"XX"!==b&&"XX"!==b,q=m||o,r=m||p,v=o||p;return g().useEffect(()=>{c&&c("XX")},[e,c]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[" ",(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aM.A,{size:20,className:"ml-4 text-secondary"}),"Kabupaten/Kota"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Pilih Kabupaten/Kota"}),p&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(aN.A,{value:b,onChange:c||(()=>console.warn("setKabkota is undefined")),kdlokasi:e,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Kabupaten/Kota",status:"pilihkdkabkota",isDisabled:q})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),o&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs flex-shrink-0",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 01,02,03, dst",className:"w-full min-w-0",size:"sm",value:f||"",isDisabled:r,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),m&&!v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: jakarta",className:"w-full min-w-0",size:"sm",value:i||"",isDisabled:v,onChange:a=>j&&j(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"})," ",(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:[k||"1"],onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),"string"==typeof b&&b.startsWith("$.")&&(b=b.replace("$.","")),!b){l&&l("1");return}l&&l(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aP=c(75757);let aQ=({inquiryState:a,status:b})=>{let{kanwil:c,setKanwil:e,prov:f,kanwilradio:h,setKanwilradio:i,kanwilkondisi:j,setKanwilkondisi:k,katakanwil:l,setKatakanwil:m}=a,o=l&&""!==l.trim(),p=j&&""!==j.trim(),q=c&&"XXX"!==c&&"XX"!==c&&"XX"!==c,r=o||p,v=o||q,w=p||q;return g().useEffect(()=>{e&&e("XX")},[f,e]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aJ.A,{size:20,className:"ml-4 text-secondary"}),"Kanwil"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Pilih Kanwil"}),q&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>e&&e("XX"),children:"Clear"})]}),(0,d.jsx)(aP.A,{value:c,onChange:e,kdlokasi:f,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihkanwil",isDisabled:r})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),p&&!v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>k&&k(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:v,onChange:a=>k&&k(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${w?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),o&&!w&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>m&&m(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: jakarta",className:"w-full min-w-0",size:"sm",value:l||"",isDisabled:w,onChange:a=>m&&m(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set(h?[h]:["1"]),onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),!b){i&&i("1");return}i&&i(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aR=c(62100),aS=c(72028);let aT=({inquiryState:a})=>{let{kppn:b,setKppn:c,kanwil:e,kppnkondisi:f,setKppnkondisi:h,katakppn:i,setKatakppn:j,kppnradio:k,setKppnradio:l}=a,m=i&&""!==i.trim(),o=f&&""!==f.trim(),p=b&&"XXX"!==b&&"XX"!==b&&"XX"!==b,q=m||o,r=m||p,v=o||p;return g().useEffect(()=>{c&&c("XX")},[e,c]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aR.A,{size:20,className:"ml-4 text-secondary"}),"KPPN"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Pilih KPPN"}),p&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(aS.A,{value:b,onChange:c||(()=>console.warn("setKppn is undefined")),kdkanwil:e,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih KPPN",status:"pilihkppn",isDisabled:q})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),o&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:f||"",isDisabled:r,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),m&&!v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: medan",className:"w-full min-w-0",size:"sm",value:i||"",isDisabled:v,onChange:a=>j&&j(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:[k||"1"],onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),"string"==typeof b&&b.startsWith("$.")&&(b=b.replace("$.","")),!b){l&&l("1");return}l&&l(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aU=c(17313),aV=c(74593);let aW=({inquiryState:a})=>{let{satker:b,setSatker:c,dept:e,kdunit:f,prov:h,kppn:i,satkerkondisi:j,setSatkerkondisi:k,katasatker:l,setKatasatker:m,satkerradio:o,setSatkerradio:p}=a,q=l&&""!==l.trim(),r=j&&""!==j.trim(),v=b&&"XXX"!==b&&"XX"!==b,w=q||r,x=q||v,y=r||v;return g().useEffect(()=>{c&&c("XX")},[e,f,h,i,c]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aU.A,{size:20,className:"text-secondary ml-4"}),"Satker"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${w?"text-gray-400":"text-gray-700"}`,children:"Pilih Satker"}),v&&!w&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(aV.A,{value:b,onChange:c||(()=>console.warn("setSatker is undefined")),kddept:e,kdunit:f,kdlokasi:h,kdkppn:i,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Satker",status:"pilihsatker",isDisabled:w})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${x?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),r&&!x&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>k&&k(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 647321,647322, dst",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:x,onChange:a=>k&&k(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${y?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),q&&!y&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>m&&m(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: universitas",className:"w-full min-w-0",size:"sm",value:l||"",isDisabled:y,onChange:a=>m&&m(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:[o||"1"],onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),"string"==typeof b&&b.startsWith("$.")&&(b=b.replace("$.","")),!b){p&&p("1");return}p&&p(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})},aX=({inquiryState:a})=>{let{jenlap:b,kddept:c,setKddept:e,unit:f,setUnit:h,kddekon:i,setKddekon:j,kdlokasi:k,setKdlokasi:l,kdkabkota:m,setKdkabkota:n,kdkanwil:o,setKdkanwil:p,kdkppn:q,setKdkppn:r,kdsatker:s,setKdsatker:t,kdfungsi:u,setKdfungsi:v,kdsfungsi:w,setKdsfungsi:x,kdprogram:y,setKdprogram:z,kdgiat:A,setKdgiat:B,kdoutput:C,setKdoutput:D,kdsoutput:E,setKdsoutput:F,kdakun:G,setKdakun:H,kdsdana:I,setKdsdana:J,kdInflasi:K,setKdInflasi:L,kdIkn:M,setKdIkn:N,kdKemiskinan:O,setKdKemiskinan:P,KdPRI:Q,setKdPRI:R,KdPangan:S,setKdPangan:T,KdPemilu:U,setKdPemilu:V,KdStunting:W,setKdStunting:X,KdTema:Y,setKdTema:Z,KdPN:$,setKdPN:_,KdPP:aa,setKdPP:ab,KdKegPP:ad,setKdKegPP:ae,KdMP:af,setKdMP:ag,KdMBG:ai,setKdMBG:aj,dept:al,setDept:am,deptkondisi:ao,setDeptkondisi:ap,katadept:ar,setKatadept:at,deptradio:au,setDeptradio:av,kdunit:ax,setKdunit:ay,unitkondisi:aA,setUnitkondisi:aB,kataunit:aC,setKataunit:aF,unitradio:aH,setUnitradio:aJ,dekon:aK,setDekon:aM,dekonkondisi:aN,setDekonkondisi:aP,katadekon:aR,setKatadekon:aS,dekonradio:aU,setDekonradio:aV,prov:aX,setProv:aY,lokasikondisi:aZ,setLokasikondisi:a$,katalokasi:a_,setKatalokasi:a0,locradio:a1,setLocradio:a2,kabkota:a3,setKabkota:a4,kabkotakondisi:a5,setKabkotakondisi:a6,katakabkota:a7,setKatakabkota:a8,kabkotaradio:a9,setKabkotaradio:ba,kanwil:bb,setKanwil:bc,kanwilkondisi:bd,setKanwilkondisi:be,katakanwil:bf,setKatakanwil:bg,kanwilradio:bh,setKanwilradio:bi,kppn:bj,setKppn:bk,kppnkondisi:bl,setKppnkondisi:bm,katakppn:bn,setKatakppn:bo,kppnradio:bp,setKppnradio:bq,satker:br,setSatker:bs,satkerkondisi:bt,setSatkerkondisi:bu,katasatker:bv,setKatasatker:bw,satkerradio:bx,setSatkerradio:by,fungsi:bz,setFungsi:bA,fungsikondisi:bB,setFungsikondisi:bC,katafungsi:bD,setKatafungsi:bE,fungsiradio:bF,setFungsiradio:bG,sfungsi:bH,setSfungsi:bI,subfungsikondisi:bJ,setSubfungsikondisi:bK,katasubfungsi:bL,setKatasubfungsi:bM,subfungsiradio:bN,setSubfungsiradio:bO,program:bP,setProgram:bQ,programkondisi:bR,setProgramkondisi:bS,kataprogram:bT,setKataprogram:bU,programradio:bV,setProgramradio:bW,giat:bX,setGiat:bY,giatkondisi:bZ,setGiatkondisi:b$,katagiat:b_,setKatagiat:b0,kegiatanradio:b1,setKegiatanradio:b2,output:b3,setOutput:b4,outputkondisi:b5,setOutputkondisi:b6,kataoutput:b7,setKataoutput:b8,outputradio:b9,setOutputradio:ca,soutput:cb,setsOutput:cc,soutputkondisi:cd,setSoutputkondisi:ce,katasoutput:cf,setKatasoutput:cg,soutputradio:ch,setsOutputradio:ci,akun:cj,setAkun:ck,akunkondisi:cl,setAkunkondisi:cm,kataakun:cn,setKataakun:co,akunradio:cp,setAkunradio:cq,sdana:cr,setSdana:cs,sdanakondisi:ct,setSdanakondisi:cu,katasdana:cv,setKatasdana:cw,sdanaradio:cx,setSdanaradio:cy}=a,cz=(a=>{let b=["KdPP","KdKegPP","KdPRI","KdMP","KdTema","kdInflasi","KdStunting","kdKemiskinan","KdPemilu","kdIkn","KdPangan","KdMBG"];return"6"===a?b.filter(a=>"kdKemiskinan"!==a).concat(["kdakun","kdregister","kdsdana"]):"1"===a?b.filter(a=>!["KdPP","KdKegPP","KdPRI"].includes(a)):"2"===a?b.filter(a=>"KdMP"!==a):"3"===a?b.filter(a=>"KdTema"!==a):"4"===a?b.filter(a=>"kdInflasi"!==a):"5"===a?b.filter(a=>"KdStunting"!==a):"7"===a?b.filter(a=>"KdPemilu"!==a):"8"===a?b.filter(a=>"kdIkn"!==a):"9"===a?b.filter(a=>"KdPangan"!==a):"10"===a?b.concat(["kdsoutput"]):"11"===a?b.filter(a=>"KdMBG"!==a):b})(b);return g().useEffect(()=>{b&&cz.length>0&&(cz.includes("kdsoutput")&&E&&F&&F(!1),cz.includes("KdPP")&&aa&&ab&&ab(!1),cz.includes("KdKegPP")&&ad&&ae&&ae(!1),cz.includes("KdPRI")&&Q&&R&&R(!1),cz.includes("KdMP")&&af&&ag&&ag(!1),cz.includes("KdTema")&&Y&&Z&&Z(!1),cz.includes("kdInflasi")&&K&&L&&L(!1),cz.includes("KdStunting")&&W&&X&&X(!1),cz.includes("kdKemiskinan")&&O&&P&&P(!1),cz.includes("KdPemilu")&&U&&V&&V(!1),cz.includes("kdIkn")&&M&&N&&N(!1),cz.includes("KdPangan")&&S&&T&&T(!1),cz.includes("KdMBG")&&ai&&aj&&aj(!1))},[b]),g().useEffect(()=>{"1"===b?(_&&_(!0),ab&&ab(!0),ae&&ae(!0),R&&R(!0),ag&&ag(!1),Z&&Z(!1),L&&L(!1),X&&X(!1)):"2"===b?(ag&&ag(!0),_&&_(!1),ab&&ab(!1),ae&&ae(!1),R&&R(!1),Z&&Z(!1),L&&L(!1),X&&X(!1)):"3"===b?(Z&&Z(!0),_&&_(!1),ab&&ab(!1),ae&&ae(!1),R&&R(!1),ag&&ag(!1),L&&L(!1),X&&X(!1)):"4"===b?(L&&L(!0),_&&_(!1),ab&&ab(!1),ae&&ae(!1),R&&R(!1),ag&&ag(!1),Z&&Z(!1),X&&X(!1)):"5"===b?(X&&X(!0),_&&_(!1),ab&&ab(!1),ae&&ae(!1),R&&R(!1),ag&&ag(!1),Z&&Z(!1),L&&L(!1)):"6"===b?(P&&P(!0),_&&_(!1),ab&&ab(!1),ae&&ae(!1),R&&R(!1),ag&&ag(!1),Z&&Z(!1),L&&L(!1),X&&X(!1),V&&V(!1)):"7"===b?(V&&V(!0),_&&_(!1),ab&&ab(!1),ae&&ae(!1),R&&R(!1),ag&&ag(!1),Z&&Z(!1),L&&L(!1),X&&X(!1),P&&P(!1),N&&N(!1)):"8"===b?(N&&N(!0),_&&_(!1),ab&&ab(!1),ae&&ae(!1),R&&R(!1),ag&&ag(!1),Z&&Z(!1),L&&L(!1),X&&X(!1),P&&P(!1),V&&V(!1)):"9"===b?(T&&T(!0),_&&_(!1),ab&&ab(!1),ae&&ae(!1),R&&R(!1),ag&&ag(!1),Z&&Z(!1),L&&L(!1),X&&X(!1),P&&P(!1),V&&V(!1),N&&N(!1)):"10"===b?(H&&H(!0),cm&&cm("511521,511522,511529,521231,521232,521233,521234,526111,526112,526113,526114,526115,526121,526122,526123,526124,526131,526132,526311,526312,526313,526321,526322,526323"),cq&&cq("1"),_&&_(!1),ab&&ab(!1),ae&&ae(!1),R&&R(!1),ag&&ag(!1),Z&&Z(!1),L&&L(!1),X&&X(!1),P&&P(!1),V&&V(!1),N&&N(!1),T&&T(!1),aj&&aj(!1)):"11"===b?(aj&&aj(!0),_&&_(!1),ab&&ab(!1),ae&&ae(!1),R&&R(!1),ag&&ag(!1),Z&&Z(!1),L&&L(!1),X&&X(!1),P&&P(!1),V&&V(!1),N&&N(!1),T&&T(!1)):(_&&_(!1),ab&&ab(!1),ae&&ae(!1),R&&R(!1),ag&&ag(!1),Z&&Z(!1),L&&L(!1),X&&X(!1),P&&P(!1),V&&V(!1),N&&N(!1),T&&T(!1),aj&&aj(!1))},[b]),g().useEffect(()=>{!c&&(am&&am("000"),ap&&ap(""),at&&at(""),av&&av("1"))},[c,am,ap,at,av]),g().useEffect(()=>{!f&&(ay&&ay("XX"),aB&&aB(""),aF&&aF(""),aJ&&aJ("1"))},[f,ay,aB,aF,aJ]),g().useEffect(()=>{!i&&(aM&&aM("XX"),aP&&aP(""),aS&&aS(""),aV&&aV("1"))},[i,aM,aP,aS,aV]),g().useEffect(()=>{!k&&(aY&&aY("XX"),a$&&a$(""),a0&&a0(""),a2&&a2("1"))},[k,aY,a$,a0,a2]),g().useEffect(()=>{!m&&(a4&&a4("XX"),a6&&a6(""),a8&&a8(""),ba&&ba("1"))},[m,a4,a6,a8,ba]),g().useEffect(()=>{!o&&(bc&&bc("XX"),be&&be(""),bg&&bg(""),bi&&bi("1"))},[o,bc,be,bg,bi]),g().useEffect(()=>{!q&&(bk&&bk("XX"),bm&&bm(""),bo&&bo(""),bq&&bq("1"))},[q,bk,bm,bo,bq]),g().useEffect(()=>{!s&&(bs&&bs("XX"),bu&&bu(""),bw&&bw(""),by&&by("1"))},[s,bs,bu,bw,by]),g().useEffect(()=>{!u&&(bA&&bA("XX"),bC&&bC(""),bE&&bE(""),bG&&bG("1"))},[u,bA,bC,bE,bG]),g().useEffect(()=>{!w&&(bI&&bI("XX"),bK&&bK(""),bM&&bM(""),bO&&bO("1"))},[w,bI,bK,bM,bO]),g().useEffect(()=>{!y&&(bQ&&bQ("XX"),bS&&bS(""),bU&&bU(""),bW&&bW("1"))},[y,bQ,bS,bU,bW]),g().useEffect(()=>{!A&&(bY&&bY("XX"),b$&&b$(""),b0&&b0(""),b2&&b2("1"))},[A,bY,b$,b0,b2]),g().useEffect(()=>{!C&&(b4&&b4("XX"),b6&&b6(""),b8&&b8(""),ca&&ca("1"))},[C,b4,b6,b8,ca]),g().useEffect(()=>{!E&&(cc&&cc("XX"),ce&&ce(""),cg&&cg(""),ci&&ci("1"))},[E,cc,ce,cg,ci]),g().useEffect(()=>{G?G&&"10"===b&&(cm&&cm("511521,511522,511529,521231,521232,521233,521234,526111,526112,526113,526114,526115,526121,526122,526123,526124,526131,526132,526311,526312,526313,526321,526322,526323"),cq&&cq("1"),co&&co("")):(ck&&ck("AKUN"),cm&&cm(""),co&&co(""),cq&&cq("1"))},[G,b,ck,cm,co,cq]),g().useEffect(()=>{!I&&(cs&&cs("XX"),cu&&cu(""),cw&&cw(""),cy&&cy("1"))},[I,cs,cu,cw,cy]),g().useEffect(()=>{if(!b)return;let a={kddept:!0,unit:!1,kddekon:!1,kdlokasi:!1,kdkabkota:!1,kdkanwil:!1,kdkppn:!1,kdsatker:!1,kdfungsi:!1,kdsfungsi:!1,kdprogram:!1,kdgiat:!1,kdoutput:!1,kdsoutput:!1,kdakun:"10"===b,kdsdana:!1,KdStunting:!1,kdKemiskinan:!1,KdPemilu:!1,kdIkn:!1,KdPangan:!1};e&&e(a.kddept),h&&h(a.unit),j&&j(a.kddekon),l&&l(a.kdlokasi),n&&n(a.kdkabkota),p&&p(a.kdkanwil),r&&r(a.kdkppn),t&&t(a.kdsatker),v&&v(a.kdfungsi),x&&x(a.kdsfungsi),z&&z(a.kdprogram),B&&B(a.kdgiat),D&&D(a.kdoutput),F&&F(a.kdsoutput),H&&H(a.kdakun),J&&J(a.kdsdana),X&&X(a.KdStunting),P&&P(a.kdKemiskinan),V&&V(a.KdPemilu),N&&N(a.kdIkn),T&&T(a.KdPangan)},[b]),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-full p-3 mb-6 sm:p-4 bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl",children:(0,d.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-6 2xl:grid-cols-8 gap-2 sm:gap-3",children:[(0,d.jsx)(ac,{id:"kddept-filter",checked:!!c,onChange:e,label:"Kementerian"}),(0,d.jsx)(ac,{id:"unit-filter",checked:f,onChange:h,label:"Eselon I"}),(0,d.jsx)(ac,{id:"dekon-filter",checked:i,onChange:j,label:"Kewenangan"}),(0,d.jsx)(ac,{id:"lokasi-filter",checked:k,onChange:l,label:"Provinsi"}),(0,d.jsx)(ac,{id:"kabkota-filter",checked:m,onChange:n,label:"Kabupaten/Kota"}),(0,d.jsx)(ac,{id:"kanwil-filter",checked:o,onChange:p,label:"Kanwil"}),(0,d.jsx)(ac,{id:"kdkppn-filter",checked:q,onChange:r,label:"KPPN"}),(0,d.jsx)(ac,{id:"kdsatker-filter",checked:s,onChange:t,label:"Satker"}),(0,d.jsx)(ac,{id:"kdfungsi-filter",checked:u,onChange:v,label:"Fungsi"}),(0,d.jsx)(ac,{id:"kdsfungsi-filter",checked:w,onChange:x,label:"Sub-fungsi"}),(0,d.jsx)(ac,{id:"kdprogram-filter",checked:y,onChange:z,label:"Program"}),(0,d.jsx)(ac,{id:"kdgiat-filter",checked:A,onChange:B,label:"Kegiatan"}),(0,d.jsx)(ac,{id:"kdoutput-filter",checked:C,onChange:D,label:"Output"}),(0,d.jsx)(ac,{id:"kdsoutput-filter",checked:E,onChange:F,label:"Sub-output",disabled:cz.includes("kdsoutput")}),(0,d.jsx)(ac,{id:"kdakun-filter",checked:G,onChange:H,label:"Akun",disabled:"10"===b||cz.includes("kdakun")}),(0,d.jsx)(ac,{id:"kdsdana-filter",checked:I,onChange:J,label:"Sumber Dana",disabled:cz.includes("kdsdana")})]})}),(0,d.jsxs)("div",{className:"space-y-4 mb-4",children:[c&&(0,d.jsx)(ah,{inquiryState:a,status:c?"pilihdept":""}),f&&(0,d.jsx)(ak,{inquiryState:a}),i&&(0,d.jsx)(aL,{inquiryState:a}),k&&(0,d.jsx)(an,{inquiryState:a}),m&&(0,d.jsx)(aO,{inquiryState:a}),o&&(0,d.jsx)(aQ,{inquiryState:a}),q&&(0,d.jsx)(aT,{inquiryState:a}),s&&(0,d.jsx)(aW,{inquiryState:a}),u&&(0,d.jsx)(aq,{inquiryState:a}),w&&(0,d.jsx)(as,{inquiryState:a}),y&&(0,d.jsx)(aw,{inquiryState:a}),A&&(0,d.jsx)(az,{inquiryState:a}),C&&(0,d.jsx)(aD,{type:"output",inquiryState:a}),E&&(0,d.jsx)(aE,{inquiryState:a}),G&&(0,d.jsx)(aG,{inquiryState:a}),I&&(0,d.jsx)(aI,{type:"source",inquiryState:a})]})]})};var aY=c(36220),aZ=c(2840),a$=c(97840),a_=c(78122),a0=c(31158);let a1=({onExecuteQuery:a,onExportExcel:b,onExportCSV:c,onExportPDF:e,onReset:f,onSaveQuery:g,onShowSQL:h,isLoading:i})=>(0,d.jsx)(T.Z,{className:"mb-4 shadow-none bg-transparent",children:(0,d.jsx)(aY.U,{children:(0,d.jsxs)("div",{className:"flex flex-wrap gap-6 justify-center md:justify-center",children:[(0,d.jsx)(n.T,{color:"primary",startContent:(0,d.jsx)(a$.A,{size:16}),onClick:a,isLoading:i,className:"w-[160px] h-[50px]",children:"Tayang Data"}),(0,d.jsx)(n.T,{color:"danger",variant:"ghost",startContent:(0,d.jsx)(a_.A,{size:16}),onClick:f,isDisabled:i,className:"w-[160px] h-[50px]",children:"Reset Filter"}),(0,d.jsxs)(aZ.x,{children:[(0,d.jsx)(n.T,{color:"success",variant:"flat",startContent:(0,d.jsx)(a0.A,{size:16}),onClick:b,isDisabled:i,className:"w-[120px] h-[50px]",children:"Excel"}),(0,d.jsx)(n.T,{color:"secondary",variant:"flat",startContent:(0,d.jsx)(a0.A,{size:16}),onClick:c,isDisabled:i,className:"w-[120px] h-[50px]",children:"CSV"})]}),(0,d.jsx)(n.T,{color:"success",variant:"flat",startContent:(0,d.jsx)(F.A,{size:16}),onClick:e,isDisabled:i,className:"w-[160px] h-[50px]",children:"Kirim WA"}),(0,d.jsx)(n.T,{color:"warning",variant:"flat",startContent:(0,d.jsx)(x.A,{size:16}),onClick:g,isDisabled:i,className:"w-[160px] h-[50px]",children:"Simpan Query"}),(0,d.jsx)(n.T,{color:"default",variant:"flat",startContent:(0,d.jsx)(I.A,{size:16}),onClick:h,isDisabled:i,className:"w-[160px] h-[50px]",children:"Tayang SQL"})]})})}),a2=({inquiryState:a,onFilterChange:b})=>{let{thang:c,setThang:e,jenlap:f,setJenlap:h,pembulatan:i,setPembulatan:j}=a||{},[k,l]=g().useState("2025"),[m,n]=g().useState("1"),[o,p]=g().useState("1"),q=null!=c?c:k,r=null!=f?f:m,s=null!=i?i:o;g().useEffect(()=>{b&&b({thang:q,jenlap:r,pembulatan:s})},[q,r,s,b]);let v=a=>b=>{let c=Array.from(b)[0];a&&void 0!==c&&a(c)};return(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)("div",{className:"w-full p-3 mb-4 sm:p-4 bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row gap-6 w-full",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("label",{id:"thang-label",className:"block text-sm font-medium mb-2",children:"Tahun Anggaran"}),(0,d.jsx)(t.d,{selectedKeys:[q],onSelectionChange:v(e||l),className:"w-full",placeholder:"Pilih Tahun",disallowEmptySelection:!0,"aria-labelledby":"thang-label","aria-label":"Pilih Tahun Anggaran",children:["2025","2024","2023"].map(a=>(0,d.jsx)(u.y,{textValue:a,children:a},a))})]}),(0,d.jsxs)("div",{className:"flex-[1.5]",children:[(0,d.jsx)("label",{id:"jenlap-label",className:"block text-sm font-medium mb-2",children:"Jenis Laporan"}),(0,d.jsx)(t.d,{selectedKeys:[r],onSelectionChange:v(h||n),className:"w-full",placeholder:"Pilih Jenis Laporan",disallowEmptySelection:!0,"aria-labelledby":"jenlap-label","aria-label":"Pilih Jenis Laporan",children:[{value:"1",label:"Prioritas Nasional"},{value:"2",label:"Major Project"},{value:"3",label:"Tematik Anggaran"},{value:"4",label:"Inflasi"},{value:"5",label:"Penanganan Stunting"},{value:"6",label:"Kemiskinan Ekstrim"},{value:"7",label:"Belanja Pemilu"},{value:"8",label:"Ibu Kota Nusantara (IKN)"},{value:"9",label:"Ketahanan Pangan"},{value:"10",label:"Bantuan Pemerintah"},{value:"11",label:"Makanan Bergizi Gratis"},{value:"12",label:"Swasembada Pangan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("label",{id:"pembulatan-label",className:"block text-sm font-medium mb-2",children:"Pembulatan"}),(0,d.jsx)(t.d,{selectedKeys:[s],onSelectionChange:v(j||p),className:"w-full",placeholder:"Pilih Pembulatan",disallowEmptySelection:!0,"aria-labelledby":"pembulatan-label","aria-label":"Pilih Pembulatan",children:[{value:"1",label:"Rupiah"},{value:"1000",label:"Ribuan"},{value:"1000000",label:"Jutaan"},{value:"1000000000",label:"Miliar"},{value:"1000000000000",label:"Triliun"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]})})})};var a3=c(92363),a4=c(56429);let a5=({inquiryState:a})=>{let{PN:b,setPN:c,pnradio:e,setPnradio:f}=a;return(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(a3.A,{size:20,className:"ml-4 text-secondary"}),"Prioritas Nasional"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[4] xl:max-w-[800px] xl:min-w-[350px]",children:(0,d.jsx)(a4.A,{value:b,onChange:c,className:"w-full",size:"sm",placeholder:"Pilih Prioritas Nasional"})}),(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[1] xl:min-w-[150px]",children:(0,d.jsx)(t.d,{"aria-label":"Jenis Tampilan Prioritas Nasional",selectedKeys:e?[e]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];f&&f(b)},disallowEmptySelection:!0,size:"sm",className:"w-full min-w-0",children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})})]})})]})})};var a6=c(11539);let a7=({inquiryState:a})=>{let{PP:b,setPP:c,ppradio:e,setPpradio:f,PN:g}=a;return(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aA.A,{size:20,className:"ml-4 text-secondary"}),"Program Prioritas"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[4] xl:max-w-[800px] xl:min-w-[350px]",children:(0,d.jsx)(a6.A,{value:b,onChange:c,kdPN:g,className:"w-full",size:"sm",placeholder:"Pilih Program Prioritas"})}),(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[1] xl:min-w-[150px]",children:(0,d.jsx)(t.d,{"aria-label":"Jenis Tampilan Program Prioritas",selectedKeys:e?[e]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];f&&f(b)},disallowEmptySelection:!0,size:"sm",className:"w-full min-w-0",children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})})]})})]})})};var a8=c(99220);let a9=({inquiryState:a})=>{let{kegiatanprioritas:b,setKegiatanPrioritas:c,kegiatanprioritasradio:e,setKegiatanPrioritasRadio:f,PP:h,PN:i,thang:j}=a;return g().useEffect(()=>{},[b,i,h]),(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aA.A,{size:20,className:"ml-4 text-secondary"}),"Kegiatan Prioritas"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[4] xl:max-w-[800px] xl:min-w-[350px]",children:(0,d.jsx)(a8.A,{value:b,onChange:a=>{c(a)},kdPN:i,kdPP:h,thang:j})}),(0,d.jsx)("div",{className:"fflex flex-col gap-1 w-full xl:flex-[1] xl:min-w-[150px]",children:(0,d.jsx)(t.d,{"aria-label":"Jenis Tampilan Kegiatan Prioritas",selectedKeys:e?[e]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];f&&f(b)},disallowEmptySelection:!0,size:"sm",className:"w-full min-w-0",children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})})]})})]})})};var ba=c(93460);let bb=({inquiryState:a})=>{let{PRI:b,setPRI:c,priradio:e,setPriradio:f,PN:g,PP:h,KegPP:i,thang:j}=a;return(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aA.A,{size:20,className:"ml-4 text-secondary"}),"Proyek Prioritas"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[4] xl:max-w-[800px] xl:min-w-[350px]",children:(0,d.jsx)(ba.A,{value:b,onChange:c,kdPN:g,kdPP:h,KegPP:i,thang:j,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Proyek Prioritas"})}),(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[1] xl:min-w-[150px]",children:(0,d.jsx)(t.d,{"aria-label":"Jenis Tampilan Proyek Prioritas",selectedKeys:e?[e]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];f&&f(b)},disallowEmptySelection:!0,size:"sm",className:"w-full min-w-0",children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})})]})})]})})},bc=({inquiryState:a})=>(0,d.jsxs)("div",{className:"w-full space-y-4 bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none pl-2 pr-6 py-4 mb-4 rounded-2xl",children:[(0,d.jsx)(a5,{inquiryState:a}),(0,d.jsx)(a7,{inquiryState:a}),(0,d.jsx)(a9,{inquiryState:a}),(0,d.jsx)(bb,{inquiryState:a})]});var bd=c(57800),be=c(43400);let bf=({inquiryState:a})=>{let{MP:b,setMP:c,mpradio:e,setMpradio:f}=a;return(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(bd.A,{size:20,className:"ml-4 text-secondary"}),"Major Project"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[2] min-w-0 max-w-full overflow-hidden",children:(0,d.jsx)(be.A,{value:b,onChange:c,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Major Project"})}),(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[1]",children:(0,d.jsx)(t.d,{"aria-label":"Jenis Tampilan Major Project",selectedKeys:e?[e]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];f&&f(b)},disallowEmptySelection:!0,size:"sm",className:"w-full min-w-0",children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})})]})})]})})},bg=({inquiryState:a})=>(0,d.jsx)("div",{className:"w-full space-y-4 bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none px-6 py-4 mb-4 rounded-2xl",children:(0,d.jsx)(bf,{inquiryState:a})});var bh=c(30772);let bi=({inquiryState:a})=>{let{Tema:b,setTema:c,temaradio:e,setTemaradio:f}=a;return(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(ai.A,{size:20,className:"ml-4 text-secondary"}),"Tematik Anggaran"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[2] min-w-0 max-w-full overflow-hidden",children:(0,d.jsx)(bh.A,{value:b,onChange:c,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Tematik","aria-label":"Pilih Tematik"})}),(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:(0,d.jsx)(t.d,{selectedKeys:e?[e]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];f&&f(b)},disallowEmptySelection:!0,size:"sm",className:"w-full min-w-0","aria-label":"Jenis Tampilan Tematik",children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})})]})})]})})},bj=({inquiryState:a})=>(0,d.jsx)("div",{className:"w-full space-y-4 bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none pl-2 pr-6 py-4 mb-4 rounded-2xl",children:(0,d.jsx)(bi,{inquiryState:a})});var bk=c(25541),bl=c(93876);let bm=({inquiryState:a})=>{let{Inflasi:b,setInflasi:c,inflasiradio:e,setInflasiradio:f}=a;return(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(bk.A,{size:20,className:"ml-4 text-secondary"}),"Jenis Inflasi"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[2] min-w-0 max-w-full overflow-hidden",children:(0,d.jsx)(bl.A,{value:b,onChange:c,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Jenis Inflasi","aria-label":"Pilih Jenis Inflasi"})}),(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:(0,d.jsx)(t.d,{selectedKeys:e?[e]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];f&&f(b)},disallowEmptySelection:!0,size:"sm",className:"w-full min-w-0","aria-label":"Jenis Tampilan Inflasi",children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})})]})})]})})},bn=({inquiryState:a})=>(0,d.jsx)("div",{className:"w-full space-y-4 bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none pl-2 pr-6 py-4 mb-4 rounded-2xl",children:(0,d.jsx)(bm,{inquiryState:a})});var bo=c(42238),bp=c(84268);let bq=({inquiryState:a})=>{let{Stunting:b,setStunting:c,stuntingradio:e,setStuntingradio:f}=a;return(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(bo.A,{size:20,className:"ml-4 text-secondary"}),"Tematik Stunting"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[2] min-w-0 max-w-full overflow-hidden",children:(0,d.jsx)(bp.A,{value:b,onChange:c,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Tematik Stunting","aria-label":"Pilih Tematik Stunting"})}),(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[1]",children:(0,d.jsx)(t.d,{selectedKeys:e?[e]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];f&&f(b)},disallowEmptySelection:!0,size:"sm",className:"w-full min-w-0","aria-label":"Jenis Tampilan Stunting",children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})})]})})]})})},br=({inquiryState:a})=>(0,d.jsx)("div",{className:"w-full space-y-4 bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none pl-2 pr-6 py-4 mb-4 rounded-2xl",children:(0,d.jsx)(bq,{inquiryState:a})});!function(){var a=Error("Cannot find module '../../../data/KdMbg.json'");throw a.code="MODULE_NOT_FOUND",a}();let bs=a=>{let b=a.value&&""!==a.value&&"XX"!==a.value?[a.value]:["00"];return(0,d.jsxs)(t.d,{selectedKeys:new Set(b),onSelectionChange:b=>{let c=Array.from(b)[0];a.onChange(c)},size:"sm",placeholder:"Pilih Intervensi",className:"max-w-full","aria-label":"Pilih Jenis MBG",children:[(0,d.jsx)(u.y,{value:"00",textValue:"Semua Intervensi",children:"Semua Intervensi"},"00"),Object(function(){var a=Error("Cannot find module '../../../data/KdMbg.json'");throw a.code="MODULE_NOT_FOUND",a}())((a,b)=>(0,d.jsx)(u.y,{value:a.kdmbg,textValue:a.nmmbg,children:a.nmmbg},a.kdmbg))]})},bt=({inquiryState:a})=>{let{mbg:b,setmbg:c,mbgradio:e,setmbgradio:f}=a;return(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(bd.A,{size:20,className:"ml-4 text-secondary"}),"Makan Bergizi Gratis"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[2] min-w-0 max-w-full overflow-hidden",children:(0,d.jsx)(bs,{value:b,onChange:c,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Major Project","aria-label":"Pilih Major Project"})}),(0,d.jsx)("div",{className:"flex flex-col gap-1 w-full xl:flex-[1]",children:(0,d.jsx)(t.d,{selectedKeys:e?[e]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];f&&f(b)},disallowEmptySelection:!0,size:"sm",className:"w-full min-w-0","aria-label":"Jenis Tampilan MBG",children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})})]})})]})})},bu=({inquiryState:a})=>(0,d.jsx)("div",{className:"w-full space-y-4 bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none px-6 py-4 mb-4 rounded-2xl",children:(0,d.jsx)(bt,{inquiryState:a})});class bv{constructor(a,b,c=null){this.fieldName=a,this.tableName=b,this.referenceTable=c}buildColumns(a,b="",c={}){let d={columns:[],joinClause:"",groupBy:[]};if(!a)return d;let e=`a.${this.fieldName}`,f=this.referenceTable?`${this.referenceTable.alias}.${this.referenceTable.nameField}`:null,g=c&&"1"===c.jenlap?"a.pagu_apbn":"a.pagu";switch(a){case"1":d.columns.push(e),d.groupBy.push(e);break;case"2":d.columns.push(e),f&&(d.columns.push(f),d.joinClause=this.buildJoinClause(b)),d.groupBy.push(e);break;case"3":f&&(d.columns.push(f),d.joinClause=this.buildJoinClause(b)),d.groupBy.push(e)}return d.paguField=g,d}buildJoinClause(a=""){if(!this.referenceTable)return"";let b=this.referenceTable.hasYear?`_${a}`:"",c=`${this.referenceTable.schema}.${this.referenceTable.table}${b}`;return` LEFT JOIN ${c} ${this.referenceTable.alias} ON ${this.referenceTable.joinCondition}`}buildWhereConditions(a){let b=[],{pilihValue:c,kondisiValue:d,kataValue:e,opsiType:f,defaultValues:g=["XXX","000","XX","00","XXXX","0000","XXXXXX","000000"]}=a;if(e&&""!==e.trim()){let a=this.referenceTable?`${this.referenceTable.alias}.${this.referenceTable.nameField}`:`a.${this.fieldName}`;b.push(`${a} LIKE '%${e}%'`)}else d&&""!==d.trim()?b.push(this.parseKondisiConditions(d)):c&&!g.includes(c)&&b.push(`a.${this.fieldName} = '${c}'`);return b.filter(a=>a&&""!==a.trim())}parseKondisiConditions(a){if(!a||""===a.trim())return"";let b=`a.${this.fieldName}`;if("!"===a.substring(0,1)){let c=a.substring(1).split(",").map(a=>a.trim()).filter(a=>""!==a);if(c.length>0){let a=c.map(a=>`'${a}'`).join(",");return`${b} NOT IN (${a})`}}else if(a.includes("%"))return`${b} LIKE '${a}'`;else if(a.includes("-")&&!a.includes(",")){let[c,d]=a.split("-").map(a=>a.trim());if(c&&d)return`${b} BETWEEN '${c}' AND '${d}'`}else{let c=a.split(",").map(a=>a.trim()).filter(a=>""!==a);if(c.length>0){let a=c.map(a=>`'${a}'`).join(",");return`${b} IN (${a})`}}return""}build(a,b=""){let{isEnabled:c,radio:d,pilihValue:e,kondisiValue:f,kataValue:g,opsiType:h}=a,i={columns:[],joinClause:"",groupBy:[],whereConditions:[]};if(!c)return i;let j=this.buildColumns(d,b);if(i.columns=j.columns,i.joinClause=j.joinClause,i.groupBy=j.groupBy,g&&""!==g.trim()&&this.referenceTable){let a=`${this.referenceTable.alias}.${this.referenceTable.nameField}`;i.joinClause||(i.joinClause=this.buildJoinClause(b)),i.columns.includes(a)||i.columns.push(a)}return i.whereConditions=this.buildWhereConditions({pilihValue:e,kondisiValue:f,kataValue:g,opsiType:h}),i}getEmptyResult(){return{columns:[],joinClause:"",whereConditions:[],groupBy:[]}}}let bw=bv;class bx extends bw{constructor(){super("kddept","department",{schema:"dbref",table:"t_dept",alias:"b",nameField:"nmdept",hasYear:!0,joinCondition:"a.kddept=b.kddept"})}buildFromState(a){let{kddept:b,dept:c,deptkondisi:d,katadept:e,deptradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class by extends bw{constructor(){super("kdunit","unit",{schema:"dbref",table:"t_unit",alias:"c",nameField:"nmunit",hasYear:!0,joinCondition:"a.kddept=c.kddept AND a.kdunit=c.kdunit"})}buildFromState(a){let{unit:b,kdunit:c,unitkondisi:d,kataunit:e,unitradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bz extends bw{constructor(){super("kddekon","dekonsentrasi",{schema:"dbref",table:"t_dekon",alias:"d",nameField:"nmdekon",hasYear:!0,joinCondition:"a.kddekon=d.kddekon"})}buildFromState(a){let{kddekon:b,dekon:c,dekonkondisi:d,katadekon:e,dekonradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bA extends bw{constructor(){super("kdsatker","satker",{schema:"dbref",table:"t_satker",alias:"s",nameField:"nmsatker",hasYear:!0,joinCondition:"a.kddept=s.kddept AND a.kdunit=s.kdunit AND a.kdsatker=s.kdsatker"})}buildFromState(a){let{kdsatker:b,satker:c,satkerkondisi:d,katasatker:e,satkerradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bB extends bw{constructor(){super("kdlokasi","provinsi",{schema:"dbref",table:"t_lokasi",alias:"p",nameField:"nmlokasi",hasYear:!0,joinCondition:"a.kdlokasi=p.kdlokasi"})}buildFromState(a){let{kdlokasi:b,prov:c,lokasikondisi:d,katalokasi:e,locradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bC extends bw{constructor(){super("kdkabkota","kabkota",{schema:"dbref",table:"t_kabkota",alias:"kk",nameField:"nmkabkota",hasYear:!0,joinCondition:"a.kdlokasi=kk.kdlokasi AND a.kdkabkota=kk.kdkabkota"})}buildFromState(a){let{kdkabkota:b,kabkota:c,kabkotakondisi:d,katakabkota:e,kabkotaradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bD extends bw{constructor(){super("kdkanwil","kanwil",{schema:"dbref",table:"t_kanwil",alias:"kw",nameField:"nmkanwil",hasYear:!0,joinCondition:"a.kdkanwil=kw.kdkanwil"})}buildFromState(a){let{kdkanwil:b,kanwil:c,kanwilkondisi:d,katakanwil:e,kanwilradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bE extends bw{constructor(){super("kdkppn","kppn",{schema:"dbref",table:"t_kppn",alias:"kp",nameField:"nmkppn",hasYear:!0,joinCondition:"a.kdkppn=kp.kdkppn"})}buildFromState(a){let{kdkppn:b,kppn:c,kppnkondisi:d,katakppn:e,kppnradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bF extends bw{constructor(){super("kdfungsi","fungsi",{schema:"dbref",table:"t_fungsi",alias:"f",nameField:"nmfungsi",hasYear:!0,joinCondition:"a.kdfungsi=f.kdfungsi"})}buildFromState(a){let{kdfungsi:b,fungsi:c,fungsikondisi:d,katafungsi:e,fungsiradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bG extends bw{constructor(){super("kdsfung","subfungsi",{schema:"dbref",table:"t_sfung",alias:"sf",nameField:"nmsfung",hasYear:!0,joinCondition:"a.kdfungsi=sf.kdfungsi AND a.kdsfung=sf.kdsfung"})}buildFromState(a){let{kdsfungsi:b,sfungsi:c,subfungsikondisi:d,katasubfungsi:e,subfungsiradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bH extends bw{constructor(){super("kdprogram","program",{schema:"dbref",table:"t_program",alias:"pr",nameField:"nmprogram",hasYear:!0,joinCondition:"a.kddept=pr.kddept AND a.kdunit=pr.kdunit AND a.kdprogram=pr.kdprogram"})}buildFromState(a){let{kdprogram:b,program:c,programkondisi:d,kataprogram:e,programradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bI extends bw{constructor(){super("kdgiat","kegiatan",{schema:"dbref",table:"t_giat",alias:"g",nameField:"nmgiat",hasYear:!0,joinCondition:"a.kddept=g.kddept AND a.kdunit=g.kdunit AND a.kdprogram=g.kdprogram AND a.kdgiat=g.kdgiat"})}buildFromState(a){let{kdgiat:b,giat:c,giatkondisi:d,katagiat:e,kegiatanradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bJ extends bw{constructor(){super("kdoutput","output",{schema:"dbref",table:"t_output",alias:"o",nameField:"nmoutput",hasYear:!0,joinCondition:"a.kddept=o.kddept AND a.kdunit=o.kdunit AND a.kdprogram=o.kdprogram AND a.kdgiat=o.kdgiat AND a.kdoutput=o.kdoutput"})}buildFromState(a){let{kdoutput:b,output:c,outputkondisi:d,kataoutput:e,outputradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bK extends bw{constructor(){super("kdsoutput","suboutput",{schema:"dbref",table:"t_soutput",alias:"so",nameField:"nmsoutput",hasYear:!0,joinCondition:"a.kddept=so.kddept AND a.kdunit=so.kdunit AND a.kdprogram=so.kdprogram AND a.kdgiat=so.kdgiat AND a.kdoutput=so.kdoutput AND a.kdsoutput=so.kdsoutput"})}buildFromState(a){let{kdsoutput:b,soutput:c,soutputkondisi:d,katasoutput:e,soutputradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bL extends bw{constructor(){super("kdakun","akun",{schema:"dbref",table:"t_akun",alias:"ak",nameField:"nmakun",hasYear:!0,joinCondition:"a.kdakun=ak.kdakun"})}buildFromState(a){let{kdakun:b,akun:c,akunkondisi:d,kataakun:e,akunradio:f,thang:g}=a;if(b&&"4"===f)return{columns:[],groupBy:[],joinClause:"",whereConditions:[]};if(b&&("BKPK"===c||"JENBEL"===c)){let a="BKPK"===c?4:2,b=this.build({isEnabled:!0,radio:f,pilihValue:"",kondisiValue:"",kataValue:""},g);if("BKPK"===c){let c=`dbref.t_bkpk_${g}`;if("3"===f?(b.columns=["bk.nmbkpk"],b.joinClause=` LEFT JOIN ${c} bk ON LEFT(a.kdakun,${a}) = bk.kdbkpk`,b.groupBy=[`LEFT(a.kdakun,${a})`]):"2"===f?(b.columns=[`LEFT(a.kdakun,${a}) AS kdbkpk`,"bk.nmbkpk"],b.joinClause=` LEFT JOIN ${c} bk ON LEFT(a.kdakun,${a}) = bk.kdbkpk`,b.groupBy=[`LEFT(a.kdakun,${a})`]):(b.columns=[`LEFT(a.kdakun,${a}) AS kdbkpk`],b.groupBy=[`LEFT(a.kdakun,${a})`],b.joinClause=""),d&&/^[0-9]+$/.test(d)){let a=d.length;b.whereConditions=[`LEFT(a.kdakun,${a}) IN ('${d}')`]}e&&""!==e.trim()&&(b.whereConditions=[`bk.nmbkpk LIKE '%${e.trim()}%'`])}else if("JENBEL"===c){let c=`dbref.t_gbkpk_${g}`;if("3"===f?(b.columns=["gb.nmgbkpk"],b.joinClause=` LEFT JOIN ${c} gb ON LEFT(a.kdakun,${a}) = gb.kdgbkpk`,b.groupBy=[`LEFT(a.kdakun,${a})`]):"2"===f?(b.columns=[`LEFT(a.kdakun,${a}) AS kdgbkpk`,"gb.nmgbkpk"],b.joinClause=` LEFT JOIN ${c} gb ON LEFT(a.kdakun,${a}) = gb.kdgbkpk`,b.groupBy=[`LEFT(a.kdakun,${a})`]):(b.columns=[`LEFT(a.kdakun,${a}) AS kdgbkpk`],b.groupBy=[`LEFT(a.kdakun,${a})`],b.joinClause=""),d&&/^[0-9]+$/.test(d)){let a=d.length;b.whereConditions=[`LEFT(a.kdakun,${a}) IN ('${d}')`]}e&&""!==e.trim()&&(b.whereConditions=[`gb.nmgbkpk LIKE '%${e.trim()}%'`])}return b}if(b&&("AKUN"===c||!c)&&!d&&!e)return this.build({isEnabled:!0,radio:f,pilihValue:"",kondisiValue:"",kataValue:""},g);if(b&&d&&/^[0-9]+$/.test(d)){let a=d.length,c=`LEFT(a.kdakun,${a}) IN ('${d}')`;return{...this.build({isEnabled:b,radio:f,pilihValue:"",kondisiValue:"",kataValue:e},g),whereConditions:[c]}}if(b&&e&&""!==e.trim()){let a=`ak.nmakun LIKE '%${e.trim()}%'`;return{...this.build({isEnabled:b,radio:f,pilihValue:"",kondisiValue:"",kataValue:e},g),whereConditions:[a]}}return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bM extends bw{constructor(){super("kdsdana","sdana",{schema:"dbref",table:"t_sdana",alias:"sd",nameField:"nmsdana",hasYear:!0,joinCondition:"a.kdsdana=sd.kdsdana"})}buildFromState(a){let{kdsdana:b,sdana:c,sdanakondisi:d,opsikatasdana:e,sdanaradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bN extends bw{constructor(){super("kdregister","register",{schema:"dbref",table:"t_register",alias:"r",nameField:"nmregister",hasYear:!0,joinCondition:"a.kdregister=r.kdregister"})}buildFromState(a){let{kdregister:b,register:c,registerkondisi:d,opsikataregister:e,registerradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bO extends bw{constructor(){super("kdpn","pronas",{schema:"dbref",table:"t_prinas",alias:"pn",nameField:"nmpn",hasYear:!0,joinCondition:"a.kdpn=pn.kdpn"})}buildFromState(a){let{KdPN:b,PN:c,PNkondisi:d,opsikataPN:e,pnradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bP extends bw{constructor(){super("kdpp","propres",{schema:"dbref",table:"t_priprog",alias:"pp",nameField:"nmpp",hasYear:!0,joinCondition:"a.kdpp=pp.kdpp"})}buildFromState(a){let{KdPP:b,PP:c,PPkondisi:d,opsikataPP:e,ppradio:f,thang:g}=a,h=c;return c&&c.includes("-")&&(h=c.split("-")[1]),this.build({isEnabled:b,radio:f,pilihValue:h,kondisiValue:d,kataValue:e},g)}}class bQ extends bw{constructor(){super("kdkp","kegiatanprioritas",{schema:"dbref",table:"t_prigiat",alias:"pg",nameField:"nmkp",hasYear:!0,joinCondition:"a.kdkp=pg.kdkp AND a.kdpp=pg.kdpp AND a.kdpn=pg.kdpn"})}buildFromState(a){let{KdKegPP:b,kegiatanprioritas:c,kegiatanprioritasradio:d,thang:e}=a,f=this.build({isEnabled:b,radio:d,pilihValue:c,kondisiValue:void 0,kataValue:void 0},e);return b&&!f.joinClause&&(f.joinClause=this.buildJoinClause(e)),f}}class bR extends bw{constructor(){super("kdproy","prioritas",{schema:"dbref",table:"t_priproy",alias:"pri",nameField:"nmproy",hasYear:!0,joinCondition:"a.kdproy=pri.kdproy"})}buildFromState(a){let{KdPRI:b,PRI:c,PRIkondisi:d,opsikataPRI:e,priradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bS extends bw{constructor(){super("kdtema","tema",{schema:"dbref",table:"t_tema",alias:"tm",nameField:"nmtema",hasYear:!0,joinCondition:"a.kdtema=tm.kdtema"})}buildFromState(a){let{KdTema:b,Tema:c,Temakondisi:d,opsikataTema:e,temaradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bT extends bw{constructor(){super("kdmp","megaproject",{schema:"dbref",table:"t_mp",alias:"mp",nameField:"nmmp",hasYear:!1,joinCondition:"a.kdmp=mp.kdmp"})}buildFromState(a){let{KdMP:b,MP:c,MPkondisi:d,opsikataMP:e,mpradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bU extends bw{constructor(){super("inflasi")}buildFromState(a){let{jenlap:b,Inflasi:c,inflasiradio:d,opsiInflasi:e}=a;if("6"!==b)return this.getEmptyResult();let f=this.getEmptyResult();return"1"===d&&"XX"!==c&&(f.columns.push("a.inf_intervensi","a.inf_pengeluaran"),f.groupBy.push("a.inf_intervensi","a.inf_pengeluaran")),"2"===d&&"XX"!==c&&(f.columns.push("a.inf_intervensi","bb.ur_inf_intervensi","a.inf_pengeluaran","inf.ur_inf_pengeluaran"),f.joinClause=" LEFT JOIN dbref.ref_inf_intervensi bb ON a.inf_intervensi=bb.inf_intervensi LEFT JOIN dbref.ref_inf_pengeluaran inf on a.inf_pengeluaran=inf.inf_pengeluaran",f.groupBy.push("a.inf_intervensi","a.inf_pengeluaran")),"3"===d&&"XX"!==c&&(f.columns.push("bb.ur_inf_intervensi","inf.ur_inf_pengeluaran"),f.joinClause=" LEFT JOIN dbref.ref_inf_intervensi bb ON a.inf_intervensi=bb.inf_intervensi LEFT JOIN dbref.ref_inf_pengeluaran inf on a.inf_pengeluaran=inf.inf_pengeluaran",f.groupBy.push("a.inf_intervensi","a.inf_pengeluaran")),"4"===d&&(f.columns=[]),"pilihInflasi"===e&&"XX"!==c&&c&&"00"!==c&&f.whereConditions.push("(a.inf_intervensi <> 'NULL' OR a.inf_pengeluaran <> 'NULL')"),f}}class bV extends bw{constructor(){super("stunting")}buildFromState(a){let{jenlap:b,Stunting:c,stuntingradio:d}=a;if("6"!==b)return this.getEmptyResult();let e=this.getEmptyResult();return"1"===d&&"XX"!==c&&(e.columns.push("a.stun_intervensi"),e.groupBy.push("a.stun_intervensi")),"2"===d&&"XX"!==c&&(e.columns.push("a.stun_intervensi","stun.ur_stun_intervensi"),e.joinClause=" LEFT JOIN dbref.ref_stunting_intervensi stun ON a.stun_intervensi=stun.stun_intervensi",e.groupBy.push("a.stun_intervensi")),"3"===d&&"XX"!==c&&(e.columns.push("stun.ur_stun_intervensi"),e.joinClause=" LEFT JOIN dbref.ref_stunting_intervensi stun ON a.stun_intervensi=stun.stun_intervensi",e.groupBy.push("a.stun_intervensi")),"4"===d&&(e.columns=[]),e}}class bW extends bw{constructor(){super("kemiskinan")}buildFromState(a){let{jenlap:b,Miskin:c,kemiskinanradio:d,opsiKemiskinan:e}=a;if("6"!==b)return this.getEmptyResult();let f=this.getEmptyResult();return"1"===d&&"XX"!==c&&(f.columns.push("a.kemiskinan_ekstrim"),f.groupBy.push("a.kemiskinan_ekstrim")),"2"===d&&"XX"!==c&&(f.columns.push("a.kemiskinan_ekstrim","(CASE WHEN a.kemiskinan_ekstrim = 'TRUE' THEN 'Belanja Kemiskinan Esktrim' WHEN a.kemiskinan_ekstrim <> 'TRUE' THEN 'Bukan Kemiskinan Ekstrim' END) AS ur_kemiskinan_ekstrim"),f.groupBy.push("a.kemiskinan_ekstrim")),"3"===d&&"XX"!==c&&(f.columns.push("(CASE WHEN a.kemiskinan_ekstrim = 'TRUE' THEN 'Belanja Kemiskinan Esktrim' WHEN a.kemiskinan_ekstrim <> 'TRUE' THEN 'Bukan Kemiskinan Ekstrim' END) AS ur_kemiskinan_ekstrim"),f.groupBy.push("a.kemiskinan_ekstrim")),"4"===d&&(f.columns=[]),"pilihKemiskinan"===e&&"XX"!==c&&c&&"00"!==c&&f.whereConditions.push(`a.kemiskinan_ekstrim = '${c}'`),f}}class bX extends bw{constructor(){super("pemilu")}buildFromState(a){let{jenlap:b,Pemilu:c,pemiluradio:d}=a;if("6"!==b)return this.getEmptyResult();let e=this.getEmptyResult();return"1"===d&&"XX"!==c&&(e.columns.push("a.pemilu"),e.groupBy.push("a.pemilu")),"2"===d&&"XX"!==c&&(e.columns.push("a.pemilu","(CASE WHEN a.pemilu = 'TRUE' THEN 'Belanja Pemilu' WHEN a.pemilu <> 'TRUE' THEN 'Bukan Belanja Pemilu' END) AS ur_belanja_pemilu"),e.groupBy.push("a.pemilu")),"3"===d&&"XX"!==c&&(e.columns.push("(CASE WHEN a.pemilu = 'TRUE' THEN 'Belanja Pemilu' WHEN a.pemilu <> 'TRUE' THEN 'Bukan Belanja Pemilu' END) AS ur_belanja_pemilu"),e.groupBy.push("a.pemilu")),"4"===d&&(e.columns=[]),e}}class bY extends bw{constructor(){super("ikn")}buildFromState(a){let{jenlap:b,Ikn:c,iknradio:d,opsiIkn:e}=a;if("6"!==b)return this.getEmptyResult();let f=this.getEmptyResult();return"1"===d&&"XX"!==c&&(f.columns.push("a.ikn"),f.groupBy.push("a.ikn")),"2"===d&&"XX"!==c&&(f.columns.push("a.ikn","(CASE WHEN a.ikn = 'TRUE' THEN 'Belanja IKN' WHEN a.ikn <> 'TRUE' THEN 'Bukan Belanja IKN' END) AS ur_belanja_ikn"),f.groupBy.push("a.ikn")),"3"===d&&"XX"!==c&&(f.columns.push("(CASE WHEN a.ikn = 'TRUE' THEN 'Belanja IKN' WHEN a.ikn <> 'TRUE' THEN 'Bukan Belanja IKN' END) AS ur_belanja_ikn"),f.groupBy.push("a.ikn")),"4"===d&&(f.columns=[]),"pilihikn"===e&&"XX"!==c&&c&&"00"!==c&&f.whereConditions.push(`a.ikn = '${c}'`),f}}class bZ extends bw{constructor(){super("pangan")}buildFromState(a){let{jenlap:b,Pangan:c,panganradio:d}=a;if("6"!==b)return this.getEmptyResult();let e=this.getEmptyResult();return"1"===d&&"XX"!==c&&(e.columns.push("a.pangan"),e.groupBy.push("a.pangan")),"2"===d&&"XX"!==c&&(e.columns.push("a.pangan","(CASE WHEN a.pangan = 'TRUE' THEN 'Ketahanan Pangan' WHEN a.pangan <> 'TRUE' THEN 'Bukan Ketahanan Pangan' END) AS ur_belanja_pangan"),e.groupBy.push("a.pangan")),"3"===d&&"XX"!==c&&(e.columns.push("(CASE WHEN a.pangan = 'TRUE' THEN 'Ketahanan Pangan' WHEN a.pangan <> 'TRUE' THEN 'Bukan Ketahanan Pangan' END) AS ur_belanja_pangan"),e.groupBy.push("a.pangan")),"4"===d&&(e.columns=[]),e}}class b$ extends bw{constructor(){super("blokir")}buildFromState(a){let{jenlap:b,thang:c}=a;if("6"!==b)return this.getEmptyResult();let d=this.getEmptyResult();return d.columns.push("a.kdblokir","a.nmblokir"),d.groupBy.push("a.kdblokir"),d}}class b_ extends bw{constructor(){super("specialgrouping")}buildFromState(a){let{jenlap:b,thang:c}=a;if("7"!==b)return this.getEmptyResult();let d=this.getEmptyResult();return c>="2021"?d.groupBy.push("a.sat","a.os","a.ket"):d.groupBy.push("a.sat"),d}}class b0 extends bw{constructor(){super("mbg")}buildFromState(a){let{jenlap:b,mbg:c,mbgradio:d}=a;if("11"!==b)return this.getEmptyResult();let e=this.getEmptyResult();return"1"===d&&"XX"!==c?e.columns.push("A.MBG"):"2"===d&&"XX"!==c?(e.columns.push("A.MBG","mbg.nmmbg"),e.joinClause=" LEFT JOIN DBREF.T_MBG mbg ON A.MBG=mbg.kdmbg"):"3"===d&&"XX"!==c?(e.columns.push("mbg.nmmbg"),e.joinClause=" LEFT JOIN DBREF.T_MBG mbg ON A.MBG=mbg.kdmbg"):"4"===d||e.columns.push("A.MBG"),e.groupBy.push("A.MBG"),c&&"XX"!==c&&"00"!==c&&e.whereConditions.push(`A.MBG = '${c}'`),e}}class b1{constructor(){this.filters={department:new bx,unit:new by,dekon:new bz,satker:new bA,provinsi:new bB,kabkota:new bC,kanwil:new bD,kppn:new bE,fungsi:new bF,subfungsi:new bG,program:new bH,kegiatan:new bI,output:new bJ,suboutput:new bK,akun:new bL,sdana:new bM,register:new bN,pronas:new bO,propres:new bP,kegiatanprioritas:new bQ,prioritas:new bR,tema:new bS,megaproject:new bT,inflasi:new bU,stunting:new bV,kemiskinan:new bW,pemilu:new bX,ikn:new bY,pangan:new bZ,blokir:new b$,specialgrouping:new b_,mbg:new b0}}buildAllFilters(a){let b={columns:[],joinClauses:[],groupBy:[],whereConditions:[]};return Object.entries(this.filters).forEach(([c,d])=>{let e=!1;if("blokir"===c)e="7"===a.jenlap;else if("specialgrouping"===c)e="6"===a.jenlap;else if("1"===a.jenlap&&["pronas","propres","kegiatanprioritas","prioritas"].includes(c)){if(e=this.isFilterEnabled(c,a))try{let c=d.buildFromState(a);c.whereConditions.length>0&&b.whereConditions.push(...c.whereConditions)}catch(a){console.warn(`Error building ${c} filter WHERE conditions:`,a)}return}else e=this.isFilterEnabled(c,a);if(["inflasi","stunting","kemiskinan","pemilu","ikn","pangan","mbg"].includes(c),e)try{let c=d.buildFromState(a);c.columns.length>0&&b.columns.push(...c.columns),c.joinClause&&b.joinClauses.push(c.joinClause),c.groupBy.length>0&&b.groupBy.push(...c.groupBy),c.whereConditions.length>0&&b.whereConditions.push(...c.whereConditions)}catch(a){console.warn(`Error building ${c} filter:`,a)}}),b.columns=[...new Set(b.columns)],b.joinClauses=this.optimizeJoins(b.joinClauses),b.groupBy=[...new Set(b.groupBy)],b.whereConditions=b.whereConditions.filter(a=>a&&""!==a.trim()),b}buildFilter(a,b){let c=this.filters[a];if(!c)throw Error(`Filter '${a}' not found`);return c.buildFromState(b)}getAvailableFilters(){return Object.keys(this.filters)}isFilterEnabled(a,b){if("mbg"===a&&"11"===b.jenlap)return!0;let c={department:"kddept",unit:"unit",dekon:"kddekon",satker:"kdsatker",provinsi:"kdlokasi",kabkota:"kdkabkota",kanwil:"kdkanwil",kppn:"kdkppn",fungsi:"kdfungsi",subfungsi:"kdsfungsi",program:"kdprogram",kegiatan:"kdgiat",output:"kdoutput",suboutput:"kdsoutput",akun:"kdakun",sdana:"kdsdana",register:"kdregister",pronas:"KdPN",propres:"KdPP",kegiatanprioritas:"KdKegPP",prioritas:"KdPRI",tema:"KdTema",megaproject:"KdMP",inflasi:"kdInflasi",stunting:"KdStunting",kemiskinan:"kdKemiskinan",pemilu:"KdPemilu",ikn:"kdIkn",pangan:"KdPangan",mbg:"KdMBG"}[a];return!!c&&!!b[c]}optimizeJoins(a){return[...new Set(a)].filter(a=>a&&""!==a.trim()).sort()}buildAccessControl(a){let{role:b,kodekppn:c,kodekanwil:d}=a;return"3"===b&&c?`a.kdkppn = '${c}'`:"2"===b&&d?`a.kdkanwil = '${d}'`:""}buildWhereClause(a){let b=this.buildAllFilters(a),c=this.buildAccessControl(a),d=[...b.whereConditions];return(c&&d.push(c),0===d.length)?"":`WHERE ${d.join(" AND ")}`}validateFilters(a){let b=[],c=[],d=this.getAvailableFilters().filter(b=>this.isFilterEnabled(b,a));return d.length>10&&c.push(`High number of filters enabled (${d.length}). Consider reducing for better performance.`),this.isFilterEnabled("unit",a)&&!this.isFilterEnabled("department",a)&&c.push("Unit filter is enabled but Department filter is not. Consider enabling Department filter for better context."),{isValid:0===b.length,errors:b,warnings:c,enabledFilters:d}}getFilterStats(a){let b=this.buildAllFilters(a),c=this.validateFilters(a);return{totalFilters:Object.keys(this.filters).length,enabledFilters:c.enabledFilters.length,enabledFilterNames:c.enabledFilters,columnsCount:b.columns.length,joinsCount:b.joinClauses.length,whereConditionsCount:b.whereConditions.length,groupByCount:b.groupBy.length,validation:c}}getFilterSwitchValue(a,b){let c={inflasi:"kdInflasi",stunting:"KdStunting",kemiskinan:"kdKemiskinan",pemilu:"KdPemilu",ikn:"kdIkn",pangan:"KdPangan",mbg:"KdMBG"}[a];return c?b[c]:void 0}getFilterRadioValue(a,b){let c={inflasi:"inflasiradio",stunting:"stuntingradio",kemiskinan:"kemiskinanradio",pemilu:"pemiluradio",ikn:"iknradio",pangan:"panganradio",mbg:"mbgradio"}[a];return c?b[c]:void 0}getFilterOptionValue(a,b){let c={inflasi:"Inflasi",stunting:"Stunting",kemiskinan:"Miskin",pemilu:"Pemilu",ikn:"Ikn",pangan:"Pangan",mbg:"mbg"}[a];return c?b[c]:void 0}getFilterState(a,b){let c={pronas:{enabled:"KdPN",pilih:"PN",kondisi:"PNkondisi",kata:"opsikataPN",radio:"pnradio"},propres:{enabled:"KdPP",pilih:"PP",kondisi:"PPkondisi",kata:"opsikataPP",radio:"ppradio"},kegiatanprioritas:{enabled:"KdKegPP",pilih:"kegiatanprioritas",radio:"kegiatanprioritasradio"},prioritas:{enabled:"KdPRI",pilih:"PRI",kondisi:"PRIkondisi",kata:"opsikataPRI",radio:"priradio"}}[a];if(!c)return{};let d={};return Object.entries(c).forEach(([a,c])=>{d[a]=b[c]}),d}}class b2{constructor(){this.filterBuilder=new b1}buildDynamicFromAndSelect(a){let{thang:b,jenlap:c,cutoff:d,tanggal:e,akumulatif:f,pembulatan:g}=a,h=parseInt(d)>=1&&12>=parseInt(d)?parseInt(d):12,i="";for(let a=1;a<=h;a++)i+=`real${a}`,a!==h&&(i+="+ ");let j=`, ROUND(SUM(${"1"===c?"a.pagu_apbn":"a.pagu"})/${g},0) AS PAGU`,k=`, ROUND(SUM(a.blokir)/${g},0) AS BLOKIR`,l=["JAN","FEB","MAR","APR","MEI","JUN","JUL","AGS","SEP","OKT","NOV","DES"],m="",n="",o="",p="";for(let a=1;a<=12;a++){let b=l[a-1];if(a<=h){m+=`, ROUND(SUM(real${a})/${g},0) AS ${b}`;let c="";for(let b=1;b<=a;b++)c+=`real${b}`,b<a&&(c+="+");n+=`, ROUND(SUM(${c})/${g},0) AS ${b}`,o+=`, ROUND(sum(pagu${a})/${g}, 0) AS ${b}`,p+=`, ROUND(sum(blokir${a})/${g}, 0) AS ${b}`}else m+=`, 0 AS ${b}`,n+=`, 0 AS ${b}`,o+=`, 0 AS ${b}`,p+=`, 0 AS ${b}`}parseInt(d);let q=`monev${b}.pagu_real_detail_harian_${b} a`,r="",s="";switch(c){case"1":r=`monev${b}.a_pagu_real_bkpk_dja_${b} a`;let t=this.buildJenlap1PriorityColumns(a);s=`${t}, ROUND(sum(a.pagu)/${g},0) AS PAGU, ROUND(SUM(real1)/${g},0) AS JAN, ROUND(SUM(real2)/${g}, 0) AS FEB, ROUND(SUM(real3)/${g}, 0) AS MAR, ROUND(SUM(real4)/${g}, 0) AS APR, ROUND(SUM(real5)/${g}, 0) AS MEI, ROUND(SUM(real6)/${g}, 0) AS JUN, ROUND(SUM(real7)/${g}, 0) AS JUL, ROUND(SUM(real8)/${g}, 0) AS AGS, ROUND(SUM(real9)/${g}, 0) AS SEP, ROUND(SUM(real10)/${g}, 0) AS OKT, ROUND(SUM(real11)/${g}, 0) AS NOV, ROUND(SUM(real12)/${g}, 0) AS DES, ROUND(SUM(a.blokir) /${g},0) AS BLOKIR`;break;case"2":case"3":r=`monev${b}.a_pagu_real_bkpk_dja_${b} a`,s=`, ROUND(sum(a.pagu)/${g},0) AS PAGU, ROUND(SUM(real1)/${g},0) AS JAN, ROUND(SUM(real2)/${g}, 0) AS FEB, ROUND(SUM(real3)/${g}, 0) AS MAR, ROUND(SUM(real4)/${g}, 0) AS APR, ROUND(SUM(real5)/${g}, 0) AS MEI, ROUND(SUM(real6)/${g}, 0) AS JUN, ROUND(SUM(real7)/${g}, 0) AS JUL, ROUND(SUM(real8)/${g}, 0) AS AGS, ROUND(SUM(real9)/${g}, 0) AS SEP, ROUND(SUM(real10)/${g}, 0) AS OKT, ROUND(SUM(real11)/${g}, 0) AS NOV, ROUND(SUM(real12)/${g}, 0) AS DES, ROUND(SUM(a.blokir) /${g},0) AS BLOKIR`;break;case"4":r=`monev${b}.a_pagu_real_bkpk_dja_${b} a`;let u=this.buildJenlap4InflasiColumns(a);s=`${u}, ROUND(sum(a.pagu)/${g},0) AS PAGU, ROUND(SUM(real1)/${g},0) AS JAN, ROUND(SUM(real2)/${g}, 0) AS FEB, ROUND(SUM(real3)/${g}, 0) AS MAR, ROUND(SUM(real4)/${g}, 0) AS APR, ROUND(SUM(real5)/${g}, 0) AS MEI, ROUND(SUM(real6)/${g}, 0) AS JUN, ROUND(SUM(real7)/${g}, 0) AS JUL, ROUND(SUM(real8)/${g}, 0) AS AGS, ROUND(SUM(real9)/${g}, 0) AS SEP, ROUND(SUM(real10)/${g}, 0) AS OKT, ROUND(SUM(real11)/${g}, 0) AS NOV, ROUND(SUM(real12)/${g}, 0) AS DES, ROUND(SUM(a.blokir)/${g},0) AS BLOKIR`;break;case"5":r=`monev${b}.a_pagu_real_bkpk_dja_${b}_stunting a`;let v=this.buildJenlap5StuntingColumns(a);s=`${v}, ROUND(sum(a.pagu)/${g},0) AS PAGU, ROUND(SUM(real1)/${g},0) AS JAN, ROUND(SUM(real2)/${g}, 0) AS FEB, ROUND(SUM(real3)/${g}, 0) AS MAR, ROUND(SUM(real4)/${g}, 0) AS APR, ROUND(SUM(real5)/${g}, 0) AS MEI, ROUND(SUM(real6)/${g}, 0) AS JUN, ROUND(SUM(real7)/${g}, 0) AS JUL, ROUND(SUM(real8)/${g}, 0) AS AGS, ROUND(SUM(real9)/${g}, 0) AS SEP, ROUND(SUM(real10)/${g}, 0) AS OKT, ROUND(SUM(real11)/${g}, 0) AS NOV, ROUND(SUM(real12)/${g}, 0) AS DES, ROUND(SUM(a.blokir)/${g},0) AS BLOKIR`;break;case"6":r=`monev${b}.a_pagu_real_bkpk_dja_${b} a`,s=`a.kemiskinan_ekstrim, ROUND(sum(a.pagu)/${g},0) AS PAGU, ROUND(SUM(real1)/${g},0) AS JAN, ROUND(SUM(real2)/${g}, 0) AS FEB, ROUND(SUM(real3)/${g}, 0) AS MAR, ROUND(SUM(real4)/${g}, 0) AS APR, ROUND(SUM(real5)/${g}, 0) AS MEI, ROUND(SUM(real6)/${g}, 0) AS JUN, ROUND(SUM(real7)/${g}, 0) AS JUL, ROUND(SUM(real8)/${g}, 0) AS AGS, ROUND(SUM(real9)/${g}, 0) AS SEP, ROUND(SUM(real10)/${g}, 0) AS OKT, ROUND(SUM(real11)/${g}, 0) AS NOV, ROUND(SUM(real12)/${g}, 0) AS DES, ROUND(SUM(a.blokir)/${g},0) AS BLOKIR`;break;case"7":r=`monev${b}.a_pagu_real_bkpk_dja_${b} a`,s=`a.pemilu, ROUND(sum(a.pagu)/${g},0) AS PAGU, ROUND(SUM(real1)/${g},0) AS JAN, ROUND(SUM(real2)/${g}, 0) AS FEB, ROUND(SUM(real3)/${g}, 0) AS MAR, ROUND(SUM(real4)/${g}, 0) AS APR, ROUND(SUM(real5)/${g}, 0) AS MEI, ROUND(SUM(real6)/${g}, 0) AS JUN, ROUND(SUM(real7)/${g}, 0) AS JUL, ROUND(SUM(real8)/${g}, 0) AS AGS, ROUND(SUM(real9)/${g}, 0) AS SEP, ROUND(SUM(real10)/${g}, 0) AS OKT, ROUND(SUM(real11)/${g}, 0) AS NOV, ROUND(SUM(real12)/${g}, 0) AS DES, ROUND(SUM(a.blokir)/${g},0) AS BLOKIR`;break;case"8":r=`monev${b}.a_pagu_real_bkpk_dja_${b} a`,s=`a.ikn, ROUND(sum(a.pagu)/${g},0) AS PAGU, ROUND(SUM(real1)/${g},0) AS JAN, ROUND(SUM(real2)/${g}, 0) AS FEB, ROUND(SUM(real3)/${g}, 0) AS MAR, ROUND(SUM(real4)/${g}, 0) AS APR, ROUND(SUM(real5)/${g}, 0) AS MEI, ROUND(SUM(real6)/${g}, 0) AS JUN, ROUND(SUM(real7)/${g}, 0) AS JUL, ROUND(SUM(real8)/${g}, 0) AS AGS, ROUND(SUM(real9)/${g}, 0) AS SEP, ROUND(SUM(real10)/${g}, 0) AS OKT, ROUND(SUM(real11)/${g}, 0) AS NOV, ROUND(SUM(real12)/${g}, 0) AS DES, ROUND(SUM(a.blokir)/${g},0) AS BLOKIR`;break;case"9":r=`monev${b}.a_pagu_real_bkpk_dja_${b} a`,s=`a.pangan, ROUND(sum(a.pagu)/${g},0) AS PAGU, ROUND(SUM(real1)/${g},0) AS JAN, ROUND(SUM(real2)/${g}, 0) AS FEB, ROUND(SUM(real3)/${g}, 0) AS MAR, ROUND(SUM(real4)/${g}, 0) AS APR, ROUND(SUM(real5)/${g}, 0) AS MEI, ROUND(SUM(real6)/${g}, 0) AS JUN, ROUND(SUM(real7)/${g}, 0) AS JUL, ROUND(SUM(real8)/${g}, 0) AS AGS, ROUND(SUM(real9)/${g}, 0) AS SEP, ROUND(SUM(real10)/${g}, 0) AS OKT, ROUND(SUM(real11)/${g}, 0) AS NOV, ROUND(SUM(real12)/${g}, 0) AS DES, ROUND(SUM(a.blokir)/${g},0) AS BLOKIR`;break;case"10":r=`monev${b}.a_pagu_real_bkpk_dja_${b} a`,s=`, ROUND(sum(a.pagu)/${g},0) AS PAGU, ROUND(SUM(real1)/${g},0) AS JAN, ROUND(SUM(real2)/${g}, 0) AS FEB, ROUND(SUM(real3)/${g}, 0) AS MAR, ROUND(SUM(real4)/${g}, 0) AS APR, ROUND(SUM(real5)/${g}, 0) AS MEI, ROUND(SUM(real6)/${g}, 0) AS JUN, ROUND(SUM(real7)/${g}, 0) AS JUL, ROUND(SUM(real8)/${g}, 0) AS AGS, ROUND(SUM(real9)/${g}, 0) AS SEP, ROUND(SUM(real10)/${g}, 0) AS OKT, ROUND(SUM(real11)/${g}, 0) AS NOV, ROUND(SUM(real12)/${g}, 0) AS DES, ROUND(SUM(a.blokir)/${g},0) AS BLOKIR`;break;case"11":r=`monev${b}.a_pagu_real_bkpk_dja_${b} a`,s=`ROUND(sum(a.pagu)/${g},0) AS PAGU, ROUND(SUM(real1)/${g},0) AS JAN, ROUND(SUM(real2)/${g}, 0) AS FEB, ROUND(SUM(real3)/${g}, 0) AS MAR, ROUND(SUM(real4)/${g}, 0) AS APR, ROUND(SUM(real5)/${g}, 0) AS MEI, ROUND(SUM(real6)/${g}, 0) AS JUN, ROUND(SUM(real7)/${g}, 0) AS JUL, ROUND(SUM(real8)/${g}, 0) AS AGS, ROUND(SUM(real9)/${g}, 0) AS SEP, ROUND(SUM(real10)/${g}, 0) AS OKT, ROUND(SUM(real11)/${g}, 0) AS NOV, ROUND(SUM(real12)/${g}, 0) AS DES, ROUND(SUM(a.blokir)/${g},0) AS BLOKIR`;break;case"12":r=`monev${b}.a_pagu_real_bkpk_dja_${b} a`,s=`, a.swasembada, ROUND(sum(a.pagu)/${g},0) AS PAGU, ROUND(SUM(real1)/${g},0) AS JAN, ROUND(SUM(real2)/${g}, 0) AS FEB, ROUND(SUM(real3)/${g}, 0) AS MAR, ROUND(SUM(real4)/${g}, 0) AS APR, ROUND(SUM(real5)/${g}, 0) AS MEI, ROUND(SUM(real6)/${g}, 0) AS JUN, ROUND(SUM(real7)/${g}, 0) AS JUL, ROUND(SUM(real8)/${g}, 0) AS AGS, ROUND(SUM(real9)/${g}, 0) AS SEP, ROUND(SUM(real10)/${g}, 0) AS OKT, ROUND(SUM(real11)/${g}, 0) AS NOV, ROUND(SUM(real12)/${g}, 0) AS DES, ROUND(SUM(a.blokir)/${g},0) AS BLOKIR`;break;default:r=q,s=j+k}return{dynamicFrom:r,dynamicSelect:s}}buildJenlap1PriorityColumns(a){let{thang:b,pnradio:c,ppradio:d,kegiatanprioritasradio:e,priradio:f}=a,g=[];if(c&&"4"!==c)switch(c){case"1":g.push("a.kdpn");break;case"2":g.push("a.kdpn","pn.nmpn");break;case"3":g.push("pn.nmpn")}if(d&&"4"!==d)switch(d){case"1":g.push("a.kdpp");break;case"2":g.push("a.kdpp","pp.nmpp");break;case"3":g.push("pp.nmpp")}if(e&&"4"!==e)switch(e){case"1":g.push("a.kdkp");break;case"2":g.push("a.kdkp","pg.nmkp");break;case"3":g.push("pg.nmkp")}if(f&&"4"!==f)switch(f){case"1":g.push("a.kdproy");break;case"2":g.push("a.kdproy","pri.nmproy");break;case"3":g.push("pri.nmproy")}return 0===g.length&&g.push("a.kdpn","a.kdpp","a.kdkp","a.kdproy"),g.join(",")}buildJenlap1JoinClauses(a){let{thang:b,pnradio:c,ppradio:d,kegiatanprioritasradio:e,priradio:f}=a,g=[];return c&&("2"===c||"3"===c)&&g.push(` LEFT JOIN dbref.t_prinas_${b} pn ON a.kdpn=pn.kdpn`),d&&("2"===d||"3"===d)&&g.push(` LEFT JOIN dbref.t_priprog_${b} pp ON a.kdpp=pp.kdpp`),e&&("2"===e||"3"===e)&&g.push(` LEFT JOIN dbref.t_prigiat_${b} pg ON a.kdkp=pg.kdkp AND a.kdpp=pg.kdpp AND a.kdpn=pg.kdpn`),f&&("2"===f||"3"===f)&&g.push(` LEFT JOIN dbref.t_priproy_${b} pri ON a.kdproy=pri.kdproy`),g.join("")}buildJenlap1GroupBy(a){let{pnradio:b,ppradio:c,kegiatanprioritasradio:d,priradio:e}=a,f=[];return f.push("a.kdpn","a.kdpp","a.kdkp","a.kdproy"),f}deduplicateJoins(a){if(!a||""===a.trim())return"";let b=a.split(" LEFT JOIN ").filter(a=>""!==a.trim()),c=new Set;return(b.forEach(a=>{if(""!==a.trim()){let b=a.trim().replace(/\s+/g," ");c.add(b)}}),0===c.size)?"":" LEFT JOIN "+Array.from(c).join(" LEFT JOIN ")}deduplicateGroupByFields(a){if(!a||0===a.length)return[];let b=new Set,c=[];return a.forEach(a=>{if(a&&""!==a.trim()){let d=a.trim().replace(/\s+/g," "),e=d.toLowerCase();b.has(e)||(b.add(e),c.push(d))}}),c}buildQuery(a){let{dynamicFrom:b,dynamicSelect:c}=this.buildDynamicFromAndSelect(a),d=this.filterBuilder.buildAllFilters(a),e=this.filterBuilder.buildWhereClause(a);if("1"===a.jenlap){let a="a.kdpn <>'00'";e=e&&""!==e.trim()&&!e.includes("WHERE")?`WHERE ${a} AND (${e.replace(/^WHERE\s*/i,"")})`:e&&e.includes("WHERE")?e.replace(/WHERE\s*/i,`WHERE ${a} AND `):`WHERE ${a}`}if("12"===a.jenlap){let a="a.swasembada <> 'NULL'";e=e&&""!==e.trim()&&!e.includes("WHERE")?`WHERE ${a} AND (${e.replace(/^WHERE\s*/i,"")})`:e&&e.includes("WHERE")?e.replace(/WHERE\s*/i,`WHERE ${a} AND `):`WHERE ${a}`}if("3"===a.jenlap&&a.KdTema&&a.Tema&&"00"!==a.Tema){let b=RegExp(`a\\.kdtema\\s*=\\s*'${a.Tema}'`,"gi");if(e&&b.test(e))e=e.replace(b,`a.kdtema LIKE '%${a.Tema}%'`);else if(e){let b=`a.kdtema LIKE '%${a.Tema}%'`;e.includes("WHERE")?e+=` AND ${b}`:e=`WHERE ${b}`}else e=`WHERE a.kdtema LIKE '%${a.Tema}%'`;if(a.opsikataTema&&""!==a.opsikataTema.trim()&&!RegExp(`tm\\.nmtema\\s*LIKE\\s*'%${a.opsikataTema}%'`,"gi").test(e)){let b=`tm.nmtema LIKE '%${a.opsikataTema}%'`;e&&e.includes("WHERE")?e+=` AND ${b}`:e=`WHERE ${b}`}}if("4"===a.jenlap){let a="(A.inf_intervensi <> 'NULL' or A.inf_pengeluaran <> 'NULL')";e&&""!==e.trim()&&e.includes("WHERE")?e+=` AND ${a}`:e=`WHERE ${a}`}if("5"===a.jenlap){let b="A.STUN_INTERVENSI IS NOT NULL",c=a.Stunting||a.StunIntervensi;c&&""!==c.trim()&&"00"!==c&&(b+=` AND A.STUN_INTERVENSI = '${c}'`),e&&""!==e.trim()&&e.includes("WHERE")?e+=` AND (${b})`:e=`WHERE ${b}`}if("6"===a.jenlap){let a="a.kemiskinan_ekstrim <> 'NULL'";e&&""!==e.trim()&&e.includes("WHERE")?e+=` AND ${a}`:e=`WHERE ${a}`}if("7"===a.jenlap){let a="a.pemilu <> 'NULL'";e&&""!==e.trim()&&e.includes("WHERE")?e+=` AND ${a}`:e=`WHERE ${a}`}if("8"===a.jenlap){let a="a.ikn <> 'NULL'";e&&""!==e.trim()&&e.includes("WHERE")?e+=` AND ${a}`:e=`WHERE ${a}`}if("9"===a.jenlap){let a="a.pangan <> 'NULL'";e&&""!==e.trim()&&e.includes("WHERE")?e+=` AND ${a}`:e=`WHERE ${a}`}if("11"===a.jenlap){let a="A.MBG IS NOT NULL";e&&""!==e.trim()&&e.includes("WHERE")?e+=` AND ${a}`:e=`WHERE ${a}`}let f="";f="1"===a.jenlap||"4"===a.jenlap||"5"===a.jenlap||"6"===a.jenlap||"7"===a.jenlap||"8"===a.jenlap||"9"===a.jenlap||"11"===a.jenlap?d.columns.length>0?d.columns.join(", ")+", "+c:c:d.columns.length>0?d.columns.join(", ")+c:c.substring(1);let g="",h=[...d.groupBy];if("1"===a.jenlap){let b=this.buildJenlap1GroupBy(a);b.length>0&&(h.length=0,h.push(...b))}if(a.jenlap,"4"===a.jenlap){let b=this.buildJenlap4GroupBy(a);b.length>0&&h.push(...b)}if("5"===a.jenlap){let b=this.buildJenlap5GroupBy(a);b.length>0&&h.push(...b)}"6"===a.jenlap&&h.push("a.kemiskinan_ekstrim"),"7"===a.jenlap&&h.push("a.pemilu"),"8"===a.jenlap&&h.push("a.ikn"),"9"===a.jenlap&&h.push("a.pangan"),"12"===a.jenlap&&h.push("a.swasembada");let i=this.deduplicateGroupByFields(h);i.length>0&&(g=`GROUP BY ${i.join(", ")}`);let j=d.joinClauses.join("");return"1"===a.jenlap&&(j+=this.buildJenlap1JoinClauses(a),j=this.deduplicateJoins(j)),"4"===a.jenlap&&(j+=this.buildJenlap4JoinClauses(a),j=this.deduplicateJoins(j)),"5"===a.jenlap&&(j+=this.buildJenlap5JoinClauses(a),j=this.deduplicateJoins(j)),`
      SELECT ${f}
      FROM ${b}${j}
      ${e}
      ${g}
    `.trim()}validateQuery(a){let b=[],c=[];a&&""!==a.trim()||b.push("Query is empty"),a.includes("FROM")||b.push("Query missing FROM clause"),a.includes("SELECT")||b.push("Query missing SELECT clause"),a.includes("a_pagu_real_bkpk_dja_")&&(a.includes("a.kdpn")||c.push("Jenlap 1 query should include kdpn field"),a.includes("a.kdpn <>'00'")||c.push("Jenlap 1 query should filter out kdpn='00'"),a.includes("GROUP BY a.kdpn,a.kdpp,a.kdkp,a.kdproy")||c.push("Jenlap 1 query should group by kdpn, kdpp, kdkp, kdproy")),a.includes("A.inf_intervensi")&&a.includes("A.inf_pengeluaran")&&(a.includes("A.inf_intervensi <> 'NULL' or A.inf_pengeluaran <> 'NULL'")||c.push("Jenlap 4 query should filter for non-NULL inflasi values"),a.includes("GROUP BY A.inf_intervensi,A.inf_pengeluaran")||c.push("Jenlap 4 query should group by inf_intervensi, inf_pengeluaran")),a.includes("A.STUN_INTERVENSI")&&(a.includes("A.STUN_INTERVENSI IS NOT NULL")||c.push("Jenlap 5 query should filter for non-NULL stunting values"),a.includes("GROUP BY A.STUN_INTERVENSI")||c.push("Jenlap 5 query should group by STUN_INTERVENSI"),a.includes("a_pagu_real_bkpk_dja_")&&a.includes("_stunting")||c.push("Jenlap 5 query should use stunting table"),a.includes("A.STUN_INTERVENSI = '")&&!a.includes("IS NOT NULL AND A.STUN_INTERVENSI = '")&&c.push("Jenlap 5 query should include both NOT NULL and specific selection conditions")),a.includes("a.kemiskinan_ekstrim")&&(a.includes("a.kemiskinan_ekstrim <> 'NULL'")||c.push("Jenlap 6 query should filter for non-NULL kemiskinan values"),a.includes("GROUP BY a.kemiskinan_ekstrim")||c.push("Jenlap 6 query should group by kemiskinan_ekstrim"),a.includes("a_pagu_real_bkpk_dja_")||c.push("Jenlap 6 query should use bkpk_dja table")),[/;\s*drop\s+table/i,/;\s*delete\s+from/i,/;\s*update\s+.*\s+set/i,/union\s+select/i].forEach(c=>{c.test(a)&&b.push("Potentially dangerous SQL pattern detected")});let d=(a.match(/LEFT JOIN/gi)||[]).length;d>10&&c.push(`High number of JOINs (${d}). Query may be slow.`);let e=(a.match(/AND|OR/gi)||[]).length;return e>15&&c.push(`High number of WHERE conditions (${e}). Query may be slow.`),{isValid:0===b.length,errors:b,warnings:c,stats:{queryLength:a.length,joinCount:d,whereConditions:e}}}getQueryPerformanceMetrics(a){let b=performance.now(),c=this.buildQuery(a),d=performance.now(),e=this.validateQuery(c),f=this.filterBuilder.getFilterStats(a);return{query:c,buildTime:d-b,validation:e,filterStats:f,recommendations:this.generatePerformanceRecommendations(e,f)}}generatePerformanceRecommendations(a,b){let c=[];return b.enabledFilters>8&&c.push("Consider reducing the number of active filters for better performance"),a.stats.joinCount>8&&c.push("High number of table JOINs detected. Consider using indexed columns"),a.stats.queryLength>5e3&&c.push("Query is very long. Consider breaking it into smaller queries"),b.whereConditionsCount>12&&c.push("Many WHERE conditions detected. Ensure proper indexing on filtered columns"),c}generateSqlPreview(a){let{dynamicFrom:b,dynamicSelect:c}=this.buildDynamicFromAndSelect(a),d=this.filterBuilder.buildAllFilters(a),e=this.filterBuilder.buildWhereClause(a);return{fromClause:b,selectClause:c,columns:d.columns,joinClauses:d.joinClauses,whereClause:e,groupBy:d.groupBy,filterStats:this.filterBuilder.getFilterStats(a)}}buildJenlap4InflasiColumns(a){let{inflasiradio:b,infintervensiradio:c,jenistampilanradio:d}=a,e=[],f=b||c||d||"1";if(f&&"4"!==f)switch(f){case"1":e.push("A.inf_intervensi");break;case"2":e.push("A.inf_intervensi","rip.ur_inf_pengeluaran");break;case"3":e.push("rip.ur_inf_pengeluaran")}else e.push("A.inf_intervensi");return e.push("A.inf_pengeluaran"),e.join(",")}buildJenlap4JoinClauses(a){let{thang:b,inflasiradio:c,infintervensiradio:d,jenistampilanradio:e}=a,f=[],g=c||d||e||"1";return g&&("2"===g||"3"===g)&&f.push(" LEFT JOIN DBREF.REF_INF_PENGELUARAN rip ON A.inf_pengeluaran=rip.inf_pengeluaran"),f.join("")}buildJenlap4GroupBy(a){let{inflasiradio:b,infintervensiradio:c,jenistampilanradio:d}=a,e=[],f=b||c||d||"1";if(f&&"4"!==f)switch(f){case"1":e.push("A.inf_intervensi");break;case"2":e.push("A.inf_intervensi","rip.ur_inf_pengeluaran");break;case"3":e.push("rip.ur_inf_pengeluaran")}else e.push("A.inf_intervensi");return e.push("A.inf_pengeluaran"),e}buildJenlap5StuntingColumns(a){let{stuntingradio:b,jenistampilanradio:c}=a,d=[],e=b||c||"1";if(e&&"4"!==e)switch(e){case"1":d.push("A.STUN_INTERVENSI");break;case"2":d.push("A.STUN_INTERVENSI","rst.ur_stun_intervensi");break;case"3":d.push("rst.ur_stun_intervensi")}else d.push("A.STUN_INTERVENSI");return d.join(",")}buildJenlap5JoinClauses(a){let{thang:b,stuntingradio:c,jenistampilanradio:d}=a,e=[],f=c||d||"1";return f&&("2"===f||"3"===f)&&e.push(" LEFT JOIN DBREF.REF_STUNTING_INTERVENSI rst ON A.STUN_INTERVENSI=rst.stun_intervensi"),e.join("")}buildJenlap5GroupBy(a){let{stuntingradio:b,jenistampilanradio:c}=a,d=[],e=b||c||"1";if(e&&"4"!==e)switch(e){case"1":d.push("A.STUN_INTERVENSI");break;case"2":d.push("A.STUN_INTERVENSI","rst.ur_stun_intervensi");break;case"3":d.push("rst.ur_stun_intervensi")}else d.push("A.STUN_INTERVENSI");return d}}let b3=()=>{let a=function(){let{role:a,telp:b,verified:c,loadingExcell:d,setloadingExcell:e,kdkppn:g,kdkanwil:i,settampilAI:j}=(0,f.useContext)(h.A),[k,l]=(0,f.useState)(!1),[m,n]=(0,f.useState)(!1),[o,p]=(0,f.useState)(!1),[q,r]=(0,f.useState)(!1),[s,t]=(0,f.useState)(!1),[u,v]=(0,f.useState)(!1),[w,x]=(0,f.useState)(!1),[y,z]=(0,f.useState)(!1),[A,B]=(0,f.useState)(!1),[C,D]=(0,f.useState)(!1),[E,F]=(0,f.useState)(!1),[G,H]=(0,f.useState)(!1),[I,J]=(0,f.useState)("1"),[K,L]=(0,f.useState)(new Date().getFullYear().toString()),[M,N]=(0,f.useState)(!1),[O,P]=(0,f.useState)("0"),[Q,R]=(0,f.useState)("1"),[S,T]=(0,f.useState)("0"),[U,V]=(0,f.useState)("pdf"),[W,X]=(0,f.useState)(!1),[Y,Z]=(0,f.useState)(!1),[$,_]=(0,f.useState)(!1),[aa,ab]=(0,f.useState)(!0),[ac,ad]=(0,f.useState)(!1),[ae,af]=(0,f.useState)(!1),[ag,ah]=(0,f.useState)(!1),[ai,aj]=(0,f.useState)(!1),[ak,al]=(0,f.useState)(!1),[am,an]=(0,f.useState)(!1),[ao,ap]=(0,f.useState)(!1),[aq,ar]=(0,f.useState)(!1),[as,at]=(0,f.useState)(!1),[au,av]=(0,f.useState)(!1),[aw,ax]=(0,f.useState)(!1),[ay,az]=(0,f.useState)(!1),[aA,aB]=(0,f.useState)(!1),[aC,aD]=(0,f.useState)(!1),[aE,aF]=(0,f.useState)(!1),[aG,aH]=(0,f.useState)(!1),[aI,aJ]=(0,f.useState)(!1),[aK,aL]=(0,f.useState)(!1),[aM,aN]=(0,f.useState)(!1),[aO,aP]=(0,f.useState)(!1),[aQ,aR]=(0,f.useState)(!1),[aS,aT]=(0,f.useState)(!1),[aU,aV]=(0,f.useState)(!1),[aW,aX]=(0,f.useState)(!1),[aY,aZ]=(0,f.useState)(!1),[a$,a_]=(0,f.useState)(!1),[a0,a1]=(0,f.useState)(!1),[a2,a3]=(0,f.useState)(!1),[a4,a5]=(0,f.useState)(!1),[a6,a7]=(0,f.useState)(!1),[a8,a9]=(0,f.useState)(!1),[ba,bb]=(0,f.useState)("000"),[bc,bd]=(0,f.useState)(""),[be,bf]=(0,f.useState)(""),[bg,bh]=(0,f.useState)("XX"),[bi,bj]=(0,f.useState)(""),[bk,bl]=(0,f.useState)(""),[bm,bn]=(0,f.useState)("XX"),[bo,bp]=(0,f.useState)(""),[bq,br]=(0,f.useState)(""),[bs,bt]=(0,f.useState)("XX"),[bu,bv]=(0,f.useState)(""),[bw,bx]=(0,f.useState)(""),[by,bz]=(0,f.useState)("XX"),[bA,bB]=(0,f.useState)(""),[bC,bD]=(0,f.useState)(""),[bE,bF]=(0,f.useState)("XX"),[bG,bH]=(0,f.useState)(""),[bI,bJ]=(0,f.useState)(""),[bK,bL]=(0,f.useState)("XX"),[bM,bN]=(0,f.useState)(""),[bO,bP]=(0,f.useState)(""),[bQ,bR]=(0,f.useState)("XX"),[bS,bT]=(0,f.useState)(""),[bU,bV]=(0,f.useState)(""),[bW,bX]=(0,f.useState)("XX"),[bY,bZ]=(0,f.useState)(""),[b$,b_]=(0,f.useState)(""),[b0,b1]=(0,f.useState)("XX"),[b2,b3]=(0,f.useState)(""),[b4,b5]=(0,f.useState)(""),[b6,b7]=(0,f.useState)("XX"),[b8,b9]=(0,f.useState)(""),[ca,cb]=(0,f.useState)(""),[cc,cd]=(0,f.useState)("XX"),[ce,cf]=(0,f.useState)(""),[cg,ch]=(0,f.useState)(""),[ci,cj]=(0,f.useState)("XX"),[ck,cl]=(0,f.useState)(""),[cm,cn]=(0,f.useState)(""),[co,cp]=(0,f.useState)("XX"),[cq,cr]=(0,f.useState)(""),[cs,ct]=(0,f.useState)(""),[cu,cv]=(0,f.useState)("XX"),[cw,cx]=(0,f.useState)(""),[cy,cz]=(0,f.useState)(""),[cA,cB]=(0,f.useState)("XX"),[cC,cD]=(0,f.useState)(""),[cE,cF]=(0,f.useState)(""),[cG,cH]=(0,f.useState)("AKUN"),[cI,cJ]=(0,f.useState)(""),[cK,cL]=(0,f.useState)(""),[cM,cN]=(0,f.useState)("XX"),[cO,cP]=(0,f.useState)(""),[cQ,cR]=(0,f.useState)(""),[cS,cT]=(0,f.useState)("XX"),[cU,cV]=(0,f.useState)(""),[cW,cX]=(0,f.useState)(""),[cY,cZ]=(0,f.useState)("XX"),[c$,c_]=(0,f.useState)("XX"),[c0,c1]=(0,f.useState)("XX"),[c2,c3]=(0,f.useState)("XX"),[c4,c5]=(0,f.useState)("XX"),[c6,c7]=(0,f.useState)("XX"),[c8,c9]=(0,f.useState)("XX"),[da,db]=(0,f.useState)("XX"),[dc,dd]=(0,f.useState)("XX"),[de,df]=(0,f.useState)("XX"),[dg,dh]=(0,f.useState)("XX"),[di,dj]=(0,f.useState)("XX"),[dk,dl]=(0,f.useState)("1"),[dm,dn]=(0,f.useState)("1"),[dp,dq]=(0,f.useState)("1"),[dr,ds]=(0,f.useState)("1"),[dt,du]=(0,f.useState)("1"),[dv,dw]=(0,f.useState)("1"),[dx,dy]=(0,f.useState)("1"),[dz,dA]=(0,f.useState)("1"),[dB,dC]=(0,f.useState)("1"),[dD,dE]=(0,f.useState)("1"),[dF,dG]=(0,f.useState)("1"),[dH,dI]=(0,f.useState)("1"),[dJ,dK]=(0,f.useState)("1"),[dL,dM]=(0,f.useState)("1"),[dN,dO]=(0,f.useState)("1"),[dP,dQ]=(0,f.useState)("1"),[dR,dS]=(0,f.useState)("1"),[dT,dU]=(0,f.useState)("1"),[dV,dW]=(0,f.useState)("1"),[dX,dY]=(0,f.useState)("1"),[dZ,d$]=(0,f.useState)("1"),[d_,d0]=(0,f.useState)("1"),[d1,d2]=(0,f.useState)("1"),[d3,d4]=(0,f.useState)("1"),[d5,d6]=(0,f.useState)("1"),[d7,d8]=(0,f.useState)("1"),[d9,ea]=(0,f.useState)("1"),[eb,ec]=(0,f.useState)("1"),[ed,ee]=(0,f.useState)("1"),[ef,eg]=(0,f.useState)("1"),[eh,ei]=(0,f.useState)("1"),[ej,ek]=(0,f.useState)("pilihdept"),[el,em]=(0,f.useState)("pilihInflasi"),[en,eo]=(0,f.useState)("pilihikn"),[ep,eq]=(0,f.useState)("pilihKemiskinan"),[er,es]=(0,f.useState)(""),[et,eu]=(0,f.useState)(""),[ev,ew]=(0,f.useState)(", round(sum(a.pagu),0) as PAGU, round(sum(a.real1),0) as REALISASI, round(sum(a.blokir) ,0) as BLOKIR"),[ex,ey]=(0,f.useState)("XX"),[ez,eA]=(0,f.useState)("1");return{role:a,telp:b,verified:c,loadingExcell:d,setloadingExcell:e,kodekppn:g,kodekanwil:i,settampilAI:j,showModal:k,setShowModal:l,showModalKedua:m,setShowModalKedua:n,showModalsql:o,setShowModalsql:p,showModalApbn:q,setShowModalApbn:r,showModalAkumulasi:s,setShowModalAkumulasi:t,showModalBulanan:u,setShowModalBulanan:v,showModalBlokir:w,setShowModalBlokir:x,showModalPN:y,setShowModalPN:z,showModalPN2:A,setShowModalPN2:B,showModalJnsblokir:C,setShowModalJnsblokir:D,showModalPDF:E,setShowModalPDF:F,showModalsimpan:G,setShowModalsimpan:H,jenlap:I,setJenlap:J,thang:K,setThang:L,tanggal:M,setTanggal:N,cutoff:O,setCutoff:P,pembulatan:Q,setPembulatan:R,akumulatif:S,setAkumulatif:T,selectedFormat:U,setSelectedFormat:V,export2:W,setExport2:X,loadingStatus:Y,setLoadingStatus:Z,showFormatDropdown:$,setShowFormatDropdown:_,kddept:aa,setKddept:ab,unit:ac,setUnit:ad,kddekon:ae,setKddekon:af,kdlokasi:ag,setKdlokasi:ah,kdkabkota:ai,setKdkabkota:aj,kdkanwil:ak,setKdkanwil:al,kdkppn:am,setKdkppn:an,kdsatker:ao,setKdsatker:ap,kdfungsi:aq,setKdfungsi:ar,kdsfungsi:as,setKdsfungsi:at,kdprogram:au,setKdprogram:av,kdgiat:aw,setKdgiat:ax,kdoutput:ay,setKdoutput:az,kdsoutput:aA,setKdsoutput:aB,kdkomponen:aC,setKdkomponen:aD,kdskomponen:aE,setKdskomponen:aF,kdakun:aG,setKdakun:aH,kdsdana:aI,setKdsdana:aJ,kdregister:aK,setKdregister:aL,kdInflasi:aM,setKdInflasi:aN,kdIkn:aO,setKdIkn:aP,kdKemiskinan:aQ,setKdKemiskinan:aR,KdPRI:aS,setKdPRI:aT,KdPangan:aU,setKdPangan:aV,KdStunting:aW,setKdStunting:aX,KdPemilu:aY,setKdPemilu:aZ,KdTema:a$,setKdTema:a_,KdPN:a0,setKdPN:a1,KdPP:a2,setKdPP:a3,KdKegPP:a4,setKdKegPP:a5,KdMP:a6,setKdMP:a7,KdMBG:a8,setKdMBG:a9,setKdMP:a7,dept:ba,setDept:bb,deptkondisi:bc,setDeptkondisi:bd,katadept:be,setKatadept:bf,kdunit:bg,setKdunit:bh,unitkondisi:bi,setUnitkondisi:bj,kataunit:bk,setKataunit:bl,dekon:bm,setDekon:bn,dekonkondisi:bo,setDekonkondisi:bp,katadekon:bq,setKatadekon:br,prov:bs,setProv:bt,lokasikondisi:bu,setLokasikondisi:bv,katalokasi:bw,setKatalokasi:bx,kabkota:by,setKabkota:bz,kabkotakondisi:bA,setKabkotakondisi:bB,katakabkota:bC,setKatakabkota:bD,kanwil:bE,setKanwil:bF,kanwilkondisi:bG,setKanwilkondisi:bH,katakanwil:bI,setKatakanwil:bJ,kppn:bK,setKppn:bL,kppnkondisi:bM,setKppnkondisi:bN,katakppn:bO,setKatakppn:bP,satker:bQ,setSatker:bR,satkerkondisi:bS,setSatkerkondisi:bT,katasatker:bU,setKatasatker:bV,fungsi:bW,setFungsi:bX,fungsikondisi:bY,setFungsikondisi:bZ,katafungsi:b$,setKatafungsi:b_,sfungsi:b0,setSfungsi:b1,subfungsikondisi:b2,setSubfungsikondisi:b3,katasubfungsi:b4,setKatasubfungsi:b5,program:b6,setProgram:b7,programkondisi:b8,setProgramkondisi:b9,kataprogram:ca,setKataprogram:cb,giat:cc,setGiat:cd,giatkondisi:ce,setGiatkondisi:cf,katagiat:cg,setKatagiat:ch,output:ci,setOutput:cj,outputkondisi:ck,setOutputkondisi:cl,kataoutput:cm,setKataoutput:cn,soutput:co,setsOutput:cp,soutputkondisi:cq,setSoutputkondisi:cr,katasoutput:cs,setKatasoutput:ct,komponen:cu,setKomponen:cv,komponenkondisi:cw,setKomponenkondisi:cx,katakomponen:cy,setKatakomponen:cz,skomponen:cA,setSkomponen:cB,skomponenkondisi:cC,setSkomponenkondisi:cD,kataskomponen:cE,setKataskomponen:cF,akun:cG,setAkun:cH,akunkondisi:cI,setAkunkondisi:cJ,kataakun:cK,setKataakun:cL,sdana:cM,setSdana:cN,sdanakondisi:cO,setSdanakondisi:cP,katasdana:cQ,setKatasdana:cR,register:cS,setRegister:cT,registerkondisi:cU,setRegisterkondisi:cV,kataregister:cW,setKataregister:cX,PN:cY,setPN:cZ,PP:c$,setPP:c_,PRI:c0,setPRI:c1,MP:c2,setMP:c3,Tema:c4,setTema:c5,Inflasi:c6,setInflasi:c7,Stunting:c8,setStunting:c9,Miskin:da,setMiskin:db,Pemilu:dc,setPemilu:dd,Ikn:de,setIkn:df,Pangan:dg,setPangan:dh,mbg:di,setmbg:dj,deptradio:dk,setDeptradio:dl,unitradio:dm,setUnitradio:dn,dekonradio:dp,setDekonradio:dq,locradio:dr,setLocradio:ds,kabkotaradio:dt,setKabkotaradio:du,kanwilradio:dv,setKanwilradio:dw,kppnradio:dx,setKppnradio:dy,satkerradio:dz,setSatkerradio:dA,fungsiradio:dB,setFungsiradio:dC,subfungsiradio:dD,setSubfungsiradio:dE,programradio:dF,setProgramradio:dG,kegiatanradio:dH,setKegiatanradio:dI,outputradio:dJ,setOutputradio:dK,soutputradio:dL,setsOutputradio:dM,komponenradio:dN,setKomponenradio:dO,skomponenradio:dP,setSkomponenradio:dQ,akunradio:dR,setAkunradio:dS,sdanaradio:dT,setSdanaradio:dU,registerradio:dV,setRegisterradio:dW,inflasiradio:dX,setInflasiradio:dY,iknradio:dZ,setIknradio:d$,kemiskinanradio:d_,setKemiskinanradio:d0,priradio:d1,setPriradio:d2,panganradio:d3,setPanganradio:d4,stuntingradio:d5,setStuntingradio:d6,pemiluradio:d7,setPemiluradio:d8,pnradio:d9,setPnradio:ea,ppradio:eb,setPpradio:ec,mpradio:ed,setMpradio:ee,temaradio:ef,setTemaradio:eg,mbgradio:eh,setmbgradio:ei,opsidept:ej,setOpsidept:ek,opsiInflasi:el,setOpsiInflasi:em,opsiIkn:en,setOpsiIkn:eo,opsiKemiskinan:ep,setOpsiKemiskinan:eq,sql:er,setSql:es,from:et,setFrom:eu,select:ev,setSelect:ew,kegiatanprioritas:ex,setKegiatanPrioritas:ey,kegiatanprioritasradio:ez,setKegiatanPrioritasRadio:eA}}(),{statusLogin:b,token:c,axiosJWT:i}=(0,f.useContext)(h.A),{buildQuery:j}=function(a){let[b,c]=(0,f.useState)({}),d=(0,f.useMemo)(()=>new b2,[]),{thang:e,jenlap:g,cutoff:h,tanggal:i,akumulatif:j,pembulatan:k,setFrom:l,setSelect:m,setSql:n}=a,o=()=>{try{let b=d.buildQuery(a),c=d.generateSqlPreview(a);return l&&l(c.fromClause),m&&m(c.selectClause),n&&n(b),b}catch(a){return console.error("Error building query:",a),""}},p=()=>d.getQueryPerformanceMetrics(a),q=()=>d.generateSqlPreview(a),r=(a=o)=>d.validateQuery(a),s=()=>d.filterBuilder.getFilterStats(a),t=b=>d.filterBuilder.isFilterEnabled(b,a),u=b=>d.filterBuilder.buildFilter(b,a),v=a=>{let b=u(a),c=t(a);return{filterName:a,isEnabled:c,...b}},w=b=>{let c={...a,jenlap:b},e=d.buildQuery(c),f=d.validateQuery(e);return{jenlapValue:b,query:e,validation:f,preview:d.generateSqlPreview(c)}};return{buildQuery:o,getBuildQuery:()=>o,generateSqlPreview:q,validateQuery:r,getQueryPerformanceMetrics:p,getFilterStats:s,analyzeQueryComplexity:()=>{let a=p(),b=s();return{complexity:{low:b.enabledFilters<=3&&a.validation.stats.joinCount<=3,medium:b.enabledFilters<=6&&a.validation.stats.joinCount<=6,high:b.enabledFilters>6||a.validation.stats.joinCount>6},metrics:a,stats:b,recommendations:a.recommendations}},isFilterEnabled:t,getAvailableFilters:()=>d.filterBuilder.getAvailableFilters(),buildFilter:u,debugFilter:v,debugSpecialFilters:()=>{let{jenlap:b}=a;if("1"===b);else if("7"===b);else if("8"===b)return v("blokir");else if(parseInt(b)>=9&&12>=parseInt(b))return["inflasi","stunting","kemiskinan","pemilu","ikn","pangan","specialgrouping"].map(a=>v(a));return[]},debugJenlap:w,testAllJenlaps:()=>{let a={};for(let b=1;b<=12;b++)a[b]=w(b.toString());return a},getCachedQuery:a=>b[a],setCachedQuery:(a,b)=>{c(c=>({...c,[a]:{query:b,timestamp:Date.now()}}))},clearQueryCache:()=>{c({})},generateSqlPreview:q,generateOptimizedSql:()=>o,parseAdvancedConditions:(a,b)=>new d.filterBuilder.filters.department.constructor().parseKondisiConditions(a),optimizeGroupBy:(a,b)=>[...new Set(b)].filter(b=>a.some(a=>a.includes(b)||b.includes("a."))),optimizeJoins:a=>d.filterBuilder.optimizeJoins(Array.isArray(a)?a:[a]),validateQuery:r,getQueryPerformanceMetrics:p,getQueryStats:s}}(a),{role:k,telp:l,verified:m,loadingExcell:n,setloadingExcell:o,kodekppn:p,kodekanwil:q,settampilAI:s,showModal:t,setShowModal:u,showModalKedua:v,setShowModalKedua:w,showModalsql:x,setShowModalsql:y,showModalPDF:z,setShowModalPDF:A,showModalsimpan:C,setShowModalsimpan:D,jenlap:E,setJenlap:F,thang:G,setThang:H,tanggal:I,setTanggal:J,cutoff:K,setCutoff:M,pembulatan:O,setPembulatan:P,akumulatif:Q,setAkumulatif:R,selectedFormat:S,setSelectedFormat:T,export2:U,setExport2:V,loadingStatus:W,setLoadingStatus:X,showFormatDropdown:Y,setShowFormatDropdown:Z,kddept:$,setKddept:_,unit:ab,setUnit:ac,kddekon:ad,setKddekon:ae,kdlokasi:af,setKdlokasi:ag,kdkabkota:ah,setKdkabkota:ai,kdkanwil:aj,setKdkanwil:ak,kdkppn:al,setKdkppn:am,kdsatker:an,setKdsatker:ao,kdfungsi:ap,setKdfungsi:aq,kdsfungsi:ar,setKdsfungsi:as,kdprogram:at,setKdprogram:au,kdgiat:av,setKdgiat:aw,kdoutput:ax,setKdoutput:ay,kdsoutput:az,setKdsoutput:aA,kdkomponen:aB,setKdkomponen:aC,kdskomponen:aD,setKdskomponen:aE,kdakun:aF,setKdakun:aG,kdsdana:aH,setKdsdana:aI,kdregister:aJ,setKdregister:aK,kdInflasi:aL,setKdInflasi:aM,kdIkn:aN,setKdIkn:aO,kdKemiskinan:aP,setKdKemiskinan:aQ,KdPRI:aR,setKdPRI:aS,KdPangan:aT,setKdPangan:aU,KdPemilu:aV,setKdPemilu:aW,KdStunting:aY,setKdStunting:aZ,KdTema:a$,setKdTema:a_,KdPN:a0,setKdPN:a3,KdPP:a4,setKdPP:a5,KdMP:a6,setKdMP:a7,KdKegPP:a8,setKdKegPP:a9,Pangan:ba,setPangan:bb,Pemilu:bd,setPemilu:be,dept:bf,setDept:bh,deptkondisi:bi,setDeptkondisi:bk,katadept:bl,setKatadept:bm,kdunit:bo,setKdunit:bp,unitkondisi:bq,setUnitkondisi:bs,kataunit:bt,setKataunit:bv,dekon:bw,setDekon:bx,dekonkondisi:by,setDekonkondisi:bz,katadekon:bA,setKatadekon:bB,prov:bC,setProv:bD,lokasikondisi:bE,setLokasikondisi:bF,katalokasi:bG,setKatalokasi:bH,kabkota:bI,setKabkota:bJ,kabkotakondisi:bK,setKabkotakondisi:bL,katakabkota:bM,setKatakabkota:bN,kanwil:bO,setKanwil:bP,kanwilkondisi:bQ,setKanwilkondisi:bR,katakanwil:bS,setKatakanwil:bT,kppn:bU,setKppn:bV,kppnkondisi:bW,setKppnkondisi:bX,katakppn:bY,setKatakppn:bZ,satker:b$,setSatker:b_,satkerkondisi:b0,setSatkerkondisi:b1,katasatker:b3,setKatasatker:b4,fungsi:b5,setFungsi:b6,fungsikondisi:b7,setFungsikondisi:b8,katafungsi:b9,setKatafungsi:ca,sfungsi:cb,setSfungsi:cc,subfungsikondisi:cd,setSubfungsikondisi:ce,katasubfungsi:cf,setKatasubfungsi:cg,program:ch,setProgram:ci,programkondisi:cj,setProgramkondisi:ck,kataprogram:cl,setKataprogram:cm,giat:cn,setGiat:co,giatkondisi:cp,setGiatkondisi:cq,katagiat:cr,setKatagiat:cs,output:ct,setOutput:cu,outputkondisi:cv,setOutputkondisi:cw,kataoutput:cx,setKataoutput:cy,soutput:cz,setsOutput:cA,soutputkondisi:cB,setSoutputkondisi:cC,katasoutput:cD,setKatasoutput:cE,komponen:cF,setKomponen:cG,komponenkondisi:cH,setKomponenkondisi:cI,katakomponen:cJ,setKatakomponen:cK,skomponen:cL,setSkomponen:cM,skomponenkondisi:cN,setSkomponenkondisi:cO,kataskomponen:cP,setKataskomponen:cQ,akun:cR,setAkun:cS,akunkondisi:cT,setAkunkondisi:cU,kataakun:cV,setKataakun:cW,sdana:cX,setSdana:cY,sdanakondisi:cZ,setSdanakondisi:c$,katasdana:c_,setKatasdana:c0,register:c1,setRegister:c2,registerkondisi:c3,setRegisterkondisi:c4,kataregister:c5,setKataregister:c6,PN:c7,setPN:c8,PP:c9,setPP:da,PRI:db,setPRI:dc,MP:dd,setMP:de,Tema:df,setTema:dg,Inflasi:dh,setInflasi:di,Stunting:dj,setStunting:dk,Miskin:dl,setMiskin:dm,Ikn:dn,setIkn:dp,deptradio:dq,setDeptradio:dr,unitradio:ds,setUnitradio:dt,dekonradio:du,setDekonradio:dv,locradio:dw,setLocradio:dx,kabkotaradio:dy,setKabkotaradio:dz,kanwilradio:dA,setKanwilradio:dB,kppnradio:dC,setKppnradio:dD,satkerradio:dE,setSatkerradio:dF,fungsiradio:dG,setFungsiradio:dH,subfungsiradio:dI,setSubfungsiradio:dJ,programradio:dK,setProgramradio:dL,kegiatanradio:dM,setKegiatanradio:dN,outputradio:dO,setOutputradio:dP,soutputradio:dQ,setsOutputradio:dR,komponenradio:dS,setKomponenradio:dT,skomponenradio:dU,setSkomponenradio:dV,akunradio:dW,setAkunradio:dX,sdanaradio:dY,setSdanaradio:dZ,registerradio:d$,setRegisterradio:d_,inflasiradio:d0,setInflasiradio:d1,iknradio:d2,setIknradio:d3,kemiskinanradio:d4,setKemiskinanradio:d5,pnradio:d6,setPnradio:d7,ppradio:d8,setPpradio:d9,mpradio:ea,setMpradio:eb,temaradio:ec,setTemaradio:ed,panganradio:ee,setPanganradio:ef,stuntingradio:eg,setStuntingradio:eh,pemiluradio:ei,setPemiluradio:ej,priradio:ek,setPriradio:el,opsiInflasi:em,setOpsiInflasi:en,opsiIkn:eo,setOpsiIkn:ep,opsiKemiskinan:eq,setOpsiKemiskinan:er,kegiatanprioritas:es,setKegiatanPrioritas:et,kegiatanprioritasradio:eu,setKegiatanPrioritasRadio:ev,sql:ew,setSql:ex,from:ey,setFrom:ez,select:eA,setSelect:eB,akunType:eC,akunValue:eD,akunSql:eE}=a,eF=()=>{let a=j();return"string"==typeof a&&a.length,a},eG=async()=>{let b=eF();a.setSql(b),u(!0)};g().useEffect(()=>{j()},[G,K,O,Q]);let eH=g().useRef(!1);g().useEffect(()=>{if(!eH.current){eH.current=!0;return}cc("XX"),ce(""),cg("")},[b5]);let[eI,eJ]=g().useState("pilihmp"),[eK,eL]=g().useState("0"!==K),[eM,eN]=g().useState(!1);async function eO(){let a=eF();if(!a||"string"!=typeof a||""===a.trim())return(0,e.qs)("Query tidak valid, silakan cek filter dan parameter."),console.error("Export aborted: SQL query is empty or invalid.",{sql:a}),[];if(!b)return[];try{let b=await i.post("http://localhost:88/next/inquiry",{sql:a,page:1},{headers:{Authorization:`Bearer ${c}`}});if(b.data&&Array.isArray(b.data.data))return b.data.data;return[]}catch(a){return console.error("Export API error:",a),a&&a.response&&console.error("[Export Debug] Backend error response:",a.response.data),[]}}let eP=async()=>{o(!0);try{let a=await eO();if(!a||!a.length){(0,e.qs)("Tidak ada data untuk diexport"),o(!1);return}await L(a,"inquiry_data.xlsx"),(0,e.qs)("Data berhasil diexport ke Excel")}catch(a){(0,e.qs)("Gagal export Excel")}o(!1)},eQ=async()=>{o(!0);try{let a=await eO();if(!a||!a.length){(0,e.qs)("Tidak ada data untuk diexport"),o(!1);return}!function(a,b="data.csv"){if(!a||!a.length)return;let c=(a,b)=>null==b?"":b,d=Object.keys(a[0]),e=new Blob([[d.join(","),...a.map(a=>d.map(b=>JSON.stringify(a[b],c)).join(","))].join("\r\n")],{type:"text/csv"}),f=URL.createObjectURL(e),g=document.createElement("a");g.setAttribute("href",f),g.setAttribute("download",b),g.style.visibility="hidden",document.body.appendChild(g),g.click(),document.body.removeChild(g),URL.revokeObjectURL(f)}(a,"inquiry_data.csv"),(0,e.qs)("Data berhasil diexport ke CSV")}catch(a){(0,e.qs)("Gagal export CSV")}o(!1)};return g().useEffect(()=>{j()},[eC,eD,eE]),(0,d.jsxs)("div",{className:"w-full",children:[(0,d.jsxs)("div",{className:"xl:px-8 p-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Inquiry Data Tematik APBN"}),(0,d.jsx)(a2,{inquiryState:{jenlap:E,setJenlap:F,pembulatan:O,setPembulatan:P,akumulatif:Q,setAkumulatif:R,thang:G,setThang:H}}),"1"===E&&(0,d.jsx)(bc,{inquiryState:a}),"2"===E&&(0,d.jsx)(bg,{inquiryState:a}),"3"===E&&(0,d.jsx)(bj,{inquiryState:a}),"4"===E&&(0,d.jsx)(bn,{inquiryState:a}),"5"===E&&(0,d.jsx)(br,{inquiryState:a}),"11"===E&&(0,d.jsx)(bu,{inquiryState:a}),(0,d.jsx)(aX,{inquiryState:{jenlap:E,tanggal:I,setTanggal:J,cutoff:K,setCutoff:M,showCutoffSelector:eK,setShowCutoffSelector:eL,akumulatif:Q,setAkumulatif:R,kddept:$,setKddept:_,unit:ab,setUnit:ac,kddekon:ad,setKddekon:ae,kdlokasi:af,setKdlokasi:ag,kdkabkota:ah,setKdkabkota:ai,kdkanwil:aj,setKdkanwil:ak,kdkppn:al,setKdkppn:am,kdsatker:an,setKdsatker:ao,kdfungsi:ap,setKdfungsi:aq,kdsfungsi:ar,setKdsfungsi:as,kdprogram:at,setKdprogram:au,kdgiat:av,setKdgiat:aw,kdoutput:ax,setKdoutput:ay,kdsoutput:az,setKdsoutput:aA,kdkomponen:aB,setKdkomponen:aC,kdskomponen:aD,setKdskomponen:aE,kdakun:aF,setKdakun:aG,kdsdana:aH,setKdsdana:aI,kdregister:aJ,setKdregister:aK,dept:bf,setDept:bh,deptkondisi:bi,setDeptkondisi:bk,katadept:bl,setKatadept:bm,deptradio:dq,setDeptradio:dr,kdunit:bo,setKdunit:bp,unitkondisi:bq,setUnitkondisi:bs,kataunit:bt,setKataunit:bv,unitradio:ds,setUnitradio:dt,prov:bC,setProv:bD,lokasikondisi:bE,setLokasikondisi:bF,katalokasi:bG,setKatalokasi:bH,locradio:dw,setLocradio:dx,dekon:bw,setDekon:bx,dekonkondisi:by,setDekonkondisi:bz,katadekon:bA,setKatadekon:bB,dekonradio:du,setDekonradio:dv,kabkota:bI,setKabkota:bJ,kabkotakondisi:bK,setKabkotakondisi:bL,katakabkota:bM,setKatakabkota:bN,kabkotaradio:dy,setKabkotaradio:dz,kanwil:bO,setKanwil:bP,kanwilkondisi:bQ,setKanwilkondisi:bR,katakanwil:bS,setKatakanwil:bT,kanwilradio:dA,setKanwilradio:dB,kppn:bU,setKppn:bV,kppnkondisi:bW,setKppnkondisi:bX,katakppn:bY,setKatakppn:bZ,kppnradio:dC,setKppnradio:dD,satker:b$,setSatker:b_,satkerkondisi:b0,setSatkerkondisi:b1,katasatker:b3,setKatasatker:b4,satkerradio:dE,setSatkerradio:dF,fungsi:b5,setFungsi:b6,fungsikondisi:b7,setFungsikondisi:b8,katafungsi:b9,setKatafungsi:ca,fungsiradio:dG,setFungsiradio:dH,sfungsi:cb,setSfungsi:cc,subfungsikondisi:cd,setSubfungsikondisi:ce,katasubfungsi:cf,setKatasubfungsi:cg,subfungsiradio:dI,setSubfungsiradio:dJ,KdPRI:aR,setKdPRI:aS,KdPangan:aT,setKdPangan:aU,KdPemilu:aV,setKdPemilu:aW,KdStunting:aY,setKdStunting:aZ,KdTema:a$,setKdTema:a_,KdPN:a0,setKdPN:a3,KdPP:a4,setKdPP:a5,KdMP:a6,setKdMP:a7,KdKegPP:a8,setKdKegPP:a9,kegiatanprioritas:es,setKegiatanPrioritas:et,kegiatanprioritasradio:eu,setKegiatanPrioritasRadio:ev,program:ch,setProgram:ci,programkondisi:cj,setProgramkondisi:ck,kataprogram:cl,setKataprogram:cm,programradio:dK,setProgramradio:dL,giat:cn,setGiat:co,giatkondisi:cp,setGiatkondisi:cq,katagiat:cr,setKatagiat:cs,kegiatanradio:dM,setKegiatanradio:dN,output:ct,setOutput:cu,outputkondisi:cv,setOutputkondisi:cw,kataoutput:cx,setKataoutput:cy,outputradio:dO,setOutputradio:dP,soutput:cz,setsOutput:cA,soutputkondisi:cB,setSoutputkondisi:cC,katasoutput:cD,setKatasoutput:cE,soutputradio:dQ,setsOutputradio:dR,komponen:cF,setKomponen:cG,komponenkondisi:cH,setKomponenkondisi:cI,katakomponen:cJ,setKatakomponen:cK,komponenradio:dS,setKomponenradio:dT,skomponen:cL,setSkomponen:cM,skomponenkondisi:cN,setSkomponenkondisi:cO,kataskomponen:cP,setKataskomponen:cQ,skomponenradio:dU,setSkomponenradio:dV,akun:cR,setAkun:cS,akunkondisi:cT,setAkunkondisi:cU,kataakun:cV,setKataakun:cW,akunradio:dW,setAkunradio:dX,sdana:cX,setSdana:cY,sdanakondisi:cZ,setSdanakondisi:c$,katasdana:c_,setKatasdana:c0,sdanaradio:dY,setSdanaradio:dZ,register:c1,setRegister:c2,registerkondisi:c3,setRegisterkondisi:c4,kataregister:c5,setKataregister:c6,registerradio:d$,setRegisterradio:d_,kdInflasi:aL,setKdInflasi:aM,Inflasi:dh,setInflasi:di,inflasiradio:d0,setInflasiradio:d1,opsiInflasi:em,setOpsiInflasi:en,kdIkn:aN,setKdIkn:aO,Ikn:dn,setIkn:dp,iknradio:d2,setIknradio:d3,opsiIkn:eo,setOpsiIkn:ep,kdKemiskinan:aP,setKdKemiskinan:aQ,Miskin:dl,setMiskin:dm,kemiskinanradio:d4,setKemiskinanradio:d5,opsiKemiskinan:eq,setOpsiKemiskinan:er,Pangan:ba,setPangan:bb,panganradio:ee,setPanganradio:ef,Stunting:dj,setStunting:dk,stuntingradio:eg,setStuntingradio:eh,Pemilu:bd,setPemilu:be,pemiluradio:ei,setPemiluradio:ej,PN:c7,setPN:c8,pnradio:d6,setPnradio:d7,PP:c9,setPP:da,ppradio:d8,setPpradio:d9,MP:dd,setMP:de,mpradio:ea,setMpradio:eb,Tema:df,setTema:dg,temaradio:ec,setTemaradio:ed,PRI:db,setPRI:dc,priradio:ek,setPriradio:el}}),(0,d.jsx)("div",{className:"my-3 sm:px-16",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row md:flex-wrap lg:flex-nowrap gap-2 border-2 dark:border-zinc-600 rounded-xl shadow-sm py-2 px-4 font-mono tracking-wide bg-zinc-100 dark:bg-black",children:[(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-blue-600 ml-4",children:"Tahun Anggaran:"}),(0,d.jsx)("span",{className:"ml-2",children:G})]}),(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-green-600 ml-4",children:"Jenis Laporan:"}),(0,d.jsx)("span",{className:"ml-2",children:"1"===E?"Prioritas Nasional":"2"===E?"Major Project":"3"===E?"Tematik Anggaran":"4"===E?"Inflasi":"5"===E?"Penanganan Stunting":"6"===E?"Belanja Pemilu":"7"===E?"Kemiskinan Ekstrim":"8"===E?"Ibu Kota Nusantara":"9"===E?"Ketahanan Pangan":"10"===E?"Bantuan Pemerintah":"11"===E?"Makanan Bergizi Gratis":"12"===E?"Swasembada Pangan":"Unknown"})]}),(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-purple-600 ml-4",children:"Pembulatan:"}),(0,d.jsx)("span",{className:"ml-2",children:"1"===O?"Rupiah":"1000"===O?"Ribuan":"1000000"===O?"Jutaan":"1000000000"===O?"Miliaran":"Triliunan"})]}),(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-orange-600 ml-4",children:"Filter Aktif:"}),(0,d.jsxs)("span",{className:"ml-2",children:[[I,$,ab,ad,af,ah,aj,al,an,ap,ar,at,av,ax,az,aB,aD,aF,aH,aJ,aL,aN,aP,aR,aT,aV,aY,a$,a0,a4,a6,a8].filter(Boolean).length," ","dari"," ",32]})]})]})}),(0,d.jsx)(a1,{onExecuteQuery:eG,onExportExcel:eP,onExportCSV:eQ,onExportPDF:()=>{A(!0)},onReset:()=>{F("1"),H(new Date().getFullYear().toString()),J(!1),_(!0),ac(!1),ae(!1),ag(!1),ai(!1),ak(!1),am(!1),ao(!1),aq(!1),as(!1),au(!1),aw(!1),ay(!1),aA(!1),aC(!1),aE(!1),aG(!1),aI(!1),aK(!1),aM(!1),aO(!1),aQ(!1),aS(!1),aU(!1),aW(!1),aZ(!1),a_(!1),a3(!1),a5(!1),a7(!1),a9(!1),R("0"),M("0"),eL(!1),c8("XX"),da("XX"),dc("XX"),de("XX"),dg("XX"),di("XX"),dk("XX"),dm("XX"),be("XX"),dp("XX"),bb("XX"),et("XX"),bh("000"),bp("XX"),bx("XX"),bD("XX"),bJ("XX"),bL(""),bN(""),bP("XX"),bV("XX"),bX(""),bZ(""),b_("XX"),b1(""),b4(""),b6("XX"),b8(""),ca(""),cc("XX"),ce(""),cg(""),ci("XX"),co("XX"),cu("XX"),cA("XX"),cG("XX"),cM("XX"),cS("XX"),cY("XX"),c2("XX"),P("1"),dr("1"),dt("1"),dv("1"),dx("1"),dz("1"),dB("1"),dD("1"),dF("1"),dH("1"),dJ("1"),dL("1"),dN("1"),dP("1"),dR("1"),dT("1"),dV("1"),dX("1"),dZ("1"),d_("1"),d1("1"),d3("1"),d5("1"),d7("1"),d9("1"),eb("1"),ed("1"),ef("1"),eh("1"),ej("1"),el("1"),ev("1"),en("pilihInflasi"),ep("pilihikn"),er("pilihKemiskinan"),ex(""),ez(""),eB(", round(sum(a.pagu),0) as PAGU, round(sum(a.real1),0) as REALISASI, round(sum(a.blokir) ,0) as BLOKIR")},isLoading:n,onSaveQuery:()=>eN(!0),onShowSQL:()=>{let b=eF();a.setSql(b),y(!0)}})]}),x&&(0,d.jsx)(r,{isOpen:x,onClose:()=>{y(!1),window.scrollTo({top:0,behavior:"smooth"})},query:ew}),t&&(0,d.jsx)(aa,{isOpen:t,onClose:()=>{u(!1),D(!1),window.scrollTo({top:0,behavior:"smooth"})},sql:ew,from:ey,thang:G,pembulatan:O}),C&&(0,d.jsx)(B,{isOpen:C,onClose:()=>{D(!1),window.scrollTo({top:0,behavior:"smooth"})},sql:ew}),z&&(0,d.jsx)(N,{showModalPDF:z,setShowModalPDF:A,selectedFormat:S,setSelectedFormat:T,fetchExportData:eO,filename:"inquiry_data",loading:n}),eM&&(0,d.jsx)(B,{isOpen:eM,onClose:()=>eN(!1),query:ew,thang:G,queryType:"INQUIRY"})]})},b4=()=>(0,d.jsx)(b3,{})},94735:a=>{"use strict";a.exports=require("events")},97654:(a,b,c)=>{Promise.resolve().then(c.bind(c,93509))}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,3598,9161,6159,6942,9697,901,2793,4229],()=>b(b.s=81410));module.exports=c})();