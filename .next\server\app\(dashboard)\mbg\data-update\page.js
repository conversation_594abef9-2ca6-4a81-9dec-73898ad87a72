(()=>{var a={};a.id=8416,a.ids=[8416],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19758:(a,b,c)=>{Promise.resolve().then(c.bind(c,54457))},21532:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\sintesaNEXT2\\\\src\\\\app\\\\(dashboard)\\\\mbg\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\mbg\\loading.tsx","default")},21820:a=>{"use strict";a.exports=require("os")},24444:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["mbg",{children:["data-update",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,74814)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\mbg\\data-update\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(c.bind(c,67239)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\mbg\\data-update\\loading.tsx"]}]},{loading:[()=>Promise.resolve().then(c.bind(c,21532)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\mbg\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,8413)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\layout.jsx"],loading:[()=>Promise.resolve().then(c.bind(c,48221)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\loading.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,53361)),"D:\\sintesaNEXT2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\sintesaNEXT2\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["D:\\sintesaNEXT2\\src\\app\\(dashboard)\\mbg\\data-update\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/mbg/data-update/page",pathname:"/mbg/data-update",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/mbg/data-update/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30550:(a,b,c)=>{Promise.resolve().then(c.bind(c,72770))},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41894:(a,b,c)=>{"use strict";c.d(b,{d:()=>j});var d=c(98869),e=c(62948),f=c(87223),g=c(79910),h=c(60687),i=(0,e.Rf)((a,b)=>{var c;let{as:e,className:i,children:j,...k}=a,l=(0,f.zD)(b),{slots:m,classNames:n}=(0,d.f)(),o=(0,g.$z)(null==n?void 0:n.header,i);return(0,h.jsx)(e||"div",{ref:l,className:null==(c=m.header)?void 0:c.call(m,{class:o}),...k,children:j})});i.displayName="HeroUI.CardHeader";var j=i},54457:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i});var d=c(60687),e=c(77317),f=c(85015),g=c(36220),h=c(41894);function i(){return(0,d.jsxs)("div",{className:"my-3 px-4 lg:px-6 max-w-[95rem] mx-auto w-full flex flex-col gap-4",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(e.m,{className:"h-9 w-32 rounded-lg"}),(0,d.jsx)(e.m,{className:"h-4 w-80 rounded-lg"})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(e.m,{className:"h-10 w-32 rounded-lg"}),(0,d.jsx)(e.m,{className:"h-10 w-28 rounded-lg"})]})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:Array.from({length:4}).map((a,b)=>(0,d.jsx)(f.Z,{children:(0,d.jsx)(g.U,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(e.m,{className:"h-10 w-10 rounded-lg"}),(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)(e.m,{className:"h-3 w-20 rounded"}),(0,d.jsx)(e.m,{className:"h-6 w-12 rounded"})]})]})})},b))}),(0,d.jsx)(f.Z,{children:(0,d.jsx)(g.U,{className:"p-4",children:(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,d.jsx)(e.m,{className:"h-10 w-full rounded-lg"}),(0,d.jsx)(e.m,{className:"h-10 w-48 rounded-lg"}),(0,d.jsx)(e.m,{className:"h-10 w-20 rounded-lg"})]})})}),(0,d.jsxs)(f.Z,{children:[(0,d.jsx)(h.d,{children:(0,d.jsx)(e.m,{className:"h-6 w-32 rounded-lg"})}),(0,d.jsxs)(g.U,{className:"p-0",children:[(0,d.jsxs)("div",{className:"overflow-x-auto",children:[(0,d.jsxs)("div",{className:"grid grid-cols-7 gap-4 p-4 border-b border-divider",children:[(0,d.jsx)(e.m,{className:"h-4 w-20 rounded"}),(0,d.jsx)(e.m,{className:"h-4 w-16 rounded"}),(0,d.jsx)(e.m,{className:"h-4 w-14 rounded"}),(0,d.jsx)(e.m,{className:"h-4 w-16 rounded"}),(0,d.jsx)(e.m,{className:"h-4 w-16 rounded"}),(0,d.jsx)(e.m,{className:"h-4 w-20 rounded"}),(0,d.jsx)(e.m,{className:"h-4 w-12 rounded"})]}),Array.from({length:8}).map((a,b)=>(0,d.jsxs)("div",{className:"grid grid-cols-7 gap-4 p-4 border-b border-divider",children:[(0,d.jsx)(e.m,{className:"h-4 w-32 rounded"}),(0,d.jsx)(e.m,{className:"h-4 w-24 rounded"}),(0,d.jsx)(e.m,{className:"h-6 w-16 rounded-full"}),(0,d.jsx)(e.m,{className:"h-4 w-20 rounded"}),(0,d.jsx)(e.m,{className:"h-4 w-12 rounded"}),(0,d.jsx)(e.m,{className:"h-4 w-18 rounded"}),(0,d.jsxs)("div",{className:"flex gap-1",children:[(0,d.jsx)(e.m,{className:"h-6 w-6 rounded"}),(0,d.jsx)(e.m,{className:"h-6 w-6 rounded"})]})]},b))]}),(0,d.jsxs)("div",{className:"flex justify-between items-center p-4",children:[(0,d.jsx)(e.m,{className:"h-4 w-32 rounded"}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(e.m,{className:"h-8 w-8 rounded"}),(0,d.jsx)(e.m,{className:"h-8 w-8 rounded"}),(0,d.jsx)(e.m,{className:"h-8 w-8 rounded"}),(0,d.jsx)(e.m,{className:"h-8 w-8 rounded"})]})]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,d.jsxs)(f.Z,{children:[(0,d.jsx)(h.d,{children:(0,d.jsx)(e.m,{className:"h-6 w-48 rounded-lg"})}),(0,d.jsx)(g.U,{children:(0,d.jsx)("div",{className:"space-y-3",children:Array.from({length:5}).map((a,b)=>(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)(e.m,{className:"h-4 w-24 rounded"}),(0,d.jsx)(e.m,{className:"h-4 w-16 rounded"})]},b))})})]}),(0,d.jsxs)(f.Z,{children:[(0,d.jsx)(h.d,{children:(0,d.jsx)(e.m,{className:"h-6 w-40 rounded-lg"})}),(0,d.jsx)(g.U,{children:(0,d.jsx)("div",{className:"space-y-4",children:Array.from({length:4}).map((a,b)=>(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,d.jsx)(e.m,{className:"h-4 w-32 rounded"}),(0,d.jsx)(e.m,{className:"h-4 w-16 rounded"})]}),(0,d.jsx)(e.m,{className:"h-2 w-full rounded-full"})]},b))})})]})]})]})}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},59078:(a,b,c)=>{Promise.resolve().then(c.bind(c,67239))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67239:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\sintesaNEXT2\\\\src\\\\app\\\\(dashboard)\\\\mbg\\\\data-update\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\mbg\\data-update\\loading.tsx","default")},67502:(a,b,c)=>{Promise.resolve().then(c.bind(c,21532))},72770:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j});var d=c(60687);c(43210);var e=c(10218),f=c(77317),g=c(85015),h=c(36220),i=c(41894);function j({key:a}){let{theme:b}=(0,e.D)();return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)("div",{className:"h-full lg:px-6 pt-4",children:(0,d.jsxs)("div",{className:"flex flex-col gap-2 pt-2 px-4 lg:px-0 max-w-[90rem] mx-auto w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4",children:[(0,d.jsx)("div",{}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-3",children:["get-kddept"===a&&(0,d.jsx)(f.m,{className:"w-full sm:w-44 lg:w-52 h-10 rounded-lg"}),"get-kanwil"===a&&(0,d.jsx)(f.m,{className:"w-full sm:w-44 lg:w-52 h-10 rounded-lg"})]})]}),"get-dipa"===a&&(0,d.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 mt-4 md:mt-6",children:Array.from({length:4}).map((a,b)=>(0,d.jsx)(g.Z,{className:"border-none shadow-sm bg-gradient-to-br from-default-50 to-default-100",children:(0,d.jsx)(h.U,{className:"p-3 md:p-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 md:gap-3 min-w-0 flex-1",children:[(0,d.jsx)(f.m,{className:"w-10 h-10 md:w-12 md:h-12 rounded-xl flex-shrink-0"}),(0,d.jsxs)("div",{className:"min-w-0 flex-1 space-y-2",children:[(0,d.jsx)(f.m,{className:"h-3 w-20 rounded"}),(0,d.jsx)(f.m,{className:"h-5 w-24 rounded"})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[(0,d.jsx)(f.m,{className:"h-4 w-4 rounded"}),(0,d.jsx)(f.m,{className:"h-3 w-8 rounded"})]})]})})},b))})," ","belanja-terbesar"===a&&(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:Array.from({length:4}).map((a,b)=>(0,d.jsx)(g.Z,{className:"bg-gradient-to-br from-default-50 to-default-100",children:(0,d.jsx)(h.U,{className:"p-6",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(f.m,{className:"w-12 h-12 rounded-lg"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(f.m,{className:"h-3 w-16 rounded"}),(0,d.jsx)(f.m,{className:"h-6 w-12 rounded"})]})]})})},b))}),"performa-terbesar"===a&&(0,d.jsxs)(g.Z,{className:`border-none shadow-sm ${"dark"===b?"bg-gradient-to-br from-slate-800 to-slate-700":"bg-gradient-to-br from-slate-100 to-slate-200"} lg:col-span-6 xl:col-span-3`,children:[(0,d.jsxs)(i.d,{className:"pb-2 px-4 md:px-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,d.jsx)(f.m,{className:"h-4 w-32 rounded"}),(0,d.jsx)(f.m,{className:"h-3 w-16 rounded"})]}),"LOADING"]}),(0,d.jsx)(h.U,{className:"pt-0 px-4 md:px-6",children:(0,d.jsx)("div",{className:"space-y-4",children:Array.from({length:4}).map((a,b)=>(0,d.jsx)("div",{className:"flex items-center justify-between p-0.5 md:p-1 rounded-lg",children:(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("div",{className:"mb-2",children:(0,d.jsx)(f.m,{className:"h-3 w-24 rounded"})}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:"relative flex-1",children:(0,d.jsx)(f.m,{className:"h-5 w-full rounded-full"})}),(0,d.jsx)(f.m,{className:"h-6 w-12 rounded-full"})]})]})},b))})})]})]})})})}},74075:a=>{"use strict";a.exports=require("zlib")},74814:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(37413);c(61120),function(){var a=Error("Cannot find module '@/components/mbg'");throw a.code="MODULE_NOT_FOUND",a}();let e=async()=>(await new Promise(a=>setTimeout(a,2e3)),(0,d.jsx)(Object(function(){var a=Error("Cannot find module '@/components/mbg'");throw a.code="MODULE_NOT_FOUND",a}()),{}))},77317:(a,b,c)=>{"use strict";c.d(b,{m:()=>k});var d=c(62948),e=(0,c(98462).tv)({slots:{base:["group","relative","overflow-hidden","bg-content3 dark:bg-content2","pointer-events-none","before:opacity-100","before:absolute","before:inset-0","before:-translate-x-full","before:animate-shimmer","before:border-t","before:border-content4/30","before:bg-gradient-to-r","before:from-transparent","before:via-content4","dark:before:via-default-700/10","before:to-transparent","after:opacity-100","after:absolute","after:inset-0","after:-z-10","after:bg-content3","dark:after:bg-content2","data-[loaded=true]:pointer-events-auto","data-[loaded=true]:overflow-visible","data-[loaded=true]:!bg-transparent","data-[loaded=true]:before:opacity-0 data-[loaded=true]:before:-z-10 data-[loaded=true]:before:animate-none","data-[loaded=true]:after:opacity-0"],content:["opacity-0","group-data-[loaded=true]:opacity-100"]},variants:{disableAnimation:{true:{base:"before:animate-none before:transition-none after:transition-none",content:"transition-none"},false:{base:"transition-background !duration-300",content:"transition-opacity motion-reduce:transition-none !duration-300"}}},defaultVariants:{}}),f=c(79910),g=c(43210),h=c(58445),i=c(60687),j=(0,d.Rf)((a,b)=>{let{Component:c,children:j,getSkeletonProps:k,getContentProps:l}=function(a){var b,c;let i=(0,h.o)(),[j,k]=(0,d.rE)(a,e.variantKeys),{as:l,children:m,isLoaded:n=!1,className:o,classNames:p,...q}=j,r=null!=(c=null!=(b=a.disableAnimation)?b:null==i?void 0:i.disableAnimation)&&c,s=(0,g.useMemo)(()=>e({...k,disableAnimation:r}),[(0,f.t6)(k),r,m]),t=(0,f.$z)(null==p?void 0:p.base,o);return{Component:l||"div",children:m,slots:s,classNames:p,getSkeletonProps:(a={})=>({"data-loaded":(0,f.sE)(n),className:s.base({class:(0,f.$z)(t,null==a?void 0:a.className)}),...q}),getContentProps:(a={})=>({className:s.content({class:(0,f.$z)(null==p?void 0:p.content,null==a?void 0:a.className)})})}}({...a});return(0,i.jsx)(c,{ref:b,...k(),children:(0,i.jsx)("div",{...l(),children:j})})});j.displayName="HeroUI.Skeleton";var k=j},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,3598,9697,901],()=>b(b.s=24444));module.exports=c})();