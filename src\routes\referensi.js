import Jo<PERSON> from "joi";

// Reference data routes
export default async function referensiRoutes(fastify, options) {
  // Backward compatibility route for old Express format
  fastify.post(
    "/",
    {
      schema: {
        body: {
          type: "object",
          required: ["query"],
          properties: {
            query: { type: "string", description: "Encrypted SQL query" },
          },
        },
        response: {
          200: {
            type: "object",
            properties: {
              result: { type: "array" },
            },
          },
        },
      },
      preHandler: [fastify.verifyToken],
    },
    async (request, reply) => {
      try {
        const { query } = request.body;

        if (!query) {
          return reply.code(400).send({
            success: false,
            message: "Query parameter is required",
          });
        }

        // Decrypt the query using the same method as old Express backend
        let decryptedQuery;
        try {
          decryptedQuery = fastify.encryption.decrypt(query);
          // Remove quotes if present (matching old behavior)
          decryptedQuery = decryptedQuery.replace(/"/g, "");
        } catch (decryptError) {
          fastify.log.error("Query decryption error:", decryptError);
          return reply.code(400).send({
            success: false,
            message: "Failed to decrypt query",
          });
        }

        fastify.log.info(
          "Executing legacy query:",
          decryptedQuery.substring(0, 100) + "..."
        );

        // Execute the SQL query directly (matching old Express behavior)
        const [results] = await fastify.db.query(decryptedQuery);

        // Return in the same format as old Express backend
        return reply.send({
          result: results,
        });
      } catch (error) {
        fastify.log.error("Legacy referensi query error:", error);

        // Match old Express error handling
        const errorMessage =
          error.sqlMessage ||
          error.message ||
          "Terjadi kesalahan dalam memproses permintaan.";

        return reply.code(500).send({
          error: errorMessage,
        });
      }
    }
  );

  // Get all reference data categories
  fastify.get(
    "/categories",
    {
      schema: {
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              data: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    id: { type: "string" },
                    name: { type: "string" },
                    description: { type: "string" },
                    table_name: { type: "string" },
                    is_active: { type: "boolean" },
                    created_at: { type: "string" },
                    updated_at: { type: "string" },
                  },
                },
              },
            },
          },
        },
      },
      preHandler: [fastify.authenticate],
    },
    async (request, reply) => {
      try {
        const categoriesQuery = `
        SELECT id, name, description, table_name, is_active, created_at, updated_at
        FROM reference_categories 
        WHERE is_active = 1
        ORDER BY name ASC
      `;

        const [categories] = await fastify.db.query(categoriesQuery);

        return reply.send({
          success: true,
          message: "Reference categories retrieved successfully",
          data: categories,
        });
      } catch (error) {
        fastify.log.error("Get reference categories error:", error);
        return reply.code(500).send({
          success: false,
          message: "Failed to retrieve reference categories",
        });
      }
    }
  );

  // Get reference data by category
  fastify.get(
    "/category/:categoryId",
    {
      schema: {
        params: {
          type: "object",
          required: ["categoryId"],
          properties: {
            categoryId: { type: "string" },
          },
        },
        querystring: {
          type: "object",
          properties: {
            page: { type: "integer", minimum: 1, default: 1 },
            limit: { type: "integer", minimum: 1, maximum: 1000, default: 100 },
            search: { type: "string" },
            sortBy: { type: "string", default: "name" },
            sortOrder: {
              type: "string",
              enum: ["ASC", "DESC"],
              default: "ASC",
            },
            active: { type: "boolean" },
          },
        },
      },
      preHandler: [fastify.authenticate],
    },
    async (request, reply) => {
      try {
        const { categoryId } = request.params;
        const { page, limit, search, sortBy, sortOrder, active } =
          request.query;
        const offset = (page - 1) * limit;

        // Get category information
        const categoryQuery = `
        SELECT id, name, description, table_name, is_active
        FROM reference_categories 
        WHERE id = ? AND is_active = 1
      `;

        const [categories] = await fastify.db.query(categoryQuery, [
          categoryId,
        ]);

        if (categories.length === 0) {
          return reply.code(404).send({
            success: false,
            message: "Reference category not found",
          });
        }

        const category = categories[0];
        const tableName = category.table_name;

        // Build dynamic query based on table structure
        let whereConditions = [];
        let queryParams = [];

        // Add search condition if provided
        if (search) {
          // Get table columns to search in
          const columnsQuery = `
          SELECT COLUMN_NAME 
          FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
          AND DATA_TYPE IN ('varchar', 'text', 'char')
        `;

          const [columns] = await fastify.db.query(columnsQuery, [tableName]);

          if (columns.length > 0) {
            const searchConditions = columns.map(
              (col) => `${col.COLUMN_NAME} LIKE ?`
            );
            whereConditions.push(`(${searchConditions.join(" OR ")})`);
            columns.forEach(() => queryParams.push(`%${search}%`));
          }
        }

        // Add active filter if provided
        if (active !== undefined) {
          whereConditions.push("is_active = ?");
          queryParams.push(active ? 1 : 0);
        }

        const whereClause =
          whereConditions.length > 0
            ? `WHERE ${whereConditions.join(" AND ")}`
            : "";

        // Get total count
        const countQuery = `SELECT COUNT(*) as total FROM ${tableName} ${whereClause}`;
        const [countResults] = await fastify.db.query(countQuery, queryParams);
        const totalCount = countResults[0].total;

        // Get paginated data
        const dataQuery = `
        SELECT * FROM ${tableName} 
        ${whereClause}
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT ? OFFSET ?
      `;

        const [referenceData] = await fastify.db.query(dataQuery, [
          ...queryParams,
          limit,
          offset,
        ]);

        const totalPages = Math.ceil(totalCount / limit);

        return reply.send({
          success: true,
          message: "Reference data retrieved successfully",
          data: {
            category: {
              id: category.id,
              name: category.name,
              description: category.description,
            },
            items: referenceData,
            pagination: {
              page,
              limit,
              total: totalCount,
              totalPages,
              hasNext: page < totalPages,
              hasPrev: page > 1,
            },
          },
        });
      } catch (error) {
        fastify.log.error("Get reference data error:", error);
        return reply.code(500).send({
          success: false,
          message: "Failed to retrieve reference data",
        });
      }
    }
  );

  // Search across all reference data
  fastify.get(
    "/search",
    {
      schema: {
        querystring: {
          type: "object",
          required: ["query"],
          properties: {
            query: { type: "string", minLength: 2 },
            categories: { type: "array", items: { type: "string" } },
            limit: { type: "integer", minimum: 1, maximum: 100, default: 50 },
          },
        },
      },
      preHandler: [fastify.authenticate],
    },
    async (request, reply) => {
      try {
        const { query, categories, limit } = request.query;

        // Get active reference categories
        let categoriesFilter = "";
        let categoryParams = [];

        if (categories && categories.length > 0) {
          categoriesFilter =
            "AND id IN (" + categories.map(() => "?").join(",") + ")";
          categoryParams = categories;
        }

        const categoriesQuery = `
        SELECT id, name, table_name
        FROM reference_categories 
        WHERE is_active = 1 ${categoriesFilter}
        ORDER BY name ASC
      `;

        const [activeCategories] = await fastify.db.query(
          categoriesQuery,
          categoryParams
        );

        const searchResults = [];

        // Search in each category table
        for (const category of activeCategories) {
          try {
            // Get searchable columns for this table
            const columnsQuery = `
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?
            AND DATA_TYPE IN ('varchar', 'text', 'char')
            LIMIT 5
          `;

            const [columns] = await fastify.db.query(columnsQuery, [
              category.table_name,
            ]);

            if (columns.length > 0) {
              const searchConditions = columns.map(
                (col) => `${col.COLUMN_NAME} LIKE ?`
              );
              const searchParams = columns.map(() => `%${query}%`);

              const searchQuery = `
              SELECT *, '${category.name}' as category_name, '${category.id}' as category_id
              FROM ${category.table_name} 
              WHERE (${searchConditions.join(" OR ")}) AND is_active = 1
              LIMIT 10
            `;

              const [results] = await fastify.db.query(
                searchQuery,
                searchParams
              );

              if (results.length > 0) {
                searchResults.push({
                  category: {
                    id: category.id,
                    name: category.name,
                  },
                  items: results,
                });
              }
            }
          } catch (tableError) {
            fastify.log.warn(
              `Error searching in table ${category.table_name}:`,
              tableError.message
            );
          }
        }

        // Limit total results
        const limitedResults = searchResults.slice(0, Math.ceil(limit / 10));

        return reply.send({
          success: true,
          message: "Search completed successfully",
          data: {
            query,
            results: limitedResults,
            totalCategories: limitedResults.length,
            totalItems: limitedResults.reduce(
              (sum, cat) => sum + cat.items.length,
              0
            ),
          },
        });
      } catch (error) {
        fastify.log.error("Reference search error:", error);
        return reply.code(500).send({
          success: false,
          message: "Search failed",
        });
      }
    }
  );

  // Get specific reference item by ID
  fastify.get(
    "/item/:categoryId/:itemId",
    {
      schema: {
        params: {
          type: "object",
          required: ["categoryId", "itemId"],
          properties: {
            categoryId: { type: "string" },
            itemId: { type: "string" },
          },
        },
      },
      preHandler: [fastify.authenticate],
    },
    async (request, reply) => {
      try {
        const { categoryId, itemId } = request.params;

        // Get category information
        const categoryQuery = `
        SELECT id, name, table_name
        FROM reference_categories 
        WHERE id = ? AND is_active = 1
      `;

        const [categories] = await fastify.db.query(categoryQuery, [
          categoryId,
        ]);

        if (categories.length === 0) {
          return reply.code(404).send({
            success: false,
            message: "Reference category not found",
          });
        }

        const category = categories[0];

        // Get specific item
        const itemQuery = `SELECT * FROM ${category.table_name} WHERE id = ?`;
        const [items] = await fastify.db.query(itemQuery, [itemId]);

        if (items.length === 0) {
          return reply.code(404).send({
            success: false,
            message: "Reference item not found",
          });
        }

        return reply.send({
          success: true,
          message: "Reference item retrieved successfully",
          data: {
            category: {
              id: category.id,
              name: category.name,
            },
            item: items[0],
          },
        });
      } catch (error) {
        fastify.log.error("Get reference item error:", error);
        return reply.code(500).send({
          success: false,
          message: "Failed to retrieve reference item",
        });
      }
    }
  );

  // Create new reference item (admin only)
  fastify.post(
    "/item/:categoryId",
    {
      schema: {
        params: {
          type: "object",
          required: ["categoryId"],
          properties: {
            categoryId: { type: "string" },
          },
        },
        body: {
          type: "object",
          required: ["data"],
          properties: {
            data: { type: "object" },
          },
        },
      },
      preHandler: [fastify.authenticate, fastify.authorize(["admin"])],
    },
    async (request, reply) => {
      try {
        const { categoryId } = request.params;
        const { data } = request.body;
        const userId = request.user.userId;

        // Get category information
        const categoryQuery = `
        SELECT id, name, table_name
        FROM reference_categories 
        WHERE id = ? AND is_active = 1
      `;

        const [categories] = await fastify.db.query(categoryQuery, [
          categoryId,
        ]);

        if (categories.length === 0) {
          return reply.code(404).send({
            success: false,
            message: "Reference category not found",
          });
        }

        const category = categories[0];

        // Add audit fields
        const insertData = {
          ...data,
          created_by: userId,
          created_at: new Date(),
          updated_by: userId,
          updated_at: new Date(),
          is_active: 1,
        };

        // Build insert query
        const columns = Object.keys(insertData);
        const placeholders = columns.map(() => "?").join(",");
        const values = Object.values(insertData);

        const insertQuery = `
        INSERT INTO ${category.table_name} (${columns.join(", ")})
        VALUES (${placeholders})
      `;

        const [result] = await fastify.db.query(insertQuery, values);

        fastify.log.info(
          `New reference item created in ${category.name} by user ${request.user.username}`
        );

        return reply.code(201).send({
          success: true,
          message: "Reference item created successfully",
          data: {
            id: result.insertId,
            category: {
              id: category.id,
              name: category.name,
            },
          },
        });
      } catch (error) {
        fastify.log.error("Create reference item error:", error);
        return reply.code(500).send({
          success: false,
          message: "Failed to create reference item",
        });
      }
    }
  );

  // Update reference item (admin only)
  fastify.put(
    "/item/:categoryId/:itemId",
    {
      schema: {
        params: {
          type: "object",
          required: ["categoryId", "itemId"],
          properties: {
            categoryId: { type: "string" },
            itemId: { type: "string" },
          },
        },
        body: {
          type: "object",
          required: ["data"],
          properties: {
            data: { type: "object" },
          },
        },
      },
      preHandler: [fastify.authenticate, fastify.authorize(["admin"])],
    },
    async (request, reply) => {
      try {
        const { categoryId, itemId } = request.params;
        const { data } = request.body;
        const userId = request.user.userId;

        // Get category information
        const categoryQuery = `
        SELECT id, name, table_name
        FROM reference_categories 
        WHERE id = ? AND is_active = 1
      `;

        const [categories] = await fastify.db.query(categoryQuery, [
          categoryId,
        ]);

        if (categories.length === 0) {
          return reply.code(404).send({
            success: false,
            message: "Reference category not found",
          });
        }

        const category = categories[0];

        // Check if item exists
        const checkQuery = `SELECT id FROM ${category.table_name} WHERE id = ?`;
        const [existingItems] = await fastify.db.query(checkQuery, [itemId]);

        if (existingItems.length === 0) {
          return reply.code(404).send({
            success: false,
            message: "Reference item not found",
          });
        }

        // Add audit fields
        const updateData = {
          ...data,
          updated_by: userId,
          updated_at: new Date(),
        };

        // Build update query
        const updateFields = Object.keys(updateData).map((key) => `${key} = ?`);
        const values = [...Object.values(updateData), itemId];

        const updateQuery = `
        UPDATE ${category.table_name} 
        SET ${updateFields.join(", ")}
        WHERE id = ?
      `;

        await fastify.db.query(updateQuery, values);

        fastify.log.info(
          `Reference item ${itemId} updated in ${category.name} by user ${request.user.username}`
        );

        return reply.send({
          success: true,
          message: "Reference item updated successfully",
        });
      } catch (error) {
        fastify.log.error("Update reference item error:", error);
        return reply.code(500).send({
          success: false,
          message: "Failed to update reference item",
        });
      }
    }
  );

  // Delete reference item (admin only)
  fastify.delete(
    "/item/:categoryId/:itemId",
    {
      schema: {
        params: {
          type: "object",
          required: ["categoryId", "itemId"],
          properties: {
            categoryId: { type: "string" },
            itemId: { type: "string" },
          },
        },
      },
      preHandler: [fastify.authenticate, fastify.authorize(["admin"])],
    },
    async (request, reply) => {
      try {
        const { categoryId, itemId } = request.params;
        const userId = request.user.userId;

        // Get category information
        const categoryQuery = `
        SELECT id, name, table_name
        FROM reference_categories 
        WHERE id = ? AND is_active = 1
      `;

        const [categories] = await fastify.db.query(categoryQuery, [
          categoryId,
        ]);

        if (categories.length === 0) {
          return reply.code(404).send({
            success: false,
            message: "Reference category not found",
          });
        }

        const category = categories[0];

        // Soft delete (set is_active = 0)
        const deleteQuery = `
        UPDATE ${category.table_name} 
        SET is_active = 0, updated_by = ?, updated_at = NOW()
        WHERE id = ?
      `;

        const [result] = await fastify.db.query(deleteQuery, [userId, itemId]);

        if (result.affectedRows === 0) {
          return reply.code(404).send({
            success: false,
            message: "Reference item not found",
          });
        }

        fastify.log.info(
          `Reference item ${itemId} deleted from ${category.name} by user ${request.user.username}`
        );

        return reply.send({
          success: true,
          message: "Reference item deleted successfully",
        });
      } catch (error) {
        fastify.log.error("Delete reference item error:", error);
        return reply.code(500).send({
          success: false,
          message: "Failed to delete reference item",
        });
      }
    }
  );

  // Get reference data statistics
  fastify.get(
    "/stats",
    {
      preHandler: [fastify.authenticate],
    },
    async (request, reply) => {
      try {
        // Get category statistics
        const statsQuery = `
        SELECT 
          rc.id,
          rc.name,
          rc.table_name,
          COUNT(CASE WHEN t.is_active = 1 THEN 1 END) as active_count,
          COUNT(CASE WHEN t.is_active = 0 THEN 1 END) as inactive_count,
          COUNT(*) as total_count
        FROM reference_categories rc
        LEFT JOIN (
          SELECT 'placeholder' as table_ref, is_active FROM ${rc.table_name}
        ) t ON 1=1
        WHERE rc.is_active = 1
        GROUP BY rc.id, rc.name, rc.table_name
        ORDER BY rc.name
      `;

        // Since we can't use dynamic table names in a single query,
        // we'll get categories first and then query each table
        const categoriesQuery = `
        SELECT id, name, table_name
        FROM reference_categories 
        WHERE is_active = 1
        ORDER BY name
      `;

        const [categories] = await fastify.db.query(categoriesQuery);

        const categoryStats = [];
        let totalActiveItems = 0;
        let totalInactiveItems = 0;

        for (const category of categories) {
          try {
            const countQuery = `
            SELECT 
              COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_count,
              COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive_count,
              COUNT(*) as total_count
            FROM ${category.table_name}
          `;

            const [counts] = await fastify.db.query(countQuery);
            const stats = counts[0];

            categoryStats.push({
              id: category.id,
              name: category.name,
              activeCount: stats.active_count,
              inactiveCount: stats.inactive_count,
              totalCount: stats.total_count,
            });

            totalActiveItems += stats.active_count;
            totalInactiveItems += stats.inactive_count;
          } catch (tableError) {
            fastify.log.warn(
              `Error getting stats for table ${category.table_name}:`,
              tableError.message
            );
            categoryStats.push({
              id: category.id,
              name: category.name,
              activeCount: 0,
              inactiveCount: 0,
              totalCount: 0,
              error: "Unable to retrieve statistics",
            });
          }
        }

        return reply.send({
          success: true,
          message: "Reference statistics retrieved successfully",
          data: {
            overview: {
              totalCategories: categories.length,
              totalActiveItems,
              totalInactiveItems,
              totalItems: totalActiveItems + totalInactiveItems,
            },
            categories: categoryStats,
          },
        });
      } catch (error) {
        fastify.log.error("Get reference stats error:", error);
        return reply.code(500).send({
          success: false,
          message: "Failed to retrieve reference statistics",
        });
      }
    }
  );

  // Root POST endpoint for dashboard queries
  fastify.post(
    "/",
    {
      schema: {
        body: {
          type: "object",
          properties: {
            query: { type: "string" },
          },
          required: ["query"],
        },
        response: {
          200: {
            type: "object",
            properties: {
              success: { type: "boolean" },
              message: { type: "string" },
              result: {
                type: "array",
                items: { type: "object" },
              },
            },
          },
        },
      },
      preHandler: [fastify.verifyToken],
    },
    async (request, reply) => {
      try {
        const { query } = request.body;

        // Decrypt the query if it's encrypted
        let decryptedQuery = query;
        try {
          // Use the encryption plugin's decrypt method
          decryptedQuery = fastify.encryption.decrypt(query);
          fastify.log.info("Query decrypted successfully");
        } catch (decryptError) {
          // If decryption fails, assume query is already plain text
          fastify.log.info("Query appears to be plain text, using as-is");
          decryptedQuery = decodeURIComponent(query);
        }

        fastify.log.info(
          `Executing dashboard query: ${decryptedQuery.substring(0, 100)}...`
        );

        // Execute the query
        const [rows] = await fastify.db.query(decryptedQuery);

        return reply.send({
          success: true,
          message: "Query executed successfully",
          result: rows,
        });
      } catch (error) {
        fastify.log.error("Dashboard query error:", error);
        return reply.code(500).send({
          success: false,
          message: "Failed to execute dashboard query",
          error: error.message,
        });
      }
    }
  );
}
