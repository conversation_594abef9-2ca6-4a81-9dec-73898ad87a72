{"name": "sintesa-backend", "version": "1.0.0", "description": "Modern Node.js backend for Sintesa financial dashboard with Fastify", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test-connection": "node test-connection.js", "test-redis": "node test-redis-connection.js", "lint": "eslint src/", "format": "prettier --write src/"}, "keywords": ["fastify", "nodejs", "api", "dashboard", "financial"], "author": "DJPB", "license": "MIT", "dependencies": {"@fastify/cookie": "^9.3.1", "@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/jwt": "^8.0.1", "@fastify/multipart": "^8.3.0", "@fastify/rate-limit": "^9.1.0", "@fastify/static": "^7.0.4", "@fastify/websocket": "^10.0.1", "bcryptjs": "^2.4.3", "crypto-js": "^4.2.0", "csv-writer": "^1.6.0", "dotenv": "^16.4.5", "fastify": "^4.28.1", "fastify-plugin": "^4.5.1", "ioredis": "^5.4.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mysql2": "^3.11.4", "node-cron": "^3.0.3", "pdfkit": "^0.15.0", "pino-pretty": "^13.0.0", "redis": "^4.7.0", "socket.io": "^4.8.1", "winston": "^3.15.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^22.15.30", "eslint": "^9.15.0", "jest": "^29.7.0", "nodemon": "^3.1.7", "prettier": "^3.3.3"}, "engines": {"node": ">=18.0.0"}}