// Test the referensi endpoint
import http from "http";

const postData = JSON.stringify({
  query: "test",
});

const options = {
  hostname: "localhost",
  port: 88,
  path: "/next/referensi",
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "Content-Length": Buffer.byteLength(postData),
  },
};

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  console.log(`Headers:`, res.headers);

  let data = "";
  res.on("data", (chunk) => {
    data += chunk;
  });

  res.on("end", () => {
    console.log("Response:", data);
  });
});

req.on("error", (e) => {
  console.error(`Request error: ${e.message}`);
});

req.write(postData);
req.end();
