import { Server } from 'socket.io';
import fp from 'fastify-plugin';

async function socketPlugin(fastify, options) {
  // Socket.IO configuration
  const io = new Server(fastify.server, {
    cors: {
      origin: [
        'http://localhost:3000',
        'http://localhost:3001',
        process.env.FRONTEND_URL || 'http://localhost:3000'
      ],
      methods: ['GET', 'POST'],
      credentials: true
    },
    transports: ['websocket', 'polling'],
    pingTimeout: 60000,
    pingInterval: 25000,
    upgradeTimeout: 30000,
    maxHttpBufferSize: 1e6 // 1MB
  });

  // Connected clients tracking
  const connectedClients = new Map();
  const activeQueries = new Map();
  const userSessions = new Map();

  // Socket utilities
  const socketUtils = {
    // Broadcast to all clients
    broadcast(event, data) {
      io.emit(event, data);
    },

    // Send to specific user
    sendToUser(userId, event, data) {
      const userSockets = userSessions.get(userId) || [];
      userSockets.forEach(socketId => {
        const socket = connectedClients.get(socketId);
        if (socket) {
          socket.emit(event, data);
        }
      });
    },

    // Send to specific socket
    sendToSocket(socketId, event, data) {
      const socket = connectedClients.get(socketId);
      if (socket) {
        socket.emit(event, data);
      }
    },

    // Broadcast query status
    broadcastQueryStatus(queryId, status, data = {}) {
      io.emit('query_status', {
        queryId,
        status,
        timestamp: new Date().toISOString(),
        ...data
      });
    },

    // Broadcast running queries
    broadcastRunningQueries(queries) {
      io.emit('running_querys', queries);
    },

    // Broadcast query errors
    broadcastQueryError(error) {
      io.emit('error_querys', {
        error: error.message || error,
        timestamp: new Date().toISOString()
      });
    },

    // Get connected users count
    getConnectedUsersCount() {
      return userSessions.size;
    },

    // Get active queries count
    getActiveQueriesCount() {
      return activeQueries.size;
    },

    // Get user sessions
    getUserSessions(userId) {
      return userSessions.get(userId) || [];
    },

    // Check if user is online
    isUserOnline(userId) {
      return userSessions.has(userId) && userSessions.get(userId).length > 0;
    }
  };

  // Connection handler
  io.on('connection', (socket) => {
    fastify.log.info(`Socket connected: ${socket.id}`);
    connectedClients.set(socket.id, socket);

    // Handle user authentication
    socket.on('authenticate', async (data) => {
      try {
        const { token, userId, username } = data;
        
        if (token && userId) {
          // Verify token (optional - can be done on frontend)
          socket.userId = userId;
          socket.username = username;
          socket.authenticated = true;

          // Track user sessions
          if (!userSessions.has(userId)) {
            userSessions.set(userId, []);
          }
          userSessions.get(userId).push(socket.id);

          // Join user room
          socket.join(`user_${userId}`);

          socket.emit('authenticated', {
            success: true,
            message: 'Socket authenticated successfully'
          });

          // Broadcast user online status
          socket.broadcast.emit('user_online', {
            userId,
            username,
            timestamp: new Date().toISOString()
          });

          fastify.log.info(`User ${username} (${userId}) authenticated on socket ${socket.id}`);
        } else {
          socket.emit('authentication_error', {
            success: false,
            message: 'Invalid authentication data'
          });
        }
      } catch (error) {
        fastify.log.error('Socket authentication error:', error);
        socket.emit('authentication_error', {
          success: false,
          message: 'Authentication failed'
        });
      }
    });

    // Handle query start
    socket.on('query_start', (data) => {
      try {
        const { queryId, sql, userId } = data;
        
        activeQueries.set(queryId, {
          id: queryId,
          sql,
          userId,
          socketId: socket.id,
          startTime: new Date().toISOString(),
          status: 'running'
        });

        // Broadcast to all clients
        socketUtils.broadcastRunningQueries(Array.from(activeQueries.values()));
        
        fastify.log.info(`Query started: ${queryId} by user ${userId}`);
      } catch (error) {
        fastify.log.error('Query start error:', error);
      }
    });

    // Handle query progress
    socket.on('query_progress', (data) => {
      try {
        const { queryId, progress, message } = data;
        
        if (activeQueries.has(queryId)) {
          const query = activeQueries.get(queryId);
          query.progress = progress;
          query.message = message;
          query.lastUpdate = new Date().toISOString();
          
          activeQueries.set(queryId, query);
          
          // Broadcast progress
          socketUtils.broadcastQueryStatus(queryId, 'progress', {
            progress,
            message
          });
        }
      } catch (error) {
        fastify.log.error('Query progress error:', error);
      }
    });

    // Handle query completion
    socket.on('query_complete', (data) => {
      try {
        const { queryId, results, executionTime } = data;
        
        if (activeQueries.has(queryId)) {
          const query = activeQueries.get(queryId);
          query.status = 'completed';
          query.endTime = new Date().toISOString();
          query.executionTime = executionTime;
          query.resultCount = results?.length || 0;
          
          // Broadcast completion
          socketUtils.broadcastQueryStatus(queryId, 'completed', {
            executionTime,
            resultCount: query.resultCount
          });
          
          // Remove from active queries
          activeQueries.delete(queryId);
          
          // Update running queries list
          socketUtils.broadcastRunningQueries(Array.from(activeQueries.values()));
          
          fastify.log.info(`Query completed: ${queryId} in ${executionTime}s`);
        }
      } catch (error) {
        fastify.log.error('Query completion error:', error);
      }
    });

    // Handle query error
    socket.on('query_error', (data) => {
      try {
        const { queryId, error } = data;
        
        if (activeQueries.has(queryId)) {
          const query = activeQueries.get(queryId);
          query.status = 'error';
          query.error = error;
          query.endTime = new Date().toISOString();
          
          // Broadcast error
          socketUtils.broadcastQueryError(error);
          socketUtils.broadcastQueryStatus(queryId, 'error', { error });
          
          // Remove from active queries
          activeQueries.delete(queryId);
          
          // Update running queries list
          socketUtils.broadcastRunningQueries(Array.from(activeQueries.values()));
          
          fastify.log.error(`Query error: ${queryId} - ${error}`);
        }
      } catch (error) {
        fastify.log.error('Query error handling error:', error);
      }
    });

    // Handle backend status check
    socket.on('check_backend_status', () => {
      socket.emit('backend_status', {
        status: 'online',
        timestamp: new Date().toISOString(),
        connectedUsers: socketUtils.getConnectedUsersCount(),
        activeQueries: socketUtils.getActiveQueriesCount(),
        uptime: process.uptime()
      });
    });

    // Handle ping
    socket.on('ping', (callback) => {
      if (typeof callback === 'function') {
        callback({
          timestamp: new Date().toISOString(),
          status: 'pong'
        });
      }
    });

    // Handle custom events
    socket.on('notification', (data) => {
      try {
        const { type, message, targetUserId, broadcast } = data;
        
        if (broadcast) {
          // Broadcast to all authenticated users
          io.emit('notification', {
            type,
            message,
            timestamp: new Date().toISOString(),
            from: socket.username || 'System'
          });
        } else if (targetUserId) {
          // Send to specific user
          socketUtils.sendToUser(targetUserId, 'notification', {
            type,
            message,
            timestamp: new Date().toISOString(),
            from: socket.username || 'System'
          });
        }
      } catch (error) {
        fastify.log.error('Notification error:', error);
      }
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      fastify.log.info(`Socket disconnected: ${socket.id} - ${reason}`);
      
      // Clean up
      connectedClients.delete(socket.id);
      
      // Remove from user sessions
      if (socket.userId) {
        const userSocketList = userSessions.get(socket.userId) || [];
        const updatedList = userSocketList.filter(id => id !== socket.id);
        
        if (updatedList.length === 0) {
          userSessions.delete(socket.userId);
          
          // Broadcast user offline status
          socket.broadcast.emit('user_offline', {
            userId: socket.userId,
            username: socket.username,
            timestamp: new Date().toISOString()
          });
        } else {
          userSessions.set(socket.userId, updatedList);
        }
      }
      
      // Clean up any active queries from this socket
      for (const [queryId, query] of activeQueries.entries()) {
        if (query.socketId === socket.id) {
          activeQueries.delete(queryId);
          socketUtils.broadcastQueryError('Query interrupted by client disconnect');
        }
      }
      
      // Update running queries list
      socketUtils.broadcastRunningQueries(Array.from(activeQueries.values()));
    });

    // Handle errors
    socket.on('error', (error) => {
      fastify.log.error(`Socket error on ${socket.id}:`, error);
    });
  });

  // Periodic status broadcast
  setInterval(() => {
    io.emit('server_status', {
      timestamp: new Date().toISOString(),
      connectedUsers: socketUtils.getConnectedUsersCount(),
      activeQueries: socketUtils.getActiveQueriesCount(),
      uptime: process.uptime(),
      memory: process.memoryUsage()
    });
  }, 30000); // Every 30 seconds

  // Register Socket.IO instance and utilities
  fastify.decorate('io', io);
  fastify.decorate('socketUtils', socketUtils);

  // Graceful shutdown
  fastify.addHook('onClose', async () => {
    io.close();
    fastify.log.info('Socket.IO server closed');
  });
}

export default fp(socketPlugin, {
  name: 'socket',
  dependencies: []
});