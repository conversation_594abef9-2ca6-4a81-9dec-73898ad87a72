(()=>{var a={};a.id=201,a.ids=[201],a.modules={132:(a,b,c)=>{Promise.resolve().then(c.bind(c,25075))},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7748:(a,b,c)=>{Promise.resolve().then(c.bind(c,9330))},8086:a=>{"use strict";a.exports=require("module")},9330:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>bq});var d=c(60687),e=c(67401),f=c(43210),g=c.n(f),h=c(14221),i=c(21875),j=c(56093),k=c(55110),l=c(49995),m=c(75378),n=c(27580),o=c(11860),p=c(13964),q=c(70615);let r=({isOpen:a,onClose:b,query:c,title:e})=>{let[g,h]=(0,f.useState)(!1),r=async()=>{if(c)try{await navigator.clipboard.writeText(c),h(!0),setTimeout(()=>h(!1),1500)}catch(a){h(!1)}};return(0,d.jsx)(i.Y,{isOpen:a,onClose:b,size:"3xl",scrollBehavior:"inside",backdrop:"blur",hideCloseButton:!0,classNames:{header:"bg-gradient-to-r from-gray-200 to-zinc-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(j.g,{children:[(0,d.jsx)(k.c,{className:"flex justify-between items-center m-6",children:(0,d.jsx)("div",{className:"text-lg font-semibold",children:e||"SQL Preview"})}),(0,d.jsx)(l.h,{children:(0,d.jsx)("div",{className:"bg-gray-100 p-8 rounded-xl overflow-x-auto max-h-[60vh]",children:(0,d.jsx)("pre",{className:"whitespace-pre-wrap break-words text-sm font-mono text-gray-800 text-center",children:c&&c.replace(/\s+/g," ").trim()})})}),(0,d.jsxs)(m.q,{className:"flex justify-between",children:[(0,d.jsx)(n.T,{color:"danger",variant:"light",onPress:b,startContent:(0,d.jsx)(o.A,{size:16}),children:"Tutup"}),(0,d.jsx)(n.T,{color:"default",variant:"ghost",onPress:r,startContent:g?(0,d.jsx)(p.A,{size:16}):(0,d.jsx)(q.A,{size:16}),children:g?"Tersalin!":"Salin ke Clipboard"})]})]})})};var s=c(41871),t=c(44301),u=c(21988),v=c(69087),w=c(61611),x=c(8819),y=c(40611),z=c(30485),A=c(62085);let B=({isOpen:a,onClose:b,query:c,thang:e,queryType:g="INQUIRY"})=>{let[p,q]=(0,f.useState)(!1),{axiosJWT:r,token:B,name:C}=(0,f.useContext)(h.A),{showToast:D}=(0,y.d)(),E=z.Ik().shape({queryName:z.Yj().required("Nama Query harus diisi"),queryType:z.Yj().required("Tipe Query harus dipilih")}),F={queryName:"",queryType:g,thang:e||new Date().getFullYear().toString()},G=async(a,{resetForm:d})=>{q(!0);try{let e={tipe:a.queryType,nama:a.queryName,name:C,query:c,thang:a.thang};await r.post("http://localhost:88/user/simpanquery",e,{headers:{Authorization:`Bearer ${B}`,"Content-Type":"application/json"}}),D("Query berhasil disimpan","success"),d(),b()}catch(a){D(a.response?.data?.error||"Gagal menyimpan query","error")}finally{q(!1)}};return(0,d.jsx)(i.Y,{isOpen:a,onClose:b,size:"3xl",scrollBehavior:"inside",backdrop:"blur",hideCloseButton:!0,classNames:{header:"bg-gradient-to-r from-yellow-200 to-amber-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(j.g,{children:[(0,d.jsx)(k.c,{className:"flex justify-between items-center m-6",children:(0,d.jsxs)("div",{className:"text-lg font-semibold flex items-center",children:[(0,d.jsx)(w.A,{className:"mr-2 text-blue-600",size:20}),"Simpan Query"]})}),(0,d.jsx)(A.l1,{initialValues:F,validationSchema:E,onSubmit:G,children:({values:a,errors:c,touched:e,handleChange:f,isSubmitting:g})=>(0,d.jsxs)(A.lV,{children:[(0,d.jsx)(l.h,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tahun Anggaran"}),(0,d.jsx)(s.r,{name:"thang",value:a.thang,onChange:f,disabled:!0,className:"bg-gray-100"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tipe Query"}),(0,d.jsxs)(t.d,{name:"queryType",value:a.queryType,onChange:f,disabled:p,children:[(0,d.jsx)(u.y,{value:"INQUIRY",children:"Inquiry"},"INQUIRY"),(0,d.jsx)(u.y,{value:"BELANJA",children:"Belanja"},"BELANJA"),(0,d.jsx)(u.y,{value:"PENERIMAAN",children:"Penerimaan"},"PENERIMAAN"),(0,d.jsx)(u.y,{value:"BLOKIR",children:"Blokir"},"BLOKIR")]}),c.queryType&&e.queryType&&(0,d.jsx)("div",{className:"text-red-500 text-xs mt-1",children:c.queryType})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nama Query"}),(0,d.jsx)(s.r,{name:"queryName",value:a.queryName,onChange:f,placeholder:"Masukkan nama untuk query ini...",disabled:p}),c.queryName&&e.queryName&&(0,d.jsx)("div",{className:"text-red-500 text-xs mt-1",children:c.queryName})]}),(0,d.jsx)("div",{className:"text-xs text-gray-500 italic",children:"*) Query yang tersimpan dapat diakses di menu Profile, tab Query Data"})]})}),(0,d.jsxs)(m.q,{className:"flex justify-between",children:[(0,d.jsx)(n.T,{color:"danger",variant:"light",onPress:b,disabled:p,startContent:(0,d.jsx)(o.A,{size:16}),children:"Tutup"}),(0,d.jsx)(n.T,{color:"warning",variant:"ghost",type:"submit",disabled:p,className:"w-[160px]",startContent:p?(0,d.jsx)(v.o,{size:"sm"}):(0,d.jsx)(x.A,{size:16}),children:p?"Menyimpan...":"Simpan Query"})]})]})})]})})};var C=c(17985),D=c(84292),E=c(53823),F=c(88977),G=c(14229),H=c(37911),I=c(10022),J=c(5066),K=c(16023);async function L(a,b="data.xlsx"){if(!a||!a.length)return;let d=await c.e(3103).then(c.bind(c,33103)),e=d.utils.json_to_sheet(a),f=d.utils.book_new();d.utils.book_append_sheet(f,e,"Sheet1");let g=new Blob([d.write(f,{bookType:"xlsx",type:"array"})],{type:"application/octet-stream"}),h=URL.createObjectURL(g),i=document.createElement("a");i.setAttribute("href",h),i.setAttribute("download",b),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(h)}async function M(a,b="data.pdf"){if(!a||!a.length)return;let d=(await c.e(4403).then(c.bind(c,4403))).default,e=(await c.e(8848).then(c.bind(c,88848))).default,f=new d,g=Object.keys(a[0]),h=a.map(a=>g.map(b=>a[b]));e(f,{head:[g],body:h,styles:{fontSize:8},headStyles:{fillColor:[41,128,185]}}),f.save(b)}let N=({showModalPDF:a,setShowModalPDF:b,selectedFormat:c,setSelectedFormat:e,fetchExportData:f,filename:g="data_export",loading:h})=>{let p=async()=>{try{let a=await f();if(!a||0===a.length)return;switch(c){case"pdf":await M(a,`${g}.pdf`);break;case"excel":await L(a,`${g}.xlsx`);break;case"json":!function(a,b="data.json"){if(!a||!a.length)return;let c=new Blob([JSON.stringify(a,null,2)],{type:"application/json"}),d=URL.createObjectURL(c),e=document.createElement("a");e.setAttribute("href",d),e.setAttribute("download",b),e.style.visibility="hidden",document.body.appendChild(e),e.click(),document.body.removeChild(e),URL.revokeObjectURL(d)}(a,`${g}.json`);break;case"text":!function(a,b="data.txt"){if(!a||!a.length)return;let c=new Blob([JSON.stringify(a,null,2)],{type:"text/plain"}),d=URL.createObjectURL(c),e=document.createElement("a");e.setAttribute("href",d),e.setAttribute("download",b),e.style.visibility="hidden",document.body.appendChild(e),e.click(),document.body.removeChild(e),URL.revokeObjectURL(d)}(a,`${g}.txt`)}b(!1)}catch(a){console.error("Export failed",a)}};return(0,d.jsx)(i.Y,{isOpen:a,onClose:()=>b(!1),size:"3xl",scrollBehavior:"inside",backdrop:"blur",hideCloseButton:!0,classNames:{header:"bg-gradient-to-r from-green-200 to-emerald-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(j.g,{children:[(0,d.jsx)(k.c,{className:"flex justify-between items-center m-6",children:(0,d.jsxs)("div",{className:"text-lg font-semibold flex items-center",children:[(0,d.jsx)(F.A,{className:"mr-2 text-success",size:20}),"Kirim Data ke WhatsApp"]})}),(0,d.jsx)(l.h,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Pilih format file untuk dikirim:"}),(0,d.jsxs)(C.U,{value:c,onValueChange:e,orientation:"horizontal",className:"flex flex-row gap-8 justify-center h-16 items-center",classNames:{wrapper:"gap-8 justify-center h-16 items-center"},children:[(0,d.jsx)(D.O,{value:"pdf",color:"danger",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(G.A,{className:"mr-2 text-red-600",size:18}),(0,d.jsx)("span",{children:"PDF"})]})}),(0,d.jsx)(D.O,{value:"excel",color:"success",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(H.A,{className:"mr-2 text-green-600",size:18}),(0,d.jsx)("span",{children:"Excel (.xlsx)"})]})}),(0,d.jsx)(D.O,{value:"json",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(I.A,{className:"mr-2 text-blue-600",size:18}),(0,d.jsx)("span",{children:"JSON"})]})}),(0,d.jsx)(D.O,{value:"text",color:"default",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(J.A,{className:"mr-2 text-gray-600",size:18}),(0,d.jsx)("span",{children:"Text (.txt)"})]})})]}),(0,d.jsx)(E.y,{className:"my-2"}),(0,d.jsx)("div",{className:"text-xs text-gray-500",children:(0,d.jsxs)("p",{children:["Nama file: ",g,".",c]})})]})}),(0,d.jsxs)(m.q,{className:"flex justify-between",children:[(0,d.jsx)(n.T,{color:"danger",variant:"light",onPress:()=>b(!1),disabled:h,startContent:(0,d.jsx)(o.A,{size:16}),children:"Tutup"}),(0,d.jsx)(n.T,{color:"success",variant:"ghost",onPress:p,disabled:h,className:"w-[160px]",startContent:h?(0,d.jsx)(v.o,{size:"sm"}):(0,d.jsx)(K.A,{size:16}),children:h?"Mengirim...":"Kirim"})]})]})})};var O=c(49867),P=c(59e3),Q=c(98564),R=c(85015),S=c(55327),T=c(80273),U=c(92241),V=c(98e3),W=c(76142),X=c(18445),Y=c(42817),Z=c(99270),$=c(51034),_=c.n($);let aa=({isOpen:a,onClose:b,sql:c,from:e,thang:p,pembulatan:q})=>{let{axiosJWT:r,token:t,statusLogin:u}=(0,f.useContext)(h.A);(0,f.useEffect)(()=>{},[q]);let[w,x]=(0,f.useState)(!1),[y,z]=(0,f.useState)(""),[A,B]=(0,f.useState)(null),[C,D]=(0,f.useState)(!1),[E,F]=(0,f.useState)(0),[G,H]=(0,f.useState)(null),[I,J]=(0,f.useState)(null),[K,L]=(0,f.useState)(!1),[M,N]=(0,f.useState)(!0),[$,aa]=(0,f.useState)(!1),[ab,ac]=(0,f.useState)(null),ad=(0,f.useRef)(""),ae=(0,f.useRef)({column:null,direction:null}),[af,ag]=(0,f.useState)({column:null,direction:null}),[ah,ai]=(0,f.useState)([]),[aj,ak]=(0,f.useState)(null),[al,am]=(0,f.useState)(!1),an=(0,f.useRef)(1),ao=async(a=1,b=!1)=>{if(!u||!c)return;let d=1===a;d&&!b?(x(!0),N(!0),ai([]),an.current=1):b&&(N(!1),aa(!0)),am(!0),J(null);let e=performance.now();try{let f=c;if(ae.current.column&&ae.current.direction){let a=ae.current.column,b="ascending"===ae.current.direction?"ASC":"DESC";if(/\bORDER\s+BY\b/i.test(c))f=c.replace(/ORDER\s+BY\s+[^;]*/i,`ORDER BY ${a} ${b}`);else{let d=c.match(/(\s+LIMIT\s+)/i);f=d?c.replace(d[0],` ORDER BY ${a} ${b}${d[0]}`):`${c} ORDER BY ${a} ${b}`}}if(ad.current&&ad.current.trim()){let a=ad.current.trim().replace(/'/g,"''"),b=/\bWHERE\b/i.test(c),d=c.match(/SELECT\s+(.*?)\s+FROM/i);if(d){let e=d[1],g=[];if("*"===e.trim());else if((g=e.split(",").map(a=>{let b=a.trim().split(/\s+AS\s+/i)[0].trim();return b=b.replace(/["`\[\]]/g,"")}).filter(a=>{let b=a.trim();return!(b.includes("(")||b.includes("*")||b.match(/^(COUNT|SUM|AVG|MAX|MIN|DISTINCT|CASE|IF|CONCAT|SUBSTRING|DATE|YEAR|MONTH|DAY)/i)||b.match(/^[0-9]+$/)||b.match(/^['"`].*['"`]$/)||b.match(/^NULL$/i)||0===b.length||b.includes("+")||b.includes("-")||b.includes("*")||b.includes("/")||b.includes("=")||b.includes("<")||b.includes(">"))&&b.match(/^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$/)})).length>0){let d=g.filter(a=>{let b=a.toUpperCase();return"PAGU"!==b&&"PAGU_APBN"!==b&&"PAGU_DIPA"!==b&&"REALISASI"!==b&&"BLOKIR"!==b});if(d.length>0){let e=d.map(b=>`(LOWER(CAST(${b} AS CHAR)) LIKE LOWER('%${a}%'))`).join(" OR "),g=`(${e})`;if(b){let a=c.match(/(\s+(GROUP\s+BY|ORDER\s+BY|HAVING|LIMIT)\s+)/i);f=a?c.replace(a[0],` AND ${g}${a[0]}`):`${c} AND ${g}`}else{let a=c.match(/(\s+(GROUP\s+BY|ORDER\s+BY|HAVING|LIMIT)\s+)/i);f=a?c.replace(a[0],` WHERE ${g}${a[0]}`):`${c} WHERE ${g}`}}}}}let g=encodeURIComponent(f),h=(0,P.A)(g),i=await r.post("http://localhost:88/next/inquiry",{sql:h,page:a},{timeout:3e4}),j=performance.now();if(B((j-e)/1e3),i.data){let c=i.data.data||[],e=i.data.total||0,f=i.data.totalPages||0,g=i.data.grandTotals||null;F(e),d&&g&&H(g);let h=!1;if(f>0)h=a<f;else if(e>0){let b=Math.ceil(e/100);h=a<b}else h=c.length>=100;ak(h?(a+1).toString():null),an.current=a,b?ai(a=>[...a,...c]):ai(c)}else F(0),ai([]),ak(null)}catch(e){let{status:a,data:c}=e.response||{},d=c&&c.error||e.message||"Terjadi Permasalahan Koneksi atau Server Backend";J(d),(0,O.t)(a,d),F(0),b||(ai([]),ak(null))}finally{am(!1),d&&!b?x(!1):b&&aa(!1)}},[ap,aq]=(0,Y.X)({hasMore:!!aj,isEnabled:a&&u,shouldUseLoader:!0,onLoadMore:()=>{aj&&!al&&ao(parseInt(aj),!0)}});(0,f.useEffect)(()=>{if(a&&u&&c){let a=setTimeout(()=>{z(""),ad.current="",ag({column:null,direction:null}),ae.current={column:null,direction:null},J(null),N(!0),ao(1,!1)},100);return()=>{clearTimeout(a)}}},[a,u,c]),(0,f.useEffect)(()=>{!a&&(J(null),z(""),ad.current="",F(0),B(null),N(!0),aa(!1),ag({column:null,direction:null}),ae.current={column:null,direction:null},ak(null),ab&&(clearTimeout(ab),ac(null)))},[a,ab]),(0,f.useEffect)(()=>{w||al||N(!1)},[w,al]);let ar=a=>{let b=Number(a);return isNaN(b)?"0":"1000000000000"===q?new Intl.NumberFormat("de-DE",{minimumFractionDigits:0,maximumFractionDigits:2}).format(b):new Intl.NumberFormat("de-DE",{minimumFractionDigits:0,maximumFractionDigits:0}).format(b)},as={kddept:a=>String(a),kdsatker:a=>String(a)},at=(0,f.useMemo)(()=>0===ah.length?[]:Object.keys(ah[0]),[ah]),au=(0,f.useMemo)(()=>0===ah.length?{}:at.reduce((a,b)=>(["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(b.toUpperCase())&&ah.reduce((a,c)=>{let d=c[b];return isNaN(Number(d))||""===d||"boolean"==typeof d?a:a+1},0)/ah.length>.7&&(a[b]=!0),a),{}),[ah,at]);g().useEffect(()=>{},[]);let av=at.length>0;return(0,d.jsx)(i.Y,{backdrop:"blur",isOpen:a,onClose:b,size:C?"full":"6xl",scrollBehavior:"inside",hideCloseButton:!0,className:C?"max-h-full":"h-[80vh] w-[80vw]",classNames:{header:"bg-gradient-to-r from-sky-200 to-cyan-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(j.g,{children:[(0,d.jsxs)(k.c,{className:"flex justify-between items-center m-6",children:[(0,d.jsx)("div",{className:"text-lg font-semibold",children:"Hasil Inquiry"}),(0,d.jsx)("div",{className:"flex items-center space-x-2",children:(0,d.jsx)(Q.A,{isSelected:C,onValueChange:D,onChange:a=>{D(a.target.checked)},size:"sm",children:(0,d.jsx)("span",{className:"text-sm",children:"Layar Penuh"})})})]}),(0,d.jsxs)(l.h,{className:"flex flex-col h-full min-h-0 p-0",children:[(0,d.jsx)("div",{className:"flex justify-end items-center px-6",children:(0,d.jsx)("div",{className:"flex space-x-2",children:(0,d.jsx)(s.r,{placeholder:"Ketik untuk mencari Kode atau Nama",value:y,onChange:a=>{let b=a.target.value;if(z(b),ad.current=b,J(null),ab&&clearTimeout(ab),""===b){ao(1,!1);let a=aq.current;a&&a.scrollTo({top:0,behavior:"smooth"}),ac(null);return}let c=setTimeout(()=>{ao(1,!1);let a=aq.current;a&&a.scrollTo({top:0,behavior:"smooth"}),ac(null)},300);ac(c)},startContent:(0,d.jsx)(Z.A,{size:16}),size:"md",className:"w-96"})})}),I?(0,d.jsxs)("div",{className:"text-center p-8 text-red-500",children:[(0,d.jsxs)("p",{children:["Error loading data: ",I]}),(0,d.jsxs)("div",{className:"mt-2 space-x-2",children:[(0,d.jsx)(n.T,{color:"primary",size:"sm",onClick:()=>{J(null),L(!0),setTimeout(()=>{ao(1,!1),L(!1)},100)},isLoading:K||w,children:"Retry"}),(0,d.jsx)(n.T,{color:"default",size:"sm",variant:"bordered",onClick:b,children:"Close"})]})]}):0!==ah.length||w||al?0===at.length?(0,d.jsx)("div",{className:"flex items-center justify-center h-full py-8",children:w||al?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(v.o,{color:"primary",size:"lg",variant:"simple"}),(0,d.jsx)("span",{className:"text-lg text-gray-600 ml-6 flex gap-0.5",children:"Memproses query data...".split("").map((a,b)=>(0,d.jsx)("span",{style:{display:"inline-block",animation:"wave 1.2s infinite",animationDelay:`${.08*b}s`},children:" "===a?"\xa0":a},b))}),(0,d.jsx)("style",{children:`
                    @keyframes wave {
                      0%, 60%, 100% { transform: translateY(0); }
                      30% { transform: translateY(-8px); }
                    }
                  `})]}):(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"No data available"})}):(0,d.jsx)("div",{className:"h-full overflow-auto px-6 py-1",ref:aq,children:(0,d.jsx)(R.Z,{className:"h-full p-4 shadow-none border-2",children:(0,d.jsxs)(S.j,{"aria-label":"Inquiry results table",removeWrapper:!0,sortDescriptor:af,onSortChange:a=>{ag(a),ae.current=a,ao(1,!1);let b=aq.current;b&&b.scrollTo({top:0,behavior:"smooth"})},classNames:{base:"h-full overflow-auto",table:"h-full",th:"position: sticky top-0 z-20",wrapper:"h-full w-full "},children:[(0,d.jsxs)(T.X,{children:[av&&(0,d.jsx)(U.e,{className:"text-center w-12 uppercase",children:"No"},"index"),at.map(a=>{au[a];let b=["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(a.toUpperCase()),c={};return["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(a.toUpperCase())&&(c={width:"160px",minWidth:"160px",maxWidth:"260px"}),(0,d.jsx)(U.e,{allowsSorting:b,className:"text-center uppercase",style:c,children:a},a)})]}),(0,d.jsxs)(V.E,{isLoading:!1,emptyContent:"No data to display",children:[0===ah.length?(0,d.jsx)(W.s,{children:(0,d.jsx)(X.w,{colSpan:at.length+ +!!av,className:"text-center",children:y?`Tidak ada hasil untuk pencarian: "${y}"`:"No data available"})}):ah.map((a,b)=>(0,d.jsxs)(W.s,{children:[av&&(0,d.jsx)(X.w,{className:"text-center",children:b+1}),at.map(b=>(0,d.jsx)(X.w,{className:au[b]?"text-right":"text-center",children:as[b]?as[b](a[b]):au[b]&&!isNaN(Number(a[b]))?ar(a[b]):a[b]},b))]},`${a.id||b}`)),ah.length>0&&(0,d.jsx)(W.s,{children:(0,d.jsx)(X.w,{colSpan:at.length+ +!!av,className:`text-center ${$?"py-4":"py-2"}`,style:{minHeight:"40px"},children:(0,d.jsx)("div",{ref:ap,className:"w-full",children:$?(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,d.jsx)(v.o,{color:"primary",size:"md",variant:"simple"}),(0,d.jsx)("span",{className:"text-sm text-default-600",children:"Memuat data selanjutnya..."})]}):(0,d.jsx)("div",{className:"h-1 w-full flex items-center justify-center",children:!1})})})}),ah.length>0&&(0,d.jsxs)(W.s,{className:"sticky bottom-0 bg-default-100 z-20 rounded-lg",children:[av&&(0,d.jsx)(X.w,{className:"text-center font-medium text-foreground-600 bg-default-100 first:rounded-l-lg"}),at.map((a,b)=>{let c=au[a],e=a.toUpperCase(),f=0;c&&["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(e)&&(f=ah.reduce((b,c)=>{let d=Number(c[a]);return isNaN(d)?b:b+d},0));let g=at.findLastIndex(a=>!au[a]);return(0,d.jsx)(X.w,{className:`${c?"text-right":"text-center"} font-medium text-foreground-600 bg-default-100 uppercase ${0===b&&!av?"first:rounded-l-lg":""} ${b===at.length-1?"last:rounded-r-lg":""}`,children:c&&["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(e)?ar(f):b===g?"GRAND TOTAL":""},a)})]})]})]})})}):(0,d.jsx)("div",{className:"text-center p-8 text-gray-500",children:y?(0,d.jsxs)("div",{children:[(0,d.jsxs)("p",{children:['Tidak ada hasil ditemukan untuk pencarian: "',y,'"']}),(0,d.jsx)("p",{className:"text-sm mt-2",children:"Coba gunakan kata kunci yang berbeda"})]}):(0,d.jsxs)("div",{children:["No data available",!1]})})]}),(0,d.jsx)(m.q,{children:(0,d.jsxs)("div",{className:"flex justify-between items-center gap-8 w-full",children:[(0,d.jsx)("div",{className:"flex text-sm",children:E>0?(0,d.jsxs)(d.Fragment,{children:["Total Baris: ",_()(E).format("0,0"),", Ditampilkan:"," ",ah.length," item",y&&` (hasil pencarian: "${y}")`]}):y?`Tidak ada hasil untuk pencarian: "${y}"`:"No data"}),(0,d.jsx)(n.T,{color:"danger",variant:"ghost",className:"w-[120px]",onPress:b,startContent:(0,d.jsx)(o.A,{size:16}),children:"Tutup"})]})})]})})};var ab=c(80505);let ac=({id:a,checked:b,onChange:c,label:e,size:f="sm",disabled:g=!1})=>(0,d.jsxs)("div",{className:`flex items-center gap-3 px-2 py-1 rounded-2xl shadow-sm transition-all duration-300 group ${g?"opacity-50":""}`,children:[(0,d.jsx)(ab.Z,{id:a,isSelected:b,onValueChange:g?void 0:c,size:f,isDisabled:g,"aria-label":e,"aria-labelledby":`${a}-label`,classNames:{wrapper:"group-data-[selected=true]:bg-gradient-to-r group-data-[selected=true]:from-purple-400 group-data-[selected=true]:to-blue-400",thumb:"group-data-[selected=true]:bg-white shadow-lg"}}),(0,d.jsx)("label",{id:`${a}-label`,htmlFor:a,className:`text-sm font-medium transition-colors duration-200 flex-1 ${g?"text-gray-400 cursor-not-allowed":"text-gray-700 group-hover:text-purple-600 cursor-pointer"}`,children:e})]});var ad=c(77611),ae=c(54861),af=c(79410),ag=c(96882);let ah=({inquiryState:a,status:b})=>{let{dept:c,setDept:e,deptradio:f,setDeptradio:g,deptkondisi:h,setDeptkondisi:i,katadept:j,setKatadept:k}=a||{},l=j&&""!==j.trim(),m=h&&""!==h.trim(),o=c&&"XXX"!==c&&"000"!==c&&"XX"!==c,p=l||m,q=l||o,r=m||o;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(af.A,{size:20,className:"ml-4 text-secondary"}),"Kementerian"]})," ",(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[" ",(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${p?"text-gray-400":"text-gray-700"}`,children:"Pilih Kementerian"}),o&&!p&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>e&&e("000"),children:"Clear"})]}),(0,d.jsx)(ae.A,{value:c,onChange:e,className:"w-full min-w-0 max-w-full",size:"sm",status:b,isDisabled:p})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),m&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>i&&i(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:h||"",isDisabled:q,onChange:a=>{let b=a.target.value;i&&i(b)}})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),l&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>k&&k(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: keuangan",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:r,onChange:a=>{let b=a.target.value;k&&k(b)}})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"})," ",(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:[f||"1"],onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),!b){g&&g("1");return}g&&g(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var ai=c(79300),aj=c(45115);let ak=({inquiryState:a})=>{let{dept:b,kdunit:c,setKdunit:e,unitkondisi:f,setUnitkondisi:h,kataunit:i,setKataunit:j,unitradio:k,setUnitradio:l}=a||{},m=i&&""!==i.trim(),o=f&&""!==f.trim(),p=c&&"XXX"!==c&&"XX"!==c,q=m||o,r=m||p,v=o||p;return g().useEffect(()=>{e&&e("XX")},[b,e]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(ai.A,{size:20,className:"ml-4 text-secondary"}),"Eselon I"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Pilih Eselon I"}),p&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>e&&e("XX"),children:"Clear"})]}),(0,d.jsx)(aj.A,{value:c,onChange:e,kddept:b,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihunit",isDisabled:q})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"ml-1 cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),o&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 01,02,03, dst",className:"w-full min-w-0",size:"sm",value:f||"",isDisabled:r,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),m&&!v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: sekretariat",className:"w-full min-w-0",size:"sm",value:i||"",isDisabled:v,onChange:a=>j&&j(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([k||"1"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&l&&l(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var al=c(71850),am=c(8521);let an=({inquiryState:a})=>{let{dekon:b,setDekon:c,dekonkondisi:e,setDekonkondisi:f,katadekon:g,setKatadekon:h,dekonradio:i,setDekonradio:j}=a,k=g&&""!==g.trim(),l=e&&""!==e.trim(),m=b&&"XXX"!==b&&"XX"!==b&&"000"!==b&&""!==b.trim(),o=k||l,p=k||m,q=l||m;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(al.A,{size:20,className:"ml-4 text-secondary"}),"Kewenangan"]})," ",(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${o?"text-gray-400":"text-gray-700"}`,children:"Pilih Kewenangan"}),m&&!o&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(am.A,{value:b,onChange:c,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihdekon",isDisabled:o})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${p?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),l&&!p&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>f&&f(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: DK,TP,UB, dst",className:"w-full min-w-0",size:"sm",value:e||"",isDisabled:p,onChange:a=>f&&f(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),k&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: dekonsentrasi",className:"w-full min-w-0",size:"sm",value:g||"",isDisabled:q,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"})," ",(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([i]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&j(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var ao=c(776);let ap=({inquiryState:a,status:b})=>{let{kanwil:c,setKanwil:e,prov:f,kanwilradio:h,setKanwilradio:i,kanwilkondisi:j,setKanwilkondisi:k,katakanwil:l,setKatakanwil:m}=a,o=l&&""!==l.trim(),p=j&&""!==j.trim(),q=c&&"XXX"!==c&&"XX"!==c&&"XX"!==c,r=o||p,v=o||q,w=p||q;return g().useEffect(()=>{e&&e("XX")},[f,e]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(al.A,{size:20,className:"ml-4 text-secondary"}),"Kanwil"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Pilih Kanwil"}),q&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>e&&e("XX"),children:"Clear"})]}),(0,d.jsx)(ao.A,{value:c,onChange:e,kdlokasi:f,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihkanwil",isDisabled:r})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),p&&!v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>k&&k(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:v,onChange:a=>k&&k(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${w?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),o&&!w&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>m&&m(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: jakarta",className:"w-full min-w-0",size:"sm",value:l||"",isDisabled:w,onChange:a=>m&&m(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set(h?[h]:["1"]),onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),!b){i&&i("1");return}i&&i(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aq=c(62100),ar=c(24286);let as=({inquiryState:a})=>{let{kppn:b,setKppn:c,kanwil:e,kppnkondisi:f,setKppnkondisi:h,katakppn:i,setKatakppn:j,kppnradio:k,setKppnradio:l}=a,m=i&&""!==i.trim(),o=f&&""!==f.trim(),p=b&&"XXX"!==b&&"XX"!==b&&"XX"!==b,q=m||o,r=m||p,v=o||p;return g().useEffect(()=>{c&&c("XX")},[e,c]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aq.A,{size:20,className:"ml-4 text-secondary"}),"KPPN"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Pilih KPPN"}),p&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(ar.A,{value:b,onChange:c||(()=>console.warn("setKppn is undefined")),kdkanwil:e,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih KPPN",status:"pilihkppn",isDisabled:q})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),o&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:f||"",isDisabled:r,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),m&&!v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: medan",className:"w-full min-w-0",size:"sm",value:i||"",isDisabled:v,onChange:a=>j&&j(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:[k||"1"],onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),"string"==typeof b&&b.startsWith("$.")&&(b=b.replace("$.","")),!b){l&&l("1");return}l&&l(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var at=c(62740),au=c.n(at),av=c(34534),aw=c(84027),ax=c(64059);let ay=(a,b,c)=>{let d=new Map;return a.forEach(a=>{a[b]&&!d.has(a[b])&&d.set(a[b],{value:a[b],label:a[c]||a[b]})}),Array.from(d.values())},az=({inquiryState:a,type:b="program"})=>{let{value:c,setValue:e,kondisi:h,setKondisi:i,kata:j,setKata:k,radio:l,setRadio:m,filterProps:o,title:p,label:q}="activity"===b?{value:a?.giat,setValue:a?.setGiat,kondisi:a?.giatkondisi,setKondisi:a?.setGiatkondisi,kata:a?.katagiat,setKata:a?.setKatagiat,radio:a?.kegiatanradio,setRadio:a?.setKegiatanradio,filterProps:{kddept:a?.dept,kdunit:a?.kdunit,kdprogram:a?.program},title:"Kegiatan",label:"Pilih Kegiatan"}:{value:a?.program,setValue:a?.setProgram,kondisi:a?.programkondisi,setKondisi:a?.setProgramkondisi,kata:a?.kataprogram,setKata:a?.setKataprogram,radio:a?.programradio,setRadio:a?.setProgramradio,filterProps:{kddept:a?.dept,kdunit:a?.kdunit},title:"Program",label:"Pilih Program"},[r,v]=(0,f.useState)([]);(0,f.useEffect)(()=>{"program"===b&&v(((a,b)=>{let c=ax;return a&&"XX"!==a&&(c=c.filter(b=>b.kddept===a)),b&&"XX"!==b&&(c=c.filter(a=>a.kdunit===b)),ay(c,"kdprogram","nmprogram")})(o.kddept,o.kdunit))},[b,o.kddept,o.kdunit]),g().useEffect(()=>{e&&e("XX")},[o.kddept,o.kdunit,o.kdprogram,e]);let w=()=>h&&""!==h||j&&""!==j||l&&"1"!==l,x=w(),y=w()&&!h,z=w()&&!j,A=h&&""!==h,B=j&&""!==j;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aw.A,{size:20,className:"ml-4 text-secondary"}),p]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:q}),(0,d.jsx)(av.A,{value:c,onChange:e,...o,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:q,status:"activity"===b?"pilihgiat":"pilihprogram",isDisabled:x})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${y?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),A&&!y&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>i&&i(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:h||"",isDisabled:y,onChange:a=>i&&i(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${z?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),B&&!z&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>k&&k(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: pendidikan",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:z,onChange:a=>k&&k(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{selectedKeys:l?[l]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];m&&m(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aA=c(58559),aB=c(98169);let aC=({inquiryState:a})=>{let{giat:b,setGiat:c,giatkondisi:e,setGiatkondisi:h,katagiat:i,setKatagiat:j,kegiatanradio:k,setKegiatanradio:l,dept:m,kdunit:o,program:p}=a,q=i&&""!==i.trim(),r=e&&""!==e.trim(),v=b&&"XXX"!==b&&"XX"!==b&&"XXX"!==b,w=q||r,x=q||v,y=r||v,[z,A]=(0,f.useState)([]);return(0,f.useEffect)(()=>{A(((a,b,c)=>{let d=ax;return a&&"XX"!==a&&(d=d.filter(b=>b.kddept===a)),b&&"XX"!==b&&(d=d.filter(a=>a.kdunit===b)),c&&"XX"!==c&&(d=d.filter(a=>a.kdprogram===c)),ay(d,"kdgiat","nmgiat")})(m,o,p))},[m,o,p]),g().useEffect(()=>{c&&c("XX")},[m,o,p,c]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aA.A,{size:20,className:"ml-4 text-secondary"}),"Kegiatan"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Pilih Kegiatan"}),(0,d.jsx)(aB.A,{value:b,onChange:c,kddept:m,kdunit:o,kdprogram:p,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Kegiatan",status:"pilihgiat",isDisabled:w})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${x?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),r&&!x&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 1001,1002,1003, dst",className:"w-full min-w-0",size:"sm",value:e||"",isDisabled:x,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${y?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),q&&!y&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: layanan",className:"w-full min-w-0",size:"sm",value:i||"",isDisabled:y,onChange:a=>j&&j(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{selectedKeys:k?[k]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];l&&l(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aD=c(28947),aE=c(95760),aF=c(11071);let aG=({inquiryState:a,type:b="output"})=>{let{value:c,setValue:e,kondisi:h,setKondisi:i,kata:j,setKata:k,radio:l,setRadio:m,filterProps:o,title:p,label:q,Component:r}="suboutput"===b?{value:a?.soutput,setValue:a?.setsOutput,kondisi:a?.soutputkondisi,setKondisi:a?.setSoutputkondisi,kata:a?.katasoutput,setKata:a?.setKatasoutput,radio:a?.soutputradio,setRadio:a?.setsOutputradio,filterProps:{kdgiat:a?.giat,kdoutput:a?.output},title:"Sub-output",label:"Pilih Sub-output",Component:aF.A}:{value:a?.output,setValue:a?.setOutput,kondisi:a?.outputkondisi,setKondisi:a?.setOutputkondisi,kata:a?.kataoutput,setKata:a?.setKataoutput,radio:a?.outputradio,setRadio:a?.setOutputradio,filterProps:{kddept:a?.dept,kdunit:a?.kdunit,kdprogram:a?.program,kdgiat:a?.giat},title:"Output",label:"Pilih Output",Component:aE.A},[v,w]=(0,f.useState)([]);(0,f.useEffect)(()=>{"output"===b&&w(((a,b,c,d)=>{let e=ax;return a&&"XX"!==a&&(e=e.filter(b=>b.kddept===a)),b&&"XX"!==b&&(e=e.filter(a=>a.kdunit===b)),c&&"XX"!==c&&(e=e.filter(a=>a.kdprogram===c)),d&&"XX"!==d&&(e=e.filter(a=>a.kdgiat===d)),ay(e,"kdoutput","nmoutput")})(o.kddept||a?.dept,o.kdunit||a?.kdunit,o.kdprogram||a?.program,o.kdgiat||a?.giat))},[b,a?.dept,a?.kdunit,a?.program,a?.giat]),g().useEffect(()=>{e&&e("XX")},[o.kddept,o.kdunit,o.kdprogram,o.kdgiat,o.kdoutput,e]);let x=h&&""!==h.trim(),y=j&&""!==j.trim(),z=c&&"XX"!==c&&"XXX"!==c,A=x||y,B=y||z,C=x||z;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aD.A,{size:20,className:"ml-4 text-secondary"}),p]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:q}),(0,d.jsx)(r,{value:c,onChange:e,...o,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:q,status:"suboutput"===b?"pilihsoutput":"pilihoutput",isDisabled:A})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${B?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),x&&!B&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>i&&i(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: EAA,EAB,EAC, dst",className:"w-full min-w-0",size:"sm",value:h||"",isDisabled:B,onChange:a=>i&&i(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${C?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),y&&!C&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>k&&k(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: layanan",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:C,onChange:a=>k&&k(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{selectedKeys:l?[l]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];m&&m(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aH=c(26238);let aI=({inquiryState:a})=>{let{akun:b,setAkun:c,akunkondisi:e,setAkunkondisi:f,kataakun:g,setKataakun:h,akunradio:i,setAkunradio:j,jenlap:k,jenis:l,kdakun:m,setAkunType:o,setAkunValue:p,setAkunSql:q}=a,r=e&&""!==e.trim(),v=g&&""!==g.trim(),w=v||"10"===k;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aw.A,{size:20,className:"ml-4 text-secondary"}),"Akun"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",id:"label-pilih-akun",children:"Pilih Akun"})}),(0,d.jsx)(aH.A,{value:b&&b.type?b.type:b,onChange:a=>{c(a),o&&o(a.type),p&&p(a.value),q&&q(a.sql)},jenlap:k,jenis:l,kdakun:m,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Akun",status:"pilihakun",isDisabled:!1,"aria-labelledby":"label-pilih-akun"})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${w?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),r&&"10"!==k&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>f&&f(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:e||"",isDisabled:w,onChange:a=>f&&f(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Mengandung Kata"}),v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: gaji",className:"w-full min-w-0",size:"sm",value:g||"",isDisabled:r,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{selectedKeys:i?[i]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];j&&j(b)},disallowEmptySelection:!0,"aria-label":"Jenis Tampilan Akun",children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})},aJ=({inquiryState:a})=>{let{jenlap:b,kddept:c,setKddept:e,unit:f,setUnit:h,kddekon:i,setKddekon:j,kdkanwil:k,setKdkanwil:l,kdkppn:m,setKdkppn:n,kdsatker:o,setKdsatker:p,kdprogram:q,setKdprogram:r,kdgiat:s,setKdgiat:t,kdoutput:u,setKdoutput:v,kdakun:w,setKdakun:x,dept:y,setDept:z,deptkondisi:A,setDeptkondisi:B,katadept:C,setKatadept:D,deptradio:E,setDeptradio:F,kdunit:G,setKdunit:H,unitkondisi:I,setUnitkondisi:J,kataunit:K,setKataunit:L,unitradio:M,setUnitradio:N,dekon:O,setDekon:P,dekonkondisi:Q,setDekonkondisi:R,katadekon:S,setKatadekon:T,dekonradio:U,setDekonradio:V,kanwil:W,setKanwil:X,kanwilkondisi:Y,setKanwilkondisi:Z,katakanwil:$,setKatakanwil:_,kanwilradio:aa,setKanwilradio:ab,kppn:ad,setKppn:ae,kppnkondisi:af,setKppnkondisi:ag,katakppn:ai,setKatakppn:aj,kppnradio:al,setKppnradio:am,satker:ao,setSatker:aq,satkerkondisi:ar,setSatkerkondisi:at,katasatker:av,setKatasatker:aw,satkerradio:ax,setSatkerradio:ay,program:aA,setProgram:aB,programkondisi:aD,setProgramkondisi:aE,kataprogram:aF,setKataprogram:aH,programradio:aJ,setProgramradio:aK,giat:aL,setGiat:aM,giatkondisi:aN,setGiatkondisi:aO,katagiat:aP,setKatagiat:aQ,kegiatanradio:aR,setKegiatanradio:aS,output:aT,setOutput:aU,outputkondisi:aV,setOutputkondisi:aW,kataoutput:aX,setKataoutput:aY,outputradio:aZ,setOutputradio:a$,akun:a_,setAkun:a0,akunkondisi:a1,setAkunkondisi:a2,kataakun:a3,setKataakun:a4,akunradio:a5,setAkunradio:a6}=a;return g().useEffect(()=>{!c&&(z&&z("000"),B&&B(""),D&&D(""),F&&F("1"))},[c,z,B,D,F]),g().useEffect(()=>{!f&&(H&&H("XX"),J&&J(""),L&&L(""),N&&N("1"))},[f,H,J,L,N]),g().useEffect(()=>{!i&&(P&&P("XX"),R&&R(""),T&&T(""),V&&V("1"))},[i,P,R,T,V]),g().useEffect(()=>{!k&&(X&&X("XX"),Z&&Z(""),_&&_(""),ab&&ab("1"))},[k,X,Z,_,ab]),g().useEffect(()=>{!m&&(ae&&ae("XX"),ag&&ag(""),aj&&aj(""),am&&am("1"))},[m,ae,ag,aj,am]),g().useEffect(()=>{!o&&(aq&&aq("XX"),at&&at(""),aw&&aw(""),ay&&ay("1"))},[o,aq,at,aw,ay]),g().useEffect(()=>{!q&&(aB&&aB("XX"),aE&&aE(""),aH&&aH(""),aK&&aK("1"))},[q,aB,aE,aH,aK]),g().useEffect(()=>{!s&&(aM&&aM("XX"),aO&&aO(""),aQ&&aQ(""),aS&&aS("1"))},[s,aM,aO,aQ,aS]),g().useEffect(()=>{!u&&(aU&&aU("XX"),aW&&aW(""),aY&&aY(""),a$&&a$("1"))},[u,aU,aW,aY,a$]),g().useEffect(()=>{w?w&&"10"===b&&(a2&&a2("511521,511522,511529,521231,521232,521233,521234,526111,526112,526113,526114,526115,526121,526122,526123,526124,526131,526132,526311,526312,526313,526321,526322,526323"),a6&&a6("1"),a4&&a4("")):(a0&&a0("AKUN"),a2&&a2(""),a4&&a4(""),a6&&a6("1"))},[w,b,a0,a2,a4,a6]),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-full p-3 mb-6 sm:p-4 bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl",children:(0,d.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-6 2xl:grid-cols-8 gap-2 sm:gap-3",children:[(0,d.jsx)(ac,{id:"kddept-filter",checked:!!c,onChange:e,label:"Kementerian"}),(0,d.jsx)(ac,{id:"unit-filter",checked:f,onChange:h,label:"Eselon I"}),(0,d.jsx)(ac,{id:"dekon-filter",checked:i,onChange:j,label:"Kewenangan"}),(0,d.jsx)(ac,{id:"kanwil-filter",checked:k,onChange:l,label:"Kanwil"}),(0,d.jsx)(ac,{id:"kdkppn-filter",checked:m,onChange:n,label:"KPPN"}),(0,d.jsx)(ac,{id:"kdsatker-filter",checked:o,onChange:p,label:"Satker"}),(0,d.jsx)(ac,{id:"kdprogram-filter",checked:q,onChange:r,label:"Program"}),(0,d.jsx)(ac,{id:"kdgiat-filter",checked:s,onChange:t,label:"Kegiatan"}),(0,d.jsx)(ac,{id:"kdoutput-filter",checked:u,onChange:v,label:"Output/KRO"}),(0,d.jsx)(ac,{id:"kdakun-filter",checked:w,onChange:x,label:"Akun"})]})}),(0,d.jsxs)("div",{className:"space-y-4 mb-4",children:[c&&(0,d.jsx)(ah,{inquiryState:a,status:c?"pilihdept":""}),f&&(0,d.jsx)(ak,{inquiryState:a}),i&&(0,d.jsx)(an,{inquiryState:a}),k&&(0,d.jsx)(ap,{inquiryState:a}),m&&(0,d.jsx)(as,{inquiryState:a}),o&&(0,d.jsx)(au(),{inquiryState:a}),q&&(0,d.jsx)(az,{inquiryState:a}),s&&(0,d.jsx)(aC,{inquiryState:a}),u&&(0,d.jsx)(aG,{type:"output",inquiryState:a}),w&&(0,d.jsx)(aI,{inquiryState:a})]})]})};var aK=c(36220),aL=c(2840),aM=c(97840),aN=c(78122),aO=c(31158);let aP=({onExecuteQuery:a,onExportExcel:b,onExportCSV:c,onExportPDF:e,onReset:f,onSaveQuery:g,onShowSQL:h,isLoading:i})=>(0,d.jsx)(R.Z,{className:"mb-4 shadow-none bg-transparent",children:(0,d.jsx)(aK.U,{children:(0,d.jsxs)("div",{className:"flex flex-wrap gap-6 justify-center md:justify-center",children:[(0,d.jsx)(n.T,{color:"primary",startContent:(0,d.jsx)(aM.A,{size:16}),onClick:a,isLoading:i,className:"w-[160px] h-[50px]",children:"Tayang Data"}),(0,d.jsx)(n.T,{color:"danger",variant:"ghost",startContent:(0,d.jsx)(aN.A,{size:16}),onClick:f,isDisabled:i,className:"w-[160px] h-[50px]",children:"Reset Filter"}),(0,d.jsxs)(aL.x,{children:[(0,d.jsx)(n.T,{color:"success",variant:"flat",startContent:(0,d.jsx)(aO.A,{size:16}),onClick:b,isDisabled:i,className:"w-[120px] h-[50px]",children:"Excel"}),(0,d.jsx)(n.T,{color:"secondary",variant:"flat",startContent:(0,d.jsx)(aO.A,{size:16}),onClick:c,isDisabled:i,className:"w-[120px] h-[50px]",children:"CSV"})]}),(0,d.jsx)(n.T,{color:"success",variant:"flat",startContent:(0,d.jsx)(F.A,{size:16}),onClick:e,isDisabled:i,className:"w-[160px] h-[50px]",children:"Kirim WA"}),(0,d.jsx)(n.T,{color:"warning",variant:"flat",startContent:(0,d.jsx)(x.A,{size:16}),onClick:g,isDisabled:i,className:"w-[160px] h-[50px]",children:"Simpan Query"}),(0,d.jsx)(n.T,{color:"default",variant:"flat",startContent:(0,d.jsx)(I.A,{size:16}),onClick:h,isDisabled:i,className:"w-[160px] h-[50px]",children:"Tayang SQL"})]})})}),aQ=({inquiryState:a,onFilterChange:b})=>{let{thang:c,setThang:e,jenlap:f,setJenlap:h,pembulatan:i,setPembulatan:j}=a||{},[k,l]=g().useState("2025"),[m,n]=g().useState("1"),[o,p]=g().useState("1"),q=null!=c?c:k,r=null!=f?f:m,s=null!=i?i:o;g().useEffect(()=>{b&&b({thang:q,jenlap:r,pembulatan:s})},[q,r,s,b]);let v=a=>b=>{let c=Array.from(b)[0];a&&void 0!==c&&a(c)};return(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)("div",{className:"w-full p-3 mb-4 sm:p-4 bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row gap-6 w-full",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("label",{id:"thang-label",className:"block text-sm font-medium mb-2",children:"Tahun Anggaran"}),(0,d.jsx)(t.d,{selectedKeys:[q],onSelectionChange:v(e||l),className:"w-full",placeholder:"Pilih Tahun",disallowEmptySelection:!0,"aria-labelledby":"thang-label","aria-label":"Pilih Tahun Anggaran",children:["2025","2024","2023","2022","2021","2020","2019","2018","2017","2016","2015","2014"].map(a=>(0,d.jsx)(u.y,{textValue:a,children:a},a))})]}),(0,d.jsxs)("div",{className:"flex-[1.5]",children:[(0,d.jsx)("label",{id:"jenlap-label",className:"block text-sm font-medium mb-2",children:"Jenis Laporan"}),(0,d.jsx)(t.d,{selectedKeys:[r],onSelectionChange:v(h||n),className:"w-full",placeholder:"Pilih Jenis Laporan",disallowEmptySelection:!0,"aria-labelledby":"jenlap-label","aria-label":"Pilih Jenis Laporan",children:[{value:"1",label:"Belanja"},{value:"2",label:"Penerimaan Negara Bukan Pajak"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("label",{id:"pembulatan-label",className:"block text-sm font-medium mb-2",children:"Pembulatan"}),(0,d.jsx)(t.d,{selectedKeys:[s],onSelectionChange:v(j||p),className:"w-full",placeholder:"Pilih Pembulatan",disallowEmptySelection:!0,"aria-labelledby":"pembulatan-label","aria-label":"Pilih Pembulatan",children:[{value:"1",label:"Rupiah"},{value:"1000",label:"Ribuan"},{value:"1000000",label:"Jutaan"},{value:"1000000000",label:"Miliar"},{value:"1000000000000",label:"Triliun"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]})})})};class aR{constructor(a,b,c=null){this.fieldName=a,this.tableName=b,this.referenceTable=c}buildColumns(a,b="",c={}){let d={columns:[],joinClause:"",groupBy:[]};if(!a)return d;let e=`a.${this.fieldName}`,f=this.referenceTable?`${this.referenceTable.alias}.${this.referenceTable.nameField}`:null,g=c&&"1"===c.jenlap?"a.pagu_apbn":"a.pagu";switch(a){case"1":d.columns.push(e),d.groupBy.push(e);break;case"2":d.columns.push(e),f&&(d.columns.push(f),d.joinClause=this.buildJoinClause(b)),d.groupBy.push(e);break;case"3":f&&(d.columns.push(f),d.joinClause=this.buildJoinClause(b)),d.groupBy.push(e)}return d.paguField=g,d}buildJoinClause(a=""){if(!this.referenceTable)return"";let b=this.referenceTable.hasYear?`_${a}`:"",c=`${this.referenceTable.schema}.${this.referenceTable.table}${b}`;return` LEFT JOIN ${c} ${this.referenceTable.alias} ON ${this.referenceTable.joinCondition}`}buildWhereConditions(a){let b=[],{pilihValue:c,kondisiValue:d,kataValue:e,opsiType:f,defaultValues:g=["XXX","000","XX","00","XXXX","0000","XXXXXX","000000"]}=a;if(e&&""!==e.trim()){let a=this.referenceTable?`${this.referenceTable.alias}.${this.referenceTable.nameField}`:`a.${this.fieldName}`;b.push(`${a} LIKE '%${e}%'`)}else d&&""!==d.trim()?b.push(this.parseKondisiConditions(d)):c&&!g.includes(c)&&b.push(`a.${this.fieldName} = '${c}'`);return b.filter(a=>a&&""!==a.trim())}parseKondisiConditions(a){if(!a||""===a.trim())return"";let b=`a.${this.fieldName}`;if("!"===a.substring(0,1)){let c=a.substring(1).split(",").map(a=>a.trim()).filter(a=>""!==a);if(c.length>0){let a=c.map(a=>`'${a}'`).join(",");return`${b} NOT IN (${a})`}}else if(a.includes("%"))return`${b} LIKE '${a}'`;else if(a.includes("-")&&!a.includes(",")){let[c,d]=a.split("-").map(a=>a.trim());if(c&&d)return`${b} BETWEEN '${c}' AND '${d}'`}else{let c=a.split(",").map(a=>a.trim()).filter(a=>""!==a);if(c.length>0){let a=c.map(a=>`'${a}'`).join(",");return`${b} IN (${a})`}}return""}build(a,b=""){let{isEnabled:c,radio:d,pilihValue:e,kondisiValue:f,kataValue:g,opsiType:h}=a,i={columns:[],joinClause:"",groupBy:[],whereConditions:[]};if(!c)return i;let j=this.buildColumns(d,b);if(i.columns=j.columns,i.joinClause=j.joinClause,i.groupBy=j.groupBy,g&&""!==g.trim()&&this.referenceTable){let a=`${this.referenceTable.alias}.${this.referenceTable.nameField}`;i.joinClause||(i.joinClause=this.buildJoinClause(b)),i.columns.includes(a)||i.columns.push(a)}return i.whereConditions=this.buildWhereConditions({pilihValue:e,kondisiValue:f,kataValue:g,opsiType:h}),i}getEmptyResult(){return{columns:[],joinClause:"",whereConditions:[],groupBy:[]}}}let aS=aR;class aT extends aS{constructor(){super("kddept","department",{schema:"dbref",table:"t_dept",alias:"b",nameField:"nmdept",hasYear:!0,joinCondition:"a.kddept=b.kddept"})}buildFromState(a){let{kddept:b,dept:c,deptkondisi:d,katadept:e,deptradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aU extends aS{constructor(){super("kdunit","unit",{schema:"dbref",table:"t_unit",alias:"c",nameField:"nmunit",hasYear:!0,joinCondition:"a.kddept=c.kddept AND a.kdunit=c.kdunit"})}buildFromState(a){let{unit:b,kdunit:c,unitkondisi:d,kataunit:e,unitradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aV extends aS{constructor(){super("kddekon","dekonsentrasi",{schema:"dbref",table:"t_dekon",alias:"d",nameField:"nmdekon",hasYear:!0,joinCondition:"a.kddekon=d.kddekon"})}buildFromState(a){let{kddekon:b,dekon:c,dekonkondisi:d,katadekon:e,dekonradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aW extends aS{constructor(){super("kdsatker","satker",{schema:"dbref",table:"t_satker",alias:"s",nameField:"nmsatker",hasYear:!0,joinCondition:"a.kddept=s.kddept AND a.kdunit=s.kdunit AND a.kdsatker=s.kdsatker"})}buildFromState(a){let{kdsatker:b,satker:c,satkerkondisi:d,katasatker:e,satkerradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aX extends aS{constructor(){super("kdlokasi","provinsi",{schema:"dbref",table:"t_lokasi",alias:"p",nameField:"nmlokasi",hasYear:!0,joinCondition:"a.kdlokasi=p.kdlokasi"})}buildFromState(a){let{kdlokasi:b,prov:c,lokasikondisi:d,katalokasi:e,locradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aY extends aS{constructor(){super("kdkabkota","kabkota",{schema:"dbref",table:"t_kabkota",alias:"kk",nameField:"nmkabkota",hasYear:!0,joinCondition:"a.kdlokasi=kk.kdlokasi AND a.kdkabkota=kk.kdkabkota"})}buildFromState(a){let{kdkabkota:b,kabkota:c,kabkotakondisi:d,katakabkota:e,kabkotaradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aZ extends aS{constructor(){super("kdkanwil","kanwil",{schema:"dbref",table:"t_kanwil",alias:"kw",nameField:"nmkanwil",hasYear:!0,joinCondition:"a.kdkanwil=kw.kdkanwil"})}buildFromState(a){let{kdkanwil:b,kanwil:c,kanwilkondisi:d,katakanwil:e,kanwilradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class a$ extends aS{constructor(){super("kdkppn","kppn",{schema:"dbref",table:"t_kppn",alias:"kp",nameField:"nmkppn",hasYear:!0,joinCondition:"a.kdkppn=kp.kdkppn"})}buildFromState(a){let{kdkppn:b,kppn:c,kppnkondisi:d,katakppn:e,kppnradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class a_ extends aS{constructor(){super("kdfungsi","fungsi",{schema:"dbref",table:"t_fungsi",alias:"f",nameField:"nmfungsi",hasYear:!0,joinCondition:"a.kdfungsi=f.kdfungsi"})}buildFromState(a){let{kdfungsi:b,fungsi:c,fungsikondisi:d,katafungsi:e,fungsiradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class a0 extends aS{constructor(){super("kdsfung","subfungsi",{schema:"dbref",table:"t_sfung",alias:"sf",nameField:"nmsfung",hasYear:!0,joinCondition:"a.kdfungsi=sf.kdfungsi AND a.kdsfung=sf.kdsfung"})}buildFromState(a){let{kdsfungsi:b,sfungsi:c,subfungsikondisi:d,katasubfungsi:e,subfungsiradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class a1 extends aS{constructor(){super("kdprogram","program",{schema:"dbref",table:"t_program",alias:"pr",nameField:"nmprogram",hasYear:!0,joinCondition:"a.kddept=pr.kddept AND a.kdunit=pr.kdunit AND a.kdprogram=pr.kdprogram"})}buildFromState(a){let{kdprogram:b,program:c,programkondisi:d,kataprogram:e,programradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class a2 extends aS{constructor(){super("kdgiat","kegiatan",{schema:"dbref",table:"t_giat",alias:"g",nameField:"nmgiat",hasYear:!0,joinCondition:"a.kddept=g.kddept AND a.kdunit=g.kdunit AND a.kdprogram=g.kdprogram AND a.kdgiat=g.kdgiat"})}buildFromState(a){let{kdgiat:b,giat:c,giatkondisi:d,katagiat:e,kegiatanradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class a3 extends aS{constructor(){super("kdoutput","output",{schema:"dbref",table:"t_output",alias:"o",nameField:"nmoutput",hasYear:!0,joinCondition:"a.kddept=o.kddept AND a.kdunit=o.kdunit AND a.kdprogram=o.kdprogram AND a.kdgiat=o.kdgiat AND a.kdoutput=o.kdoutput"})}buildFromState(a){let{kdoutput:b,output:c,outputkondisi:d,kataoutput:e,outputradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class a4 extends aS{constructor(){super("kdsoutput","suboutput",{schema:"dbref",table:"t_soutput",alias:"so",nameField:"nmsoutput",hasYear:!0,joinCondition:"a.kddept=so.kddept AND a.kdunit=so.kdunit AND a.kdprogram=so.kdprogram AND a.kdgiat=so.kdgiat AND a.kdoutput=so.kdoutput AND a.kdsoutput=so.kdsoutput"})}buildFromState(a){let{kdsoutput:b,soutput:c,soutputkondisi:d,katasoutput:e,soutputradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class a5 extends aS{constructor(){super("kdakun","akun",{schema:"dbref",table:"t_akun",alias:"ak",nameField:"nmakun",hasYear:!0,joinCondition:"a.kdakun=ak.kdakun"})}buildFromState(a){let{kdakun:b,akun:c,akunkondisi:d,kataakun:e,akunradio:f,thang:g}=a;if(b&&"4"===f)return{columns:[],groupBy:[],joinClause:"",whereConditions:[]};if(b&&("BKPK"===c||"JENBEL"===c)){let a="BKPK"===c?4:2,b=this.build({isEnabled:!0,radio:f,pilihValue:"",kondisiValue:"",kataValue:""},g);if("BKPK"===c){let c=`dbref.t_bkpk_${g}`;if("3"===f?(b.columns=["bk.nmbkpk"],b.joinClause=` LEFT JOIN ${c} bk ON LEFT(a.kdakun,${a}) = bk.kdbkpk`,b.groupBy=[`LEFT(a.kdakun,${a})`]):"2"===f?(b.columns=[`LEFT(a.kdakun,${a}) AS kdbkpk`,"bk.nmbkpk"],b.joinClause=` LEFT JOIN ${c} bk ON LEFT(a.kdakun,${a}) = bk.kdbkpk`,b.groupBy=[`LEFT(a.kdakun,${a})`]):(b.columns=[`LEFT(a.kdakun,${a}) AS kdbkpk`],b.groupBy=[`LEFT(a.kdakun,${a})`],b.joinClause=""),d&&/^[0-9]+$/.test(d)){let a=d.length;b.whereConditions=[`LEFT(a.kdakun,${a}) IN ('${d}')`]}e&&""!==e.trim()&&(b.whereConditions=[`bk.nmbkpk LIKE '%${e.trim()}%'`])}else if("JENBEL"===c){let c=`dbref.t_gbkpk_${g}`;if("3"===f?(b.columns=["gb.nmgbkpk"],b.joinClause=` LEFT JOIN ${c} gb ON LEFT(a.kdakun,${a}) = gb.kdgbkpk`,b.groupBy=[`LEFT(a.kdakun,${a})`]):"2"===f?(b.columns=[`LEFT(a.kdakun,${a}) AS kdgbkpk`,"gb.nmgbkpk"],b.joinClause=` LEFT JOIN ${c} gb ON LEFT(a.kdakun,${a}) = gb.kdgbkpk`,b.groupBy=[`LEFT(a.kdakun,${a})`]):(b.columns=[`LEFT(a.kdakun,${a}) AS kdgbkpk`],b.groupBy=[`LEFT(a.kdakun,${a})`],b.joinClause=""),d&&/^[0-9]+$/.test(d)){let a=d.length;b.whereConditions=[`LEFT(a.kdakun,${a}) IN ('${d}')`]}e&&""!==e.trim()&&(b.whereConditions=[`gb.nmgbkpk LIKE '%${e.trim()}%'`])}return b}if(b&&("AKUN"===c||!c)&&!d&&!e)return this.build({isEnabled:!0,radio:f,pilihValue:"",kondisiValue:"",kataValue:""},g);if(b&&d&&/^[0-9]+$/.test(d)){let a=d.length,c=`LEFT(a.kdakun,${a}) IN ('${d}')`;return{...this.build({isEnabled:b,radio:f,pilihValue:"",kondisiValue:"",kataValue:e},g),whereConditions:[c]}}if(b&&e&&""!==e.trim()){let a=`ak.nmakun LIKE '%${e.trim()}%'`;return{...this.build({isEnabled:b,radio:f,pilihValue:"",kondisiValue:"",kataValue:e},g),whereConditions:[a]}}return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class a6 extends aS{constructor(){super("kdsdana","sdana",{schema:"dbref",table:"t_sdana",alias:"sd",nameField:"nmsdana",hasYear:!0,joinCondition:"a.kdsdana=sd.kdsdana"})}buildFromState(a){let{kdsdana:b,sdana:c,sdanakondisi:d,opsikatasdana:e,sdanaradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class a7 extends aS{constructor(){super("kdregister","register",{schema:"dbref",table:"t_register",alias:"r",nameField:"nmregister",hasYear:!0,joinCondition:"a.kdregister=r.kdregister"})}buildFromState(a){let{kdregister:b,register:c,registerkondisi:d,opsikataregister:e,registerradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class a8 extends aS{constructor(){super("kdpn","pronas",{schema:"dbref",table:"t_prinas",alias:"pn",nameField:"nmpn",hasYear:!0,joinCondition:"a.kdpn=pn.kdpn"})}buildFromState(a){let{KdPN:b,PN:c,PNkondisi:d,opsikataPN:e,pnradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class a9 extends aS{constructor(){super("kdpp","propres",{schema:"dbref",table:"t_priprog",alias:"pp",nameField:"nmpp",hasYear:!0,joinCondition:"a.kdpp=pp.kdpp"})}buildFromState(a){let{KdPP:b,PP:c,PPkondisi:d,opsikataPP:e,ppradio:f,thang:g}=a,h=c;return c&&c.includes("-")&&(h=c.split("-")[1]),this.build({isEnabled:b,radio:f,pilihValue:h,kondisiValue:d,kataValue:e},g)}}class ba extends aS{constructor(){super("kdkp","kegiatanprioritas",{schema:"dbref",table:"t_prigiat",alias:"pg",nameField:"nmkp",hasYear:!0,joinCondition:"a.kdkp=pg.kdkp AND a.kdpp=pg.kdpp AND a.kdpn=pg.kdpn"})}buildFromState(a){let{KdKegPP:b,kegiatanprioritas:c,kegiatanprioritasradio:d,thang:e}=a,f=this.build({isEnabled:b,radio:d,pilihValue:c,kondisiValue:void 0,kataValue:void 0},e);return b&&!f.joinClause&&(f.joinClause=this.buildJoinClause(e)),f}}class bb extends aS{constructor(){super("kdproy","prioritas",{schema:"dbref",table:"t_priproy",alias:"pri",nameField:"nmproy",hasYear:!0,joinCondition:"a.kdproy=pri.kdproy"})}buildFromState(a){let{KdPRI:b,PRI:c,PRIkondisi:d,opsikataPRI:e,priradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bc extends aS{constructor(){super("kdtema","tema",{schema:"dbref",table:"t_tema",alias:"tm",nameField:"nmtema",hasYear:!0,joinCondition:"a.kdtema=tm.kdtema"})}buildFromState(a){let{KdTema:b,Tema:c,Temakondisi:d,opsikataTema:e,temaradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class bd extends aS{constructor(){super("kdmp","megaproject",{schema:"dbref",table:"t_mp",alias:"mp",nameField:"nmmp",hasYear:!1,joinCondition:"a.kdmp=mp.kdmp"})}buildFromState(a){let{KdMP:b,MP:c,MPkondisi:d,opsikataMP:e,mpradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class be extends aS{constructor(){super("inflasi")}buildFromState(a){let{jenlap:b,Inflasi:c,inflasiradio:d,opsiInflasi:e}=a;if("6"!==b)return this.getEmptyResult();let f=this.getEmptyResult();return"1"===d&&"XX"!==c&&(f.columns.push("a.inf_intervensi","a.inf_pengeluaran"),f.groupBy.push("a.inf_intervensi","a.inf_pengeluaran")),"2"===d&&"XX"!==c&&(f.columns.push("a.inf_intervensi","bb.ur_inf_intervensi","a.inf_pengeluaran","inf.ur_inf_pengeluaran"),f.joinClause=" LEFT JOIN dbref.ref_inf_intervensi bb ON a.inf_intervensi=bb.inf_intervensi LEFT JOIN dbref.ref_inf_pengeluaran inf on a.inf_pengeluaran=inf.inf_pengeluaran",f.groupBy.push("a.inf_intervensi","a.inf_pengeluaran")),"3"===d&&"XX"!==c&&(f.columns.push("bb.ur_inf_intervensi","inf.ur_inf_pengeluaran"),f.joinClause=" LEFT JOIN dbref.ref_inf_intervensi bb ON a.inf_intervensi=bb.inf_intervensi LEFT JOIN dbref.ref_inf_pengeluaran inf on a.inf_pengeluaran=inf.inf_pengeluaran",f.groupBy.push("a.inf_intervensi","a.inf_pengeluaran")),"4"===d&&(f.columns=[]),"pilihInflasi"===e&&"XX"!==c&&c&&"00"!==c&&f.whereConditions.push("(a.inf_intervensi <> 'NULL' OR a.inf_pengeluaran <> 'NULL')"),f}}class bf extends aS{constructor(){super("stunting")}buildFromState(a){let{jenlap:b,Stunting:c,stuntingradio:d}=a;if("6"!==b)return this.getEmptyResult();let e=this.getEmptyResult();return"1"===d&&"XX"!==c&&(e.columns.push("a.stun_intervensi"),e.groupBy.push("a.stun_intervensi")),"2"===d&&"XX"!==c&&(e.columns.push("a.stun_intervensi","stun.ur_stun_intervensi"),e.joinClause=" LEFT JOIN dbref.ref_stunting_intervensi stun ON a.stun_intervensi=stun.stun_intervensi",e.groupBy.push("a.stun_intervensi")),"3"===d&&"XX"!==c&&(e.columns.push("stun.ur_stun_intervensi"),e.joinClause=" LEFT JOIN dbref.ref_stunting_intervensi stun ON a.stun_intervensi=stun.stun_intervensi",e.groupBy.push("a.stun_intervensi")),"4"===d&&(e.columns=[]),e}}class bg extends aS{constructor(){super("kemiskinan")}buildFromState(a){let{jenlap:b,Miskin:c,kemiskinanradio:d,opsiKemiskinan:e}=a;if("6"!==b)return this.getEmptyResult();let f=this.getEmptyResult();return"1"===d&&"XX"!==c&&(f.columns.push("a.kemiskinan_ekstrim"),f.groupBy.push("a.kemiskinan_ekstrim")),"2"===d&&"XX"!==c&&(f.columns.push("a.kemiskinan_ekstrim","(CASE WHEN a.kemiskinan_ekstrim = 'TRUE' THEN 'Belanja Kemiskinan Esktrim' WHEN a.kemiskinan_ekstrim <> 'TRUE' THEN 'Bukan Kemiskinan Ekstrim' END) AS ur_kemiskinan_ekstrim"),f.groupBy.push("a.kemiskinan_ekstrim")),"3"===d&&"XX"!==c&&(f.columns.push("(CASE WHEN a.kemiskinan_ekstrim = 'TRUE' THEN 'Belanja Kemiskinan Esktrim' WHEN a.kemiskinan_ekstrim <> 'TRUE' THEN 'Bukan Kemiskinan Ekstrim' END) AS ur_kemiskinan_ekstrim"),f.groupBy.push("a.kemiskinan_ekstrim")),"4"===d&&(f.columns=[]),"pilihKemiskinan"===e&&"XX"!==c&&c&&"00"!==c&&f.whereConditions.push(`a.kemiskinan_ekstrim = '${c}'`),f}}class bh extends aS{constructor(){super("pemilu")}buildFromState(a){let{jenlap:b,Pemilu:c,pemiluradio:d}=a;if("6"!==b)return this.getEmptyResult();let e=this.getEmptyResult();return"1"===d&&"XX"!==c&&(e.columns.push("a.pemilu"),e.groupBy.push("a.pemilu")),"2"===d&&"XX"!==c&&(e.columns.push("a.pemilu","(CASE WHEN a.pemilu = 'TRUE' THEN 'Belanja Pemilu' WHEN a.pemilu <> 'TRUE' THEN 'Bukan Belanja Pemilu' END) AS ur_belanja_pemilu"),e.groupBy.push("a.pemilu")),"3"===d&&"XX"!==c&&(e.columns.push("(CASE WHEN a.pemilu = 'TRUE' THEN 'Belanja Pemilu' WHEN a.pemilu <> 'TRUE' THEN 'Bukan Belanja Pemilu' END) AS ur_belanja_pemilu"),e.groupBy.push("a.pemilu")),"4"===d&&(e.columns=[]),e}}class bi extends aS{constructor(){super("ikn")}buildFromState(a){let{jenlap:b,Ikn:c,iknradio:d,opsiIkn:e}=a;if("6"!==b)return this.getEmptyResult();let f=this.getEmptyResult();return"1"===d&&"XX"!==c&&(f.columns.push("a.ikn"),f.groupBy.push("a.ikn")),"2"===d&&"XX"!==c&&(f.columns.push("a.ikn","(CASE WHEN a.ikn = 'TRUE' THEN 'Belanja IKN' WHEN a.ikn <> 'TRUE' THEN 'Bukan Belanja IKN' END) AS ur_belanja_ikn"),f.groupBy.push("a.ikn")),"3"===d&&"XX"!==c&&(f.columns.push("(CASE WHEN a.ikn = 'TRUE' THEN 'Belanja IKN' WHEN a.ikn <> 'TRUE' THEN 'Bukan Belanja IKN' END) AS ur_belanja_ikn"),f.groupBy.push("a.ikn")),"4"===d&&(f.columns=[]),"pilihikn"===e&&"XX"!==c&&c&&"00"!==c&&f.whereConditions.push(`a.ikn = '${c}'`),f}}class bj extends aS{constructor(){super("pangan")}buildFromState(a){let{jenlap:b,Pangan:c,panganradio:d}=a;if("6"!==b)return this.getEmptyResult();let e=this.getEmptyResult();return"1"===d&&"XX"!==c&&(e.columns.push("a.pangan"),e.groupBy.push("a.pangan")),"2"===d&&"XX"!==c&&(e.columns.push("a.pangan","(CASE WHEN a.pangan = 'TRUE' THEN 'Ketahanan Pangan' WHEN a.pangan <> 'TRUE' THEN 'Bukan Ketahanan Pangan' END) AS ur_belanja_pangan"),e.groupBy.push("a.pangan")),"3"===d&&"XX"!==c&&(e.columns.push("(CASE WHEN a.pangan = 'TRUE' THEN 'Ketahanan Pangan' WHEN a.pangan <> 'TRUE' THEN 'Bukan Ketahanan Pangan' END) AS ur_belanja_pangan"),e.groupBy.push("a.pangan")),"4"===d&&(e.columns=[]),e}}class bk extends aS{constructor(){super("blokir")}buildFromState(a){let{jenlap:b,thang:c}=a;if("6"!==b)return this.getEmptyResult();let d=this.getEmptyResult();return d.columns.push("a.kdblokir","a.nmblokir"),d.groupBy.push("a.kdblokir"),d}}class bl extends aS{constructor(){super("specialgrouping")}buildFromState(a){let{jenlap:b,thang:c}=a;if("7"!==b)return this.getEmptyResult();let d=this.getEmptyResult();return c>="2021"?d.groupBy.push("a.sat","a.os","a.ket"):d.groupBy.push("a.sat"),d}}class bm extends aS{constructor(){super("mbg")}buildFromState(a){let{jenlap:b,mbg:c,mbgradio:d}=a;if("11"!==b)return this.getEmptyResult();let e=this.getEmptyResult();return"1"===d&&"XX"!==c?e.columns.push("A.MBG"):"2"===d&&"XX"!==c?(e.columns.push("A.MBG","mbg.nmmbg"),e.joinClause=" LEFT JOIN DBREF.T_MBG mbg ON A.MBG=mbg.kdmbg"):"3"===d&&"XX"!==c?(e.columns.push("mbg.nmmbg"),e.joinClause=" LEFT JOIN DBREF.T_MBG mbg ON A.MBG=mbg.kdmbg"):"4"===d||e.columns.push("A.MBG"),e.groupBy.push("A.MBG"),c&&"XX"!==c&&"00"!==c&&e.whereConditions.push(`A.MBG = '${c}'`),e}}class bn{constructor(){this.filters={department:new aT,unit:new aU,dekon:new aV,satker:new aW,provinsi:new aX,kabkota:new aY,kanwil:new aZ,kppn:new a$,fungsi:new a_,subfungsi:new a0,program:new a1,kegiatan:new a2,output:new a3,suboutput:new a4,akun:new a5,sdana:new a6,register:new a7,pronas:new a8,propres:new a9,kegiatanprioritas:new ba,prioritas:new bb,tema:new bc,megaproject:new bd,inflasi:new be,stunting:new bf,kemiskinan:new bg,pemilu:new bh,ikn:new bi,pangan:new bj,blokir:new bk,specialgrouping:new bl,mbg:new bm}}buildAllFilters(a){let b={columns:[],joinClauses:[],groupBy:[],whereConditions:[]};return Object.entries(this.filters).forEach(([c,d])=>{if(this.isFilterEnabled(c,a))try{let c=d.buildFromState(a);c.columns.length>0&&b.columns.push(...c.columns),c.joinClause&&b.joinClauses.push(c.joinClause),c.groupBy.length>0&&b.groupBy.push(...c.groupBy),c.whereConditions.length>0&&b.whereConditions.push(...c.whereConditions)}catch(a){console.warn(`Error building ${c} filter:`,a)}}),b.columns=[...new Set(b.columns)],b.joinClauses=this.optimizeJoins(b.joinClauses),b.groupBy=[...new Set(b.groupBy)],b.whereConditions=b.whereConditions.filter(a=>a&&""!==a.trim()),b}buildFilter(a,b){let c=this.filters[a];if(!c)throw Error(`Filter '${a}' not found`);return c.buildFromState(b)}getAvailableFilters(){return Object.keys(this.filters)}isFilterEnabled(a,b){if("mbg"===a&&"11"===b.jenlap)return!0;let c={department:"kddept",unit:"unit",dekon:"kddekon",satker:"kdsatker",provinsi:"kdlokasi",kabkota:"kdkabkota",kanwil:"kdkanwil",kppn:"kdkppn",fungsi:"kdfungsi",subfungsi:"kdsfungsi",program:"kdprogram",kegiatan:"kdgiat",output:"kdoutput",suboutput:"kdsoutput",akun:"kdakun",sdana:"kdsdana",register:"kdregister",pronas:"KdPN",propres:"KdPP",kegiatanprioritas:"KdKegPP",prioritas:"KdPRI",tema:"KdTema",megaproject:"KdMP",inflasi:"kdInflasi",stunting:"KdStunting",kemiskinan:"kdKemiskinan",pemilu:"KdPemilu",ikn:"kdIkn",pangan:"KdPangan",mbg:"KdMBG"}[a];return!!c&&!!b[c]}optimizeJoins(a){return[...new Set(a)].filter(a=>a&&""!==a.trim()).sort()}buildAccessControl(a){let{role:b,kodekppn:c,kodekanwil:d}=a;return"3"===b&&c?`a.kdkppn = '${c}'`:"2"===b&&d?`a.kdkanwil = '${d}'`:""}buildWhereClause(a){let b=this.buildAllFilters(a),c=this.buildAccessControl(a),d=[...b.whereConditions];return(c&&d.push(c),0===d.length)?"":`WHERE ${d.join(" AND ")}`}validateFilters(a){let b=[],c=[],d=this.getAvailableFilters().filter(b=>this.isFilterEnabled(b,a));return d.length>10&&c.push(`High number of filters enabled (${d.length}). Consider reducing for better performance.`),this.isFilterEnabled("unit",a)&&!this.isFilterEnabled("department",a)&&c.push("Unit filter is enabled but Department filter is not. Consider enabling Department filter for better context."),{isValid:0===b.length,errors:b,warnings:c,enabledFilters:d}}getFilterStats(a){let b=this.buildAllFilters(a),c=this.validateFilters(a);return{totalFilters:Object.keys(this.filters).length,enabledFilters:c.enabledFilters.length,enabledFilterNames:c.enabledFilters,columnsCount:b.columns.length,joinsCount:b.joinClauses.length,whereConditionsCount:b.whereConditions.length,groupByCount:b.groupBy.length,validation:c}}getFilterSwitchValue(a,b){let c={inflasi:"kdInflasi",stunting:"KdStunting",kemiskinan:"kdKemiskinan",pemilu:"KdPemilu",ikn:"kdIkn",pangan:"KdPangan",mbg:"KdMBG"}[a];return c?b[c]:void 0}getFilterRadioValue(a,b){let c={inflasi:"inflasiradio",stunting:"stuntingradio",kemiskinan:"kemiskinanradio",pemilu:"pemiluradio",ikn:"iknradio",pangan:"panganradio",mbg:"mbgradio"}[a];return c?b[c]:void 0}getFilterOptionValue(a,b){let c={inflasi:"Inflasi",stunting:"Stunting",kemiskinan:"Miskin",pemilu:"Pemilu",ikn:"Ikn",pangan:"Pangan",mbg:"mbg"}[a];return c?b[c]:void 0}getFilterState(a,b){let c={pronas:{enabled:"KdPN",pilih:"PN",kondisi:"PNkondisi",kata:"opsikataPN",radio:"pnradio"},propres:{enabled:"KdPP",pilih:"PP",kondisi:"PPkondisi",kata:"opsikataPP",radio:"ppradio"},kegiatanprioritas:{enabled:"KdKegPP",pilih:"kegiatanprioritas",radio:"kegiatanprioritasradio"},prioritas:{enabled:"KdPRI",pilih:"PRI",kondisi:"PRIkondisi",kata:"opsikataPRI",radio:"priradio"}}[a];if(!c)return{};let d={};return Object.entries(c).forEach(([a,c])=>{d[a]=b[c]}),d}}class bo{constructor(){this.filterBuilder=new bn}buildDynamicFromAndSelect(a){let{thang:b,jenlap:c,cutoff:d,tanggal:e,akumulatif:f,pembulatan:g}=a,h=parseInt(d)>=1&&12>=parseInt(d)?parseInt(d):12,i="";for(let a=1;a<=h;a++)i+=`real${a}`,a!==h&&(i+="+ ");let j=`, ROUND(SUM(${"1"===c?"a.pagu_apbn":"a.pagu"})/${g},0) AS PAGU`,k=`, ROUND(SUM(a.blokir)/${g},0) AS BLOKIR`,l=["JAN","FEB","MAR","APR","MEI","JUN","JUL","AGS","SEP","OKT","NOV","DES"],m="",n="",o="",p="";for(let a=1;a<=12;a++){let b=l[a-1];if(a<=h){m+=`, ROUND(SUM(real${a})/${g},0) AS ${b}`;let c="";for(let b=1;b<=a;b++)c+=`real${b}`,b<a&&(c+="+");n+=`, ROUND(SUM(${c})/${g},0) AS ${b}`,o+=`, ROUND(sum(pagu${a})/${g}, 0) AS ${b}`,p+=`, ROUND(sum(blokir${a})/${g}, 0) AS ${b}`}else m+=`, 0 AS ${b}`,n+=`, 0 AS ${b}`,o+=`, 0 AS ${b}`,p+=`, 0 AS ${b}`}parseInt(d);let q=`monev${b}.pagu_real_detail_harian_${b} a`,r="",s="";switch(c){case"1":r=`monev${b}.rencana_real_harian_output_${b}_new a`,s=`, ROUND(SUM(a.pagu)/${g}, 0) AS pagu, ROUND(sum(renc1)/${g}, 0) as renc1, ROUND(sum(real1)/${g}, 0) as real1, ROUND(sum(renc2)/${g}, 0) as renc2, ROUND(sum(real2)/${g}, 0) as real2, ROUND(sum(renc3)/${g}, 0) as renc3, ROUND(sum(real3)/${g}, 0) as real3, ROUND(sum(renc4)/${g}, 0) as renc4, ROUND(sum(real4)/${g}, 0) as real4, ROUND(sum(renc5)/${g}, 0) as renc5, ROUND(sum(real5)/${g}, 0) as real5, ROUND(sum(renc6)/${g}, 0) as renc6, ROUND(sum(real6)/${g}, 0) as real6, ROUND(sum(renc7)/${g}, 0) as renc7, ROUND(sum(real7)/${g}, 0) as real7, ROUND(sum(renc8)/${g}, 0) as renc8, ROUND(sum(real8)/${g}, 0) as real8, ROUND(sum(renc9)/${g}, 0) as renc9, ROUND(sum(real9)/${g}, 0) as real9, ROUND(sum(renc10)/${g}, 0) as renc10, ROUND(sum(real10)/${g}, 0) as real10, ROUND(sum(renc11)/${g}, 0) as renc11, ROUND(sum(real11)/${g}, 0) as real11, ROUND(sum(renc12)/${g}, 0) as renc12, ROUND(sum(real12)/${g}, 0) as real12`;break;case"2":r=`monev${b}.pnbp_rencana_${b} a`,s=`, ROUND(SUM(rencjan)/${g}, 0) AS renc1, ROUND(SUM(realjan)/${g}, 0) AS real1, ROUND(SUM(rencfeb)/${g}, 0) AS renc2, ROUND(SUM(realfeb)/${g}, 0) AS real2, ROUND(SUM(rencmar)/${g}, 0) AS renc3, ROUND(SUM(realmar)/${g}, 0) AS real3, ROUND(SUM(rencapr)/${g}, 0) AS renc4, ROUND(SUM(realapr)/${g}, 0) AS real4, ROUND(SUM(rencmei)/${g}, 0) AS renc5, ROUND(SUM(realmei)/${g}, 0) AS real5, ROUND(SUM(rencjun)/${g}, 0) AS renc6, ROUND(SUM(realjun)/${g}, 0) AS real6, ROUND(SUM(rencjul)/${g}, 0) AS renc7, ROUND(SUM(realjul)/${g}, 0) AS real7, ROUND(SUM(rencags)/${g}, 0) AS renc8, ROUND(SUM(realags)/${g}, 0) AS real8, ROUND(SUM(rencsep)/${g}, 0) AS renc9, ROUND(SUM(realsep)/${g}, 0) AS real9, ROUND(SUM(rencokt)/${g}, 0) AS renc10, ROUND(SUM(realokt)/${g}, 0) AS real10, ROUND(SUM(rencnov)/${g}, 0) AS renc11, ROUND(SUM(realnov)/${g}, 0) AS real11, ROUND(SUM(rencdes)/${g}, 0) AS renc12, ROUND(SUM(realdes)/${g}, 0) AS real12`;break;default:r=q,s=j+k}return{dynamicFrom:r,dynamicSelect:s}}buildJenlap1PriorityColumns(a){let{thang:b,pnradio:c,ppradio:d,kegiatanprioritasradio:e,priradio:f}=a,g=[];if(c&&"4"!==c)switch(c){case"1":g.push("a.kdpn");break;case"2":g.push("a.kdpn","pn.nmpn");break;case"3":g.push("pn.nmpn")}if(d&&"4"!==d)switch(d){case"1":g.push("a.kdpp");break;case"2":g.push("a.kdpp","pp.nmpp");break;case"3":g.push("pp.nmpp")}if(e&&"4"!==e)switch(e){case"1":g.push("a.kdkp");break;case"2":g.push("a.kdkp","pg.nmkp");break;case"3":g.push("pg.nmkp")}if(f&&"4"!==f)switch(f){case"1":g.push("a.kdproy");break;case"2":g.push("a.kdproy","pri.nmproy");break;case"3":g.push("pri.nmproy")}return 0===g.length&&g.push("a.kdpn","a.kdpp","a.kdkp","a.kdproy"),g.join(",")}buildJenlap1JoinClauses(a){let{thang:b,pnradio:c,ppradio:d,kegiatanprioritasradio:e,priradio:f}=a,g=[];return c&&("2"===c||"3"===c)&&g.push(` LEFT JOIN dbref.t_prinas_${b} pn ON a.kdpn=pn.kdpn`),d&&("2"===d||"3"===d)&&g.push(` LEFT JOIN dbref.t_priprog_${b} pp ON a.kdpp=pp.kdpp`),e&&("2"===e||"3"===e)&&g.push(` LEFT JOIN dbref.t_prigiat_${b} pg ON a.kdkp=pg.kdkp AND a.kdpp=pg.kdpp AND a.kdpn=pg.kdpn`),f&&("2"===f||"3"===f)&&g.push(` LEFT JOIN dbref.t_priproy_${b} pri ON a.kdproy=pri.kdproy`),g.join("")}buildJenlap1GroupBy(a){let{pnradio:b,ppradio:c,kegiatanprioritasradio:d,priradio:e}=a,f=[];return f.push("a.kdpn","a.kdpp","a.kdkp","a.kdproy"),f}deduplicateJoins(a){if(!a||""===a.trim())return"";let b=a.split(" LEFT JOIN ").filter(a=>""!==a.trim()),c=new Set;return(b.forEach(a=>{if(""!==a.trim()){let b=a.trim().replace(/\s+/g," ");c.add(b)}}),0===c.size)?"":" LEFT JOIN "+Array.from(c).join(" LEFT JOIN ")}deduplicateGroupByFields(a){if(!a||0===a.length)return[];let b=new Set,c=[];return a.forEach(a=>{if(a&&""!==a.trim()){let d=a.trim().replace(/\s+/g," "),e=d.toLowerCase();b.has(e)||(b.add(e),c.push(d))}}),c}buildQuery(a){let{dynamicFrom:b,dynamicSelect:c}=this.buildDynamicFromAndSelect(a),d=this.filterBuilder.buildAllFilters(a),e=this.filterBuilder.buildWhereClause(a),f="";f=d.columns.length>0?d.columns.join(", ")+c:c.substring(1);let g="",h=[...d.groupBy],i=this.deduplicateGroupByFields(h);i.length>0&&(g=`GROUP BY ${i.join(", ")}`);let j=d.joinClauses.join("");return`
      SELECT ${f}
      FROM ${b}${j}
      ${e}
      ${g}
    `.trim()}validateQuery(a){let b=[],c=[];a&&""!==a.trim()||b.push("Query is empty"),a.includes("FROM")||b.push("Query missing FROM clause"),a.includes("SELECT")||b.push("Query missing SELECT clause"),a.includes("a_pagu_real_bkpk_dja_")&&(a.includes("a.kdpn")||c.push("Jenlap 1 query should include kdpn field"),a.includes("a.kdpn <>'00'")||c.push("Jenlap 1 query should filter out kdpn='00'"),a.includes("GROUP BY a.kdpn,a.kdpp,a.kdkp,a.kdproy")||c.push("Jenlap 1 query should group by kdpn, kdpp, kdkp, kdproy")),a.includes("A.inf_intervensi")&&a.includes("A.inf_pengeluaran")&&(a.includes("A.inf_intervensi <> 'NULL' or A.inf_pengeluaran <> 'NULL'")||c.push("Jenlap 4 query should filter for non-NULL inflasi values"),a.includes("GROUP BY A.inf_intervensi,A.inf_pengeluaran")||c.push("Jenlap 4 query should group by inf_intervensi, inf_pengeluaran")),a.includes("A.STUN_INTERVENSI")&&(a.includes("A.STUN_INTERVENSI IS NOT NULL")||c.push("Jenlap 5 query should filter for non-NULL stunting values"),a.includes("GROUP BY A.STUN_INTERVENSI")||c.push("Jenlap 5 query should group by STUN_INTERVENSI"),a.includes("a_pagu_real_bkpk_dja_")&&a.includes("_stunting")||c.push("Jenlap 5 query should use stunting table"),a.includes("A.STUN_INTERVENSI = '")&&!a.includes("IS NOT NULL AND A.STUN_INTERVENSI = '")&&c.push("Jenlap 5 query should include both NOT NULL and specific selection conditions")),a.includes("a.kemiskinan_ekstrim")&&(a.includes("a.kemiskinan_ekstrim <> 'NULL'")||c.push("Jenlap 6 query should filter for non-NULL kemiskinan values"),a.includes("GROUP BY a.kemiskinan_ekstrim")||c.push("Jenlap 6 query should group by kemiskinan_ekstrim"),a.includes("a_pagu_real_bkpk_dja_")||c.push("Jenlap 6 query should use bkpk_dja table")),[/;\s*drop\s+table/i,/;\s*delete\s+from/i,/;\s*update\s+.*\s+set/i,/union\s+select/i].forEach(c=>{c.test(a)&&b.push("Potentially dangerous SQL pattern detected")});let d=(a.match(/LEFT JOIN/gi)||[]).length;d>10&&c.push(`High number of JOINs (${d}). Query may be slow.`);let e=(a.match(/AND|OR/gi)||[]).length;return e>15&&c.push(`High number of WHERE conditions (${e}). Query may be slow.`),{isValid:0===b.length,errors:b,warnings:c,stats:{queryLength:a.length,joinCount:d,whereConditions:e}}}getQueryPerformanceMetrics(a){let b=performance.now(),c=this.buildQuery(a),d=performance.now(),e=this.validateQuery(c),f=this.filterBuilder.getFilterStats(a);return{query:c,buildTime:d-b,validation:e,filterStats:f,recommendations:this.generatePerformanceRecommendations(e,f)}}generatePerformanceRecommendations(a,b){let c=[];return b.enabledFilters>8&&c.push("Consider reducing the number of active filters for better performance"),a.stats.joinCount>8&&c.push("High number of table JOINs detected. Consider using indexed columns"),a.stats.queryLength>5e3&&c.push("Query is very long. Consider breaking it into smaller queries"),b.whereConditionsCount>12&&c.push("Many WHERE conditions detected. Ensure proper indexing on filtered columns"),c}generateSqlPreview(a){let{dynamicFrom:b,dynamicSelect:c}=this.buildDynamicFromAndSelect(a),d=this.filterBuilder.buildAllFilters(a),e=this.filterBuilder.buildWhereClause(a);return{fromClause:b,selectClause:c,columns:d.columns,joinClauses:d.joinClauses,whereClause:e,groupBy:d.groupBy,filterStats:this.filterBuilder.getFilterStats(a)}}buildJenlap4InflasiColumns(a){let{inflasiradio:b,infintervensiradio:c,jenistampilanradio:d}=a,e=[],f=b||c||d||"1";if(f&&"4"!==f)switch(f){case"1":e.push("A.inf_intervensi");break;case"2":e.push("A.inf_intervensi","rip.ur_inf_pengeluaran");break;case"3":e.push("rip.ur_inf_pengeluaran")}else e.push("A.inf_intervensi");return e.push("A.inf_pengeluaran"),e.join(",")}buildJenlap4JoinClauses(a){let{thang:b,inflasiradio:c,infintervensiradio:d,jenistampilanradio:e}=a,f=[],g=c||d||e||"1";return g&&("2"===g||"3"===g)&&f.push(" LEFT JOIN DBREF.REF_INF_PENGELUARAN rip ON A.inf_pengeluaran=rip.inf_pengeluaran"),f.join("")}buildJenlap4GroupBy(a){let{inflasiradio:b,infintervensiradio:c,jenistampilanradio:d}=a,e=[],f=b||c||d||"1";if(f&&"4"!==f)switch(f){case"1":e.push("A.inf_intervensi");break;case"2":e.push("A.inf_intervensi","rip.ur_inf_pengeluaran");break;case"3":e.push("rip.ur_inf_pengeluaran")}else e.push("A.inf_intervensi");return e.push("A.inf_pengeluaran"),e}buildJenlap5StuntingColumns(a){let{stuntingradio:b,jenistampilanradio:c}=a,d=[],e=b||c||"1";if(e&&"4"!==e)switch(e){case"1":d.push("A.STUN_INTERVENSI");break;case"2":d.push("A.STUN_INTERVENSI","rst.ur_stun_intervensi");break;case"3":d.push("rst.ur_stun_intervensi")}else d.push("A.STUN_INTERVENSI");return d.join(",")}buildJenlap5JoinClauses(a){let{thang:b,stuntingradio:c,jenistampilanradio:d}=a,e=[],f=c||d||"1";return f&&("2"===f||"3"===f)&&e.push(" LEFT JOIN DBREF.REF_STUNTING_INTERVENSI rst ON A.STUN_INTERVENSI=rst.stun_intervensi"),e.join("")}buildJenlap5GroupBy(a){let{stuntingradio:b,jenistampilanradio:c}=a,d=[],e=b||c||"1";if(e&&"4"!==e)switch(e){case"1":d.push("A.STUN_INTERVENSI");break;case"2":d.push("A.STUN_INTERVENSI","rst.ur_stun_intervensi");break;case"3":d.push("rst.ur_stun_intervensi")}else d.push("A.STUN_INTERVENSI");return d}}let bp=()=>{let a=function(){let{role:a,telp:b,verified:c,loadingExcell:d,setloadingExcell:e,kdkppn:g,kdkanwil:i,settampilAI:j}=(0,f.useContext)(h.A),[k,l]=(0,f.useState)(!1),[m,n]=(0,f.useState)(!1),[o,p]=(0,f.useState)(!1),[q,r]=(0,f.useState)(!1),[s,t]=(0,f.useState)(!1),[u,v]=(0,f.useState)(!1),[w,x]=(0,f.useState)(!1),[y,z]=(0,f.useState)(!1),[A,B]=(0,f.useState)(!1),[C,D]=(0,f.useState)(!1),[E,F]=(0,f.useState)(!1),[G,H]=(0,f.useState)(!1),[I,J]=(0,f.useState)("1"),[K,L]=(0,f.useState)(new Date().getFullYear().toString()),[M,N]=(0,f.useState)(!1),[O,P]=(0,f.useState)("0"),[Q,R]=(0,f.useState)("1"),[S,T]=(0,f.useState)("0"),[U,V]=(0,f.useState)("pdf"),[W,X]=(0,f.useState)(!1),[Y,Z]=(0,f.useState)(!1),[$,_]=(0,f.useState)(!1),[aa,ab]=(0,f.useState)(!0),[ac,ad]=(0,f.useState)(!1),[ae,af]=(0,f.useState)(!1),[ag,ah]=(0,f.useState)(!1),[ai,aj]=(0,f.useState)(!1),[ak,al]=(0,f.useState)(!1),[am,an]=(0,f.useState)(!1),[ao,ap]=(0,f.useState)(!1),[aq,ar]=(0,f.useState)(!1),[as,at]=(0,f.useState)(!1),[au,av]=(0,f.useState)(!1),[aw,ax]=(0,f.useState)(!1),[ay,az]=(0,f.useState)(!1),[aA,aB]=(0,f.useState)(!1),[aC,aD]=(0,f.useState)(!1),[aE,aF]=(0,f.useState)(!1),[aG,aH]=(0,f.useState)(!1),[aI,aJ]=(0,f.useState)(!1),[aK,aL]=(0,f.useState)(!1),[aM,aN]=(0,f.useState)(!1),[aO,aP]=(0,f.useState)(!1),[aQ,aR]=(0,f.useState)(!1),[aS,aT]=(0,f.useState)(!1),[aU,aV]=(0,f.useState)(!1),[aW,aX]=(0,f.useState)(!1),[aY,aZ]=(0,f.useState)(!1),[a$,a_]=(0,f.useState)(!1),[a0,a1]=(0,f.useState)(!1),[a2,a3]=(0,f.useState)(!1),[a4,a5]=(0,f.useState)(!1),[a6,a7]=(0,f.useState)(!1),[a8,a9]=(0,f.useState)(!1),[ba,bb]=(0,f.useState)("000"),[bc,bd]=(0,f.useState)(""),[be,bf]=(0,f.useState)(""),[bg,bh]=(0,f.useState)("XX"),[bi,bj]=(0,f.useState)(""),[bk,bl]=(0,f.useState)(""),[bm,bn]=(0,f.useState)("XX"),[bo,bp]=(0,f.useState)(""),[bq,br]=(0,f.useState)(""),[bs,bt]=(0,f.useState)("XX"),[bu,bv]=(0,f.useState)(""),[bw,bx]=(0,f.useState)(""),[by,bz]=(0,f.useState)("XX"),[bA,bB]=(0,f.useState)(""),[bC,bD]=(0,f.useState)(""),[bE,bF]=(0,f.useState)("XX"),[bG,bH]=(0,f.useState)(""),[bI,bJ]=(0,f.useState)(""),[bK,bL]=(0,f.useState)("XX"),[bM,bN]=(0,f.useState)(""),[bO,bP]=(0,f.useState)(""),[bQ,bR]=(0,f.useState)("XX"),[bS,bT]=(0,f.useState)(""),[bU,bV]=(0,f.useState)(""),[bW,bX]=(0,f.useState)("XX"),[bY,bZ]=(0,f.useState)(""),[b$,b_]=(0,f.useState)(""),[b0,b1]=(0,f.useState)("XX"),[b2,b3]=(0,f.useState)(""),[b4,b5]=(0,f.useState)(""),[b6,b7]=(0,f.useState)("XX"),[b8,b9]=(0,f.useState)(""),[ca,cb]=(0,f.useState)(""),[cc,cd]=(0,f.useState)("XX"),[ce,cf]=(0,f.useState)(""),[cg,ch]=(0,f.useState)(""),[ci,cj]=(0,f.useState)("XX"),[ck,cl]=(0,f.useState)(""),[cm,cn]=(0,f.useState)(""),[co,cp]=(0,f.useState)("XX"),[cq,cr]=(0,f.useState)(""),[cs,ct]=(0,f.useState)(""),[cu,cv]=(0,f.useState)("XX"),[cw,cx]=(0,f.useState)(""),[cy,cz]=(0,f.useState)(""),[cA,cB]=(0,f.useState)("XX"),[cC,cD]=(0,f.useState)(""),[cE,cF]=(0,f.useState)(""),[cG,cH]=(0,f.useState)("AKUN"),[cI,cJ]=(0,f.useState)(""),[cK,cL]=(0,f.useState)(""),[cM,cN]=(0,f.useState)("XX"),[cO,cP]=(0,f.useState)(""),[cQ,cR]=(0,f.useState)(""),[cS,cT]=(0,f.useState)("XX"),[cU,cV]=(0,f.useState)(""),[cW,cX]=(0,f.useState)(""),[cY,cZ]=(0,f.useState)("XX"),[c$,c_]=(0,f.useState)("XX"),[c0,c1]=(0,f.useState)("XX"),[c2,c3]=(0,f.useState)("XX"),[c4,c5]=(0,f.useState)("XX"),[c6,c7]=(0,f.useState)("XX"),[c8,c9]=(0,f.useState)("XX"),[da,db]=(0,f.useState)("XX"),[dc,dd]=(0,f.useState)("XX"),[de,df]=(0,f.useState)("XX"),[dg,dh]=(0,f.useState)("XX"),[di,dj]=(0,f.useState)("XX"),[dk,dl]=(0,f.useState)("1"),[dm,dn]=(0,f.useState)("1"),[dp,dq]=(0,f.useState)("1"),[dr,ds]=(0,f.useState)("1"),[dt,du]=(0,f.useState)("1"),[dv,dw]=(0,f.useState)("1"),[dx,dy]=(0,f.useState)("1"),[dz,dA]=(0,f.useState)("1"),[dB,dC]=(0,f.useState)("1"),[dD,dE]=(0,f.useState)("1"),[dF,dG]=(0,f.useState)("1"),[dH,dI]=(0,f.useState)("1"),[dJ,dK]=(0,f.useState)("1"),[dL,dM]=(0,f.useState)("1"),[dN,dO]=(0,f.useState)("1"),[dP,dQ]=(0,f.useState)("1"),[dR,dS]=(0,f.useState)("1"),[dT,dU]=(0,f.useState)("1"),[dV,dW]=(0,f.useState)("1"),[dX,dY]=(0,f.useState)("1"),[dZ,d$]=(0,f.useState)("1"),[d_,d0]=(0,f.useState)("1"),[d1,d2]=(0,f.useState)("1"),[d3,d4]=(0,f.useState)("1"),[d5,d6]=(0,f.useState)("1"),[d7,d8]=(0,f.useState)("1"),[d9,ea]=(0,f.useState)("1"),[eb,ec]=(0,f.useState)("1"),[ed,ee]=(0,f.useState)("1"),[ef,eg]=(0,f.useState)("1"),[eh,ei]=(0,f.useState)("1"),[ej,ek]=(0,f.useState)("pilihdept"),[el,em]=(0,f.useState)("pilihInflasi"),[en,eo]=(0,f.useState)("pilihikn"),[ep,eq]=(0,f.useState)("pilihKemiskinan"),[er,es]=(0,f.useState)(""),[et,eu]=(0,f.useState)(""),[ev,ew]=(0,f.useState)(", round(sum(a.pagu),0) as PAGU, round(sum(a.real1),0) as REALISASI, round(sum(a.blokir) ,0) as BLOKIR"),[ex,ey]=(0,f.useState)("XX"),[ez,eA]=(0,f.useState)("1");return{role:a,telp:b,verified:c,loadingExcell:d,setloadingExcell:e,kodekppn:g,kodekanwil:i,settampilAI:j,showModal:k,setShowModal:l,showModalKedua:m,setShowModalKedua:n,showModalsql:o,setShowModalsql:p,showModalApbn:q,setShowModalApbn:r,showModalAkumulasi:s,setShowModalAkumulasi:t,showModalBulanan:u,setShowModalBulanan:v,showModalBlokir:w,setShowModalBlokir:x,showModalPN:y,setShowModalPN:z,showModalPN2:A,setShowModalPN2:B,showModalJnsblokir:C,setShowModalJnsblokir:D,showModalPDF:E,setShowModalPDF:F,showModalsimpan:G,setShowModalsimpan:H,jenlap:I,setJenlap:J,thang:K,setThang:L,tanggal:M,setTanggal:N,cutoff:O,setCutoff:P,pembulatan:Q,setPembulatan:R,akumulatif:S,setAkumulatif:T,selectedFormat:U,setSelectedFormat:V,export2:W,setExport2:X,loadingStatus:Y,setLoadingStatus:Z,showFormatDropdown:$,setShowFormatDropdown:_,kddept:aa,setKddept:ab,unit:ac,setUnit:ad,kddekon:ae,setKddekon:af,kdlokasi:ag,setKdlokasi:ah,kdkabkota:ai,setKdkabkota:aj,kdkanwil:ak,setKdkanwil:al,kdkppn:am,setKdkppn:an,kdsatker:ao,setKdsatker:ap,kdfungsi:aq,setKdfungsi:ar,kdsfungsi:as,setKdsfungsi:at,kdprogram:au,setKdprogram:av,kdgiat:aw,setKdgiat:ax,kdoutput:ay,setKdoutput:az,kdsoutput:aA,setKdsoutput:aB,kdkomponen:aC,setKdkomponen:aD,kdskomponen:aE,setKdskomponen:aF,kdakun:aG,setKdakun:aH,kdsdana:aI,setKdsdana:aJ,kdregister:aK,setKdregister:aL,kdInflasi:aM,setKdInflasi:aN,kdIkn:aO,setKdIkn:aP,kdKemiskinan:aQ,setKdKemiskinan:aR,KdPRI:aS,setKdPRI:aT,KdPangan:aU,setKdPangan:aV,KdStunting:aW,setKdStunting:aX,KdPemilu:aY,setKdPemilu:aZ,KdTema:a$,setKdTema:a_,KdPN:a0,setKdPN:a1,KdPP:a2,setKdPP:a3,KdKegPP:a4,setKdKegPP:a5,KdMP:a6,setKdMP:a7,KdMBG:a8,setKdMBG:a9,setKdMP:a7,dept:ba,setDept:bb,deptkondisi:bc,setDeptkondisi:bd,katadept:be,setKatadept:bf,kdunit:bg,setKdunit:bh,unitkondisi:bi,setUnitkondisi:bj,kataunit:bk,setKataunit:bl,dekon:bm,setDekon:bn,dekonkondisi:bo,setDekonkondisi:bp,katadekon:bq,setKatadekon:br,prov:bs,setProv:bt,lokasikondisi:bu,setLokasikondisi:bv,katalokasi:bw,setKatalokasi:bx,kabkota:by,setKabkota:bz,kabkotakondisi:bA,setKabkotakondisi:bB,katakabkota:bC,setKatakabkota:bD,kanwil:bE,setKanwil:bF,kanwilkondisi:bG,setKanwilkondisi:bH,katakanwil:bI,setKatakanwil:bJ,kppn:bK,setKppn:bL,kppnkondisi:bM,setKppnkondisi:bN,katakppn:bO,setKatakppn:bP,satker:bQ,setSatker:bR,satkerkondisi:bS,setSatkerkondisi:bT,katasatker:bU,setKatasatker:bV,fungsi:bW,setFungsi:bX,fungsikondisi:bY,setFungsikondisi:bZ,katafungsi:b$,setKatafungsi:b_,sfungsi:b0,setSfungsi:b1,subfungsikondisi:b2,setSubfungsikondisi:b3,katasubfungsi:b4,setKatasubfungsi:b5,program:b6,setProgram:b7,programkondisi:b8,setProgramkondisi:b9,kataprogram:ca,setKataprogram:cb,giat:cc,setGiat:cd,giatkondisi:ce,setGiatkondisi:cf,katagiat:cg,setKatagiat:ch,output:ci,setOutput:cj,outputkondisi:ck,setOutputkondisi:cl,kataoutput:cm,setKataoutput:cn,soutput:co,setsOutput:cp,soutputkondisi:cq,setSoutputkondisi:cr,katasoutput:cs,setKatasoutput:ct,komponen:cu,setKomponen:cv,komponenkondisi:cw,setKomponenkondisi:cx,katakomponen:cy,setKatakomponen:cz,skomponen:cA,setSkomponen:cB,skomponenkondisi:cC,setSkomponenkondisi:cD,kataskomponen:cE,setKataskomponen:cF,akun:cG,setAkun:cH,akunkondisi:cI,setAkunkondisi:cJ,kataakun:cK,setKataakun:cL,sdana:cM,setSdana:cN,sdanakondisi:cO,setSdanakondisi:cP,katasdana:cQ,setKatasdana:cR,register:cS,setRegister:cT,registerkondisi:cU,setRegisterkondisi:cV,kataregister:cW,setKataregister:cX,PN:cY,setPN:cZ,PP:c$,setPP:c_,PRI:c0,setPRI:c1,MP:c2,setMP:c3,Tema:c4,setTema:c5,Inflasi:c6,setInflasi:c7,Stunting:c8,setStunting:c9,Miskin:da,setMiskin:db,Pemilu:dc,setPemilu:dd,Ikn:de,setIkn:df,Pangan:dg,setPangan:dh,mbg:di,setmbg:dj,deptradio:dk,setDeptradio:dl,unitradio:dm,setUnitradio:dn,dekonradio:dp,setDekonradio:dq,locradio:dr,setLocradio:ds,kabkotaradio:dt,setKabkotaradio:du,kanwilradio:dv,setKanwilradio:dw,kppnradio:dx,setKppnradio:dy,satkerradio:dz,setSatkerradio:dA,fungsiradio:dB,setFungsiradio:dC,subfungsiradio:dD,setSubfungsiradio:dE,programradio:dF,setProgramradio:dG,kegiatanradio:dH,setKegiatanradio:dI,outputradio:dJ,setOutputradio:dK,soutputradio:dL,setsOutputradio:dM,komponenradio:dN,setKomponenradio:dO,skomponenradio:dP,setSkomponenradio:dQ,akunradio:dR,setAkunradio:dS,sdanaradio:dT,setSdanaradio:dU,registerradio:dV,setRegisterradio:dW,inflasiradio:dX,setInflasiradio:dY,iknradio:dZ,setIknradio:d$,kemiskinanradio:d_,setKemiskinanradio:d0,priradio:d1,setPriradio:d2,panganradio:d3,setPanganradio:d4,stuntingradio:d5,setStuntingradio:d6,pemiluradio:d7,setPemiluradio:d8,pnradio:d9,setPnradio:ea,ppradio:eb,setPpradio:ec,mpradio:ed,setMpradio:ee,temaradio:ef,setTemaradio:eg,mbgradio:eh,setmbgradio:ei,opsidept:ej,setOpsidept:ek,opsiInflasi:el,setOpsiInflasi:em,opsiIkn:en,setOpsiIkn:eo,opsiKemiskinan:ep,setOpsiKemiskinan:eq,sql:er,setSql:es,from:et,setFrom:eu,select:ev,setSelect:ew,kegiatanprioritas:ex,setKegiatanPrioritas:ey,kegiatanprioritasradio:ez,setKegiatanPrioritasRadio:eA}}(),{statusLogin:b,token:c,axiosJWT:i}=(0,f.useContext)(h.A),{buildQuery:j}=function(a){let[b,c]=(0,f.useState)({}),d=(0,f.useMemo)(()=>new bo,[]),{thang:e,jenlap:g,cutoff:h,tanggal:i,akumulatif:j,pembulatan:k,setFrom:l,setSelect:m,setSql:n}=a,o=()=>{try{let b=d.buildQuery(a),c=d.generateSqlPreview(a);return l&&l(c.fromClause),m&&m(c.selectClause),n&&n(b),b}catch(a){return console.error("Error building query:",a),""}},p=()=>d.getQueryPerformanceMetrics(a),q=()=>d.generateSqlPreview(a),r=(a=o)=>d.validateQuery(a),s=()=>d.filterBuilder.getFilterStats(a),t=b=>d.filterBuilder.isFilterEnabled(b,a),u=b=>d.filterBuilder.buildFilter(b,a),v=b=>{let c={...a,jenlap:b},e=d.buildQuery(c),f=d.validateQuery(e);return{jenlapValue:b,query:e,validation:f,preview:d.generateSqlPreview(c)}};return{buildQuery:o,getBuildQuery:()=>o,generateSqlPreview:q,validateQuery:r,getQueryPerformanceMetrics:p,getFilterStats:s,analyzeQueryComplexity:()=>{let a=p(),b=s();return{complexity:{low:b.enabledFilters<=3&&a.validation.stats.joinCount<=3,medium:b.enabledFilters<=6&&a.validation.stats.joinCount<=6,high:b.enabledFilters>6||a.validation.stats.joinCount>6},metrics:a,stats:b,recommendations:a.recommendations}},isFilterEnabled:t,getAvailableFilters:()=>d.filterBuilder.getAvailableFilters(),buildFilter:u,debugFilter:a=>{let b=u(a),c=t(a);return{filterName:a,isEnabled:c,...b}},debugSpecialFilters:()=>[],debugJenlap:v,testAllJenlaps:()=>{let a={};for(let b=1;b<=12;b++)a[b]=v(b.toString());return a},getCachedQuery:a=>b[a],setCachedQuery:(a,b)=>{c(c=>({...c,[a]:{query:b,timestamp:Date.now()}}))},clearQueryCache:()=>{c({})},generateSqlPreview:q,generateOptimizedSql:()=>o,parseAdvancedConditions:(a,b)=>new d.filterBuilder.filters.department.constructor().parseKondisiConditions(a),optimizeGroupBy:(a,b)=>[...new Set(b)].filter(b=>a.some(a=>a.includes(b)||b.includes("a."))),optimizeJoins:a=>d.filterBuilder.optimizeJoins(Array.isArray(a)?a:[a]),validateQuery:r,getQueryPerformanceMetrics:p,getQueryStats:s}}(a),{loadingExcell:k,setloadingExcell:l,showModal:m,setShowModal:n,showModalsql:o,setShowModalsql:p,showModalPDF:q,setShowModalPDF:s,showModalsimpan:t,setShowModalsimpan:u,jenlap:v,setJenlap:w,thang:x,setThang:y,pembulatan:z,setPembulatan:A,akumulatif:C,setAkumulatif:D,selectedFormat:E,setSelectedFormat:F,kddept:G,setKddept:H,unit:I,setUnit:J,kddekon:K,setKddekon:M,kdkanwil:O,setKdkanwil:P,kdkppn:Q,setKdkppn:R,kdsatker:S,setKdsatker:T,kdprogram:U,setKdprogram:V,kdgiat:W,setKdgiat:X,kdoutput:Y,setKdoutput:Z,kdakun:$,setKdakun:_,dept:ab,setDept:ac,deptkondisi:ad,setDeptkondisi:ae,katadept:af,setKatadept:ag,kdunit:ah,setKdunit:ai,unitkondisi:aj,setUnitkondisi:ak,kataunit:al,setKataunit:am,dekon:an,setDekon:ao,dekonkondisi:ap,setDekonkondisi:aq,katadekon:ar,setKatadekon:as,kanwil:at,setKanwil:au,kanwilkondisi:av,setKanwilkondisi:aw,katakanwil:ax,setKatakanwil:ay,kppn:az,setKppn:aA,kppnkondisi:aB,setKppnkondisi:aC,katakppn:aD,setKatakppn:aE,satker:aF,setSatker:aG,satkerkondisi:aH,setSatkerkondisi:aI,katasatker:aK,setKatasatker:aL,program:aM,setProgram:aN,programkondisi:aO,setProgramkondisi:aR,kataprogram:aS,setKataprogram:aT,giat:aU,setGiat:aV,giatkondisi:aW,setGiatkondisi:aX,katagiat:aY,setKatagiat:aZ,output:a$,setOutput:a_,outputkondisi:a0,setOutputkondisi:a1,kataoutput:a2,setKataoutput:a3,akun:a4,setAkun:a5,akunkondisi:a6,setAkunkondisi:a7,kataakun:a8,setKataakun:a9,deptradio:ba,setDeptradio:bb,unitradio:bc,setUnitradio:bd,dekonradio:be,setDekonradio:bf,kanwilradio:bg,setKanwilradio:bh,kppnradio:bi,setKppnradio:bj,satkerradio:bk,setSatkerradio:bl,programradio:bm,setProgramradio:bn,kegiatanradio:bp,setKegiatanradio:bq,outputradio:br,setOutputradio:bs,akunradio:bt,setAkunradio:bu,sql:bv,setSql:bw,from:bx,setFrom:by,select:bz,setSelect:bA,akunType:bB,akunValue:bC,akunSql:bD}=a,bE=()=>{let a=j();return"string"==typeof a&&a.length,a},bF=async()=>{let b=bE();a.setSql(b),n(!0)};g().useEffect(()=>{j()},[x,z]);let[bG,bH]=g().useState(!1);async function bI(){let a=bE();if(!a||"string"!=typeof a||""===a.trim())return(0,e.qs)("Query tidak valid, silakan cek filter dan parameter."),console.error("Export aborted: SQL query is empty or invalid.",{sql:a}),[];if(!b)return[];try{let b=await i.post("http://localhost:88/next/inquiry",{sql:a,page:1},{headers:{Authorization:`Bearer ${c}`}});if(b.data&&Array.isArray(b.data.data))return b.data.data;return[]}catch(a){return console.error("Export API error:",a),a&&a.response&&console.error("[Export Debug] Backend error response:",a.response.data),[]}}let bJ=async()=>{l(!0);try{let a=await bI();if(!a||!a.length){(0,e.qs)("Tidak ada data untuk diexport"),l(!1);return}await L(a,"inquiry_data.xlsx"),(0,e.qs)("Data berhasil diexport ke Excel")}catch(a){(0,e.qs)("Gagal export Excel")}l(!1)},bK=async()=>{l(!0);try{let a=await bI();if(!a||!a.length){(0,e.qs)("Tidak ada data untuk diexport"),l(!1);return}!function(a,b="data.csv"){if(!a||!a.length)return;let c=(a,b)=>null==b?"":b,d=Object.keys(a[0]),e=new Blob([[d.join(","),...a.map(a=>d.map(b=>JSON.stringify(a[b],c)).join(","))].join("\r\n")],{type:"text/csv"}),f=URL.createObjectURL(e),g=document.createElement("a");g.setAttribute("href",f),g.setAttribute("download",b),g.style.visibility="hidden",document.body.appendChild(g),g.click(),document.body.removeChild(g),URL.revokeObjectURL(f)}(a,"inquiry_data.csv"),(0,e.qs)("Data berhasil diexport ke CSV")}catch(a){(0,e.qs)("Gagal export CSV")}l(!1)};return g().useEffect(()=>{j()},[bB,bC,bD]),(0,d.jsxs)("div",{className:"w-full",children:[(0,d.jsxs)("div",{className:"xl:px-8 p-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Inquiry Deviasi Belanja"}),(0,d.jsx)(aQ,{inquiryState:{jenlap:v,setJenlap:w,pembulatan:z,setPembulatan:A,akumulatif:C,setAkumulatif:D,thang:x,setThang:y}}),(0,d.jsx)(aJ,{inquiryState:{jenlap:v,kddept:G,setKddept:H,unit:I,setUnit:J,kddekon:K,setKddekon:M,kdkanwil:O,setKdkanwil:P,kdkppn:Q,setKdkppn:R,kdsatker:S,setKdsatker:T,kdprogram:U,setKdprogram:V,kdgiat:W,setKdgiat:X,kdoutput:Y,setKdoutput:Z,kdakun:$,setKdakun:_,dept:ab,setDept:ac,deptkondisi:ad,setDeptkondisi:ae,katadept:af,setKatadept:ag,deptradio:ba,setDeptradio:bb,kdunit:ah,setKdunit:ai,unitkondisi:aj,setUnitkondisi:ak,kataunit:al,setKataunit:am,unitradio:bc,setUnitradio:bd,dekon:an,setDekon:ao,dekonkondisi:ap,setDekonkondisi:aq,katadekon:ar,setKatadekon:as,dekonradio:be,setDekonradio:bf,kanwil:at,setKanwil:au,kanwilkondisi:av,setKanwilkondisi:aw,katakanwil:ax,setKatakanwil:ay,kanwilradio:bg,setKanwilradio:bh,kppn:az,setKppn:aA,kppnkondisi:aB,setKppnkondisi:aC,katakppn:aD,setKatakppn:aE,kppnradio:bi,setKppnradio:bj,satker:aF,setSatker:aG,satkerkondisi:aH,setSatkerkondisi:aI,katasatker:aK,setKatasatker:aL,satkerradio:bk,setSatkerradio:bl,program:aM,setProgram:aN,programkondisi:aO,setProgramkondisi:aR,kataprogram:aS,setKataprogram:aT,programradio:bm,setProgramradio:bn,giat:aU,setGiat:aV,giatkondisi:aW,setGiatkondisi:aX,katagiat:aY,setKatagiat:aZ,kegiatanradio:bp,setKegiatanradio:bq,output:a$,setOutput:a_,outputkondisi:a0,setOutputkondisi:a1,kataoutput:a2,setKataoutput:a3,outputradio:br,setOutputradio:bs,akun:a4,setAkun:a5,akunkondisi:a6,setAkunkondisi:a7,kataakun:a8,setKataakun:a9,akunradio:bt,setAkunradio:bu}}),(0,d.jsx)("div",{className:"my-3 sm:px-16",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row md:flex-wrap lg:flex-nowrap gap-2 border-2 dark:border-zinc-600 rounded-xl shadow-sm py-2 px-4 font-mono tracking-wide bg-zinc-100 dark:bg-black",children:[(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-blue-600 ml-4",children:"Tahun Anggaran:"}),(0,d.jsx)("span",{className:"ml-2",children:x})]}),(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-green-600 ml-4",children:"Jenis Laporan:"}),(0,d.jsx)("span",{className:"ml-2",children:"1"===v?"Belanja":"2"===v?"Penerimaan Negara Bukan Pajak":"Unknown"})]}),(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-purple-600 ml-4",children:"Pembulatan:"}),(0,d.jsx)("span",{className:"ml-2",children:"1"===z?"Rupiah":"1000"===z?"Ribuan":"1000000"===z?"Jutaan":"1000000000"===z?"Miliaran":"Triliunan"})]}),(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-orange-600 ml-4",children:"Filter Aktif:"}),(0,d.jsxs)("span",{className:"ml-2",children:[[G,I,K,O,Q,S,U,W,Y,$].filter(Boolean).length," ","dari"," ",10]})]})]})}),(0,d.jsx)(aP,{onExecuteQuery:bF,onExportExcel:bJ,onExportCSV:bK,onExportPDF:()=>{s(!0)},onReset:()=>{w("1"),y(new Date().getFullYear().toString()),H(!0),J(!1),M(!1),P(!1),R(!1),T(!1),V(!1),X(!1),Z(!1),_(!1),A("1"),ac("000"),ai("XX"),ao("XX"),au("XX"),aA("XX"),aC(""),aE(""),aG("XX"),aI(""),aL(""),aN("XX"),aV("XX"),a_("XX"),a5("XX"),bb("1"),bd("1"),bf("1"),bh("1"),bj("1"),bl("1"),bn("1"),bq("1"),bs("1"),bu("1"),bw(""),by(""),bA(", round(sum(a.pagu),0) as PAGU, round(sum(a.real1),0) as REALISASI, round(sum(a.blokir) ,0) as BLOKIR")},isLoading:k,onSaveQuery:()=>bH(!0),onShowSQL:()=>{let b=bE();a.setSql(b),p(!0)}})]}),o&&(0,d.jsx)(r,{isOpen:o,onClose:()=>{p(!1),window.scrollTo({top:0,behavior:"smooth"})},query:bv}),m&&(0,d.jsx)(aa,{isOpen:m,onClose:()=>{n(!1),u(!1),window.scrollTo({top:0,behavior:"smooth"})},sql:bv,from:bx,thang:x,pembulatan:z}),t&&(0,d.jsx)(B,{isOpen:t,onClose:()=>{u(!1),window.scrollTo({top:0,behavior:"smooth"})},sql:bv}),q&&(0,d.jsx)(N,{showModalPDF:q,setShowModalPDF:s,selectedFormat:E,setSelectedFormat:F,fetchExportData:bI,filename:"inquiry_data",loading:k}),bG&&(0,d.jsx)(B,{isOpen:bG,onClose:()=>bH(!1),query:bv,thang:x,queryType:"INQUIRY"})]})},bq=()=>(0,d.jsx)(bp,{})},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},13778:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["inquiry-data",{children:["deviasi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,25075)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\inquiry-data\\deviasi\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,8413)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\layout.jsx"],loading:[()=>Promise.resolve().then(c.bind(c,48221)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\loading.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,53361)),"D:\\sintesaNEXT2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\sintesaNEXT2\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["D:\\sintesaNEXT2\\src\\app\\(dashboard)\\inquiry-data\\deviasi\\page.jsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/inquiry-data/deviasi/page",pathname:"/inquiry-data/deviasi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/inquiry-data/deviasi/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},25075:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\sintesaNEXT2\\\\src\\\\app\\\\(dashboard)\\\\inquiry-data\\\\deviasi\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\inquiry-data\\deviasi\\page.jsx","default")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},59e3:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(4765),e=c.n(d);let f="mebe23",g=(a,b=f)=>{let c=e().AES.encrypt(JSON.stringify(a),b).toString();return e().enc.Base64.stringify(e().enc.Utf8.parse(c))}},62740:()=>{},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,3598,9161,6159,6942,9697,901,3723,3752,635],()=>b(b.s=13778));module.exports=c})();