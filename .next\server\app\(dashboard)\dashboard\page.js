(()=>{var a={};a.id=6337,a.ids=[6337],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2254:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\sintesaNEXT2\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\loading.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\dashboard\\loading.jsx","default")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7239:(a,b,c)=>{"use strict";c.d(b,{V:()=>g});var d=c(61751),e=c(32168),f=c(43210);function g(a){var b;let[c,g]=(0,e.P)(a.selectedKey,null!=(b=a.defaultSelectedKey)?b:null,a.onSelectionChange),h=(0,f.useMemo)(()=>null!=c?[c]:[],[c]),{collection:i,disabledKeys:j,selectionManager:k}=(0,d.p)({...a,selectionMode:"single",disallowEmptySelection:!0,allowDuplicateSelectionEvents:!0,selectedKeys:h,onSelectionChange:b=>{var d;if("all"===b)return;let e=null!=(d=b.values().next().value)?d:null;e===c&&a.onSelectionChange&&a.onSelectionChange(e),g(e)}}),l=null!=c?i.getItem(c):null;return{collection:i,disabledKeys:j,selectionManager:k,selectedKey:c,setSelectedKey:g,selectedItem:l}}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},14719:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},16800:(a,b,c)=>{Promise.resolve().then(c.bind(c,2254))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19587:(a,b)=>{"use strict";function c(a){return a.split("/").map(a=>encodeURIComponent(a)).join("/")}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"encodeURIPath",{enumerable:!0,get:function(){return c}})},21820:a=>{"use strict";a.exports=require("os")},25541:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28947:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31062:(a,b,c)=>{Promise.resolve().then(c.bind(c,2254))},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},37624:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(4765),e=c.n(d);let f="mebe23",g=(a,b=f)=>{let c=e().AES.encrypt(JSON.stringify(a),b).toString();return e().enc.Base64.stringify(e().enc.Utf8.parse(c))}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41894:(a,b,c)=>{"use strict";c.d(b,{d:()=>j});var d=c(98869),e=c(62948),f=c(87223),g=c(79910),h=c(60687),i=(0,e.Rf)((a,b)=>{var c;let{as:e,className:i,children:j,...k}=a,l=(0,f.zD)(b),{slots:m,classNames:n}=(0,d.f)(),o=(0,g.$z)(null==n?void 0:n.header,i);return(0,h.jsx)(e||"div",{ref:l,className:null==(c=m.header)?void 0:c.call(m,{class:o}),...k,children:j})});i.displayName="HeroUI.CardHeader";var j=i},49587:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return e}});let d=c(59630)._(c(64963));function e(a,b){var c;let e={};"function"==typeof a&&(e.loader=a);let f={...e,...b};return(0,d.default)({...f,modules:null==(c=f.loadableGenerated)?void 0:c.modules})}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},50537:(a,b,c)=>{Promise.resolve().then(c.bind(c,75627))},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},56780:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"BailoutToCSR",{enumerable:!0,get:function(){return e}});let d=c(81208);function e(a){let{reason:b,children:c}=a;throw Object.defineProperty(new d.BailoutToCSRError(b),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64777:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"PreloadChunks",{enumerable:!0,get:function(){return h}});let d=c(60687),e=c(51215),f=c(29294),g=c(19587);function h(a){let{moduleIds:b}=a,c=f.workAsyncStorage.getStore();if(void 0===c)return null;let h=[];if(c.reactLoadableManifest&&b){let a=c.reactLoadableManifest;for(let c of b){if(!a[c])continue;let b=a[c].files;h.push(...b)}}return 0===h.length?null:(0,d.jsx)(d.Fragment,{children:h.map(a=>{let b=c.assetPrefix+"/_next/"+(0,g.encodeURIPath)(a);return a.endsWith(".css")?(0,d.jsx)("link",{precedence:"dynamic",href:b,rel:"stylesheet",as:"style"},a):((0,e.preload)(b,{as:"script",fetchPriority:"low"}),null)})})}},64963:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"default",{enumerable:!0,get:function(){return j}});let d=c(60687),e=c(43210),f=c(56780),g=c(64777);function h(a){return{default:a&&"default"in a?a.default:a}}let i={loader:()=>Promise.resolve(h(()=>null)),loading:null,ssr:!0},j=function(a){let b={...i,...a},c=(0,e.lazy)(()=>b.loader().then(h)),j=b.loading;function k(a){let h=j?(0,d.jsx)(j,{isLoading:!0,pastDelay:!0,error:null}):null,i=!b.ssr||!!b.loading,k=i?e.Suspense:e.Fragment,l=b.ssr?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(g.PreloadChunks,{moduleIds:b.modules}),(0,d.jsx)(c,{...a})]}):(0,d.jsx)(f.BailoutToCSR,{reason:"next/dynamic",children:(0,d.jsx)(c,{...a})});return(0,d.jsx)(k,{...i?{fallback:h}:{},children:l})}return k.displayName="LoadableComponent",k}},69037:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(37413),e=c(2254);let f=async()=>(0,d.jsx)(e.default,{})},71021:()=>{},74075:a=>{"use strict";a.exports=require("zlib")},75627:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>a9});var d=c(60687),e=c(77317),f=c(85015),g=c(41894),h=c(36220),i=c(10473),j=c(53823),k=c(27580),l=c(10218),m=c(43210),n=c(58445),o=c(62948),p=c(98462),q=(0,p.tv)({slots:{base:"flex flex-col gap-2 w-full",label:"",labelWrapper:"flex justify-between",value:"",track:"z-0 relative bg-default-300/50 overflow-hidden rtl:rotate-180",indicator:"h-full"},variants:{color:{default:{indicator:"bg-default-400"},primary:{indicator:"bg-primary"},secondary:{indicator:"bg-secondary"},success:{indicator:"bg-success"},warning:{indicator:"bg-warning"},danger:{indicator:"bg-danger"}},size:{sm:{label:"text-small",value:"text-small",track:"h-1"},md:{label:"text-medium",value:"text-medium",track:"h-3"},lg:{label:"text-large",value:"text-large",track:"h-5"}},radius:{none:{track:"rounded-none",indicator:"rounded-none"},sm:{track:"rounded-small",indicator:"rounded-small"},md:{track:"rounded-medium",indicator:"rounded-medium"},lg:{track:"rounded-large",indicator:"rounded-large"},full:{track:"rounded-full",indicator:"rounded-full"}},isStriped:{true:{indicator:"bg-stripe-gradient-default bg-stripe-size"}},isIndeterminate:{true:{indicator:["absolute","w-full","origin-left","animate-indeterminate-bar"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:{},false:{indicator:"transition-transform !duration-500"}}},defaultVariants:{color:"primary",size:"md",radius:"full",isStriped:!1,isIndeterminate:!1,isDisabled:!1},compoundVariants:[{disableAnimation:!0,isIndeterminate:!1,class:{indicator:"!transition-none motion-reduce:transition-none"}},{color:"primary",isStriped:!0,class:{indicator:"bg-stripe-gradient-primary bg-stripe-size"}},{color:"secondary",isStriped:!0,class:{indicator:"bg-stripe-gradient-secondary bg-stripe-size"}},{color:"success",isStriped:!0,class:{indicator:"bg-stripe-gradient-success bg-stripe-size"}},{color:"warning",isStriped:!0,class:{indicator:"bg-stripe-gradient-warning bg-stripe-size"}},{color:"danger",isStriped:!0,class:{indicator:"bg-stripe-gradient-danger bg-stripe-size"}}]},{twMerge:!0});(0,p.tv)({slots:{base:"flex flex-col justify-center gap-1 max-w-fit items-center",label:"",svgWrapper:"relative block",svg:"z-0 relative overflow-hidden",track:"h-full stroke-default-300/50",indicator:"h-full stroke-current",value:"absolute font-normal inset-0 flex items-center justify-center"},variants:{color:{default:{svg:"text-default-400"},primary:{svg:"text-primary"},secondary:{svg:"text-secondary"},success:{svg:"text-success"},warning:{svg:"text-warning"},danger:{svg:"text-danger"}},size:{sm:{svg:"w-8 h-8",label:"text-small",value:"text-[0.5rem]"},md:{svg:"w-10 h-10",label:"text-small",value:"text-[0.55rem]"},lg:{svg:"w-12 h-12",label:"text-medium",value:"text-[0.6rem]"}},isIndeterminate:{true:{svg:"animate-spinner-ease-spin"}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:{},false:{indicator:"transition-all !duration-500"}}},defaultVariants:{color:"primary",size:"md",isDisabled:!1},compoundVariants:[{disableAnimation:!0,isIndeterminate:!1,class:{svg:"!transition-none motion-reduce:transition-none"}}]});var r=c(87223),s=c(79910),t=c(83004),u=c(45427),v=c(42517),w=c(25381),x=c(46039),y=c(30900);let z=new Map,A=!1;try{A="exceptZero"===new Intl.NumberFormat("de-DE",{signDisplay:"exceptZero"}).resolvedOptions().signDisplay}catch{}let B=!1;try{B="unit"===new Intl.NumberFormat("de-DE",{style:"unit",unit:"degree"}).resolvedOptions().style}catch{}let C={degree:{narrow:{default:"\xb0","ja-JP":" 度","zh-TW":"度","sl-SI":" \xb0"}}};class D{format(a){let b="";if(b=A||null==this.options.signDisplay?this.numberFormatter.format(a):function(a,b,c){if("auto"===b)return a.format(c);{if("never"===b)return a.format(Math.abs(c));let d=!1;if("always"===b?d=c>0||Object.is(c,0):"exceptZero"===b&&(Object.is(c,-0)||Object.is(c,0)?c=Math.abs(c):d=c>0),!d)return a.format(c);{let b=a.format(-c),d=a.format(c),e=b.replace(d,"").replace(/\u200e|\u061C/,"");return 1!=[...e].length&&console.warn("@react-aria/i18n polyfill for NumberFormat signDisplay: Unsupported case"),b.replace(d,"!!!").replace(e,"+").replace("!!!",d)}}}(this.numberFormatter,this.options.signDisplay,a),"unit"===this.options.style&&!B){var c;let{unit:a,unitDisplay:d="short",locale:e}=this.resolvedOptions();if(!a)return b;let f=null==(c=C[a])?void 0:c[d];b+=f[e]||f.default}return b}formatToParts(a){return this.numberFormatter.formatToParts(a)}formatRange(a,b){if("function"==typeof this.numberFormatter.formatRange)return this.numberFormatter.formatRange(a,b);if(b<a)throw RangeError("End date must be >= start date");return`${this.format(a)} \u{2013} ${this.format(b)}`}formatRangeToParts(a,b){if("function"==typeof this.numberFormatter.formatRangeToParts)return this.numberFormatter.formatRangeToParts(a,b);if(b<a)throw RangeError("End date must be >= start date");let c=this.numberFormatter.formatToParts(a),d=this.numberFormatter.formatToParts(b);return[...c.map(a=>({...a,source:"startRange"})),{type:"literal",value:" – ",source:"shared"},...d.map(a=>({...a,source:"endRange"}))]}resolvedOptions(){let a=this.numberFormatter.resolvedOptions();return A||null==this.options.signDisplay||(a={...a,signDisplay:this.options.signDisplay}),B||"unit"!==this.options.style||(a={...a,style:"unit",unit:this.options.unit,unitDisplay:this.options.unitDisplay}),a}constructor(a,b={}){this.numberFormatter=function(a,b={}){let{numberingSystem:c}=b;if(c&&a.includes("-nu-")&&(a.includes("-u-")||(a+="-u-"),a+=`-nu-${c}`),"unit"===b.style&&!B){var d;let{unit:a,unitDisplay:c="short"}=b;if(!a)throw Error('unit option must be provided with style: "unit"');if(!(null==(d=C[a])?void 0:d[c]))throw Error(`Unsupported unit ${a} with unitDisplay = ${c}`);b={...b,style:"decimal"}}let e=a+(b?Object.entries(b).sort((a,b)=>a[0]<b[0]?-1:1).join():"");if(z.has(e))return z.get(e);let f=new Intl.NumberFormat(a,b);return z.set(e,f),f}(a,b),this.options=b}}var E=(0,o.Rf)((a,b)=>{let{Component:c,slots:e,classNames:f,label:g,percentage:h,showValueLabel:i,getProgressBarProps:j,getLabelProps:k}=function(a){var b,c;let d=(0,n.o)(),[e,f]=(0,o.rE)(a,q.variantKeys),{ref:g,as:h,id:i,className:j,classNames:k,label:l,valueLabel:p,value:z=0,minValue:A=0,maxValue:B=100,showValueLabel:C=!1,formatOptions:E={style:"percent"},...F}=e,G=(0,r.zD)(g),H=(0,s.$z)(null==k?void 0:k.base,j),[,I]=(0,t.a)({rerender:!0,delay:100}),J=a.isIndeterminate,K=null!=(c=null!=(b=a.disableAnimation)?b:null==d?void 0:d.disableAnimation)&&c,{progressBarProps:L,labelProps:M}=function(a){let{value:b=0,minValue:c=0,maxValue:d=100,valueLabel:e,isIndeterminate:f,formatOptions:g={style:"percent"}}=a,h=(0,u.$)(a,{labelable:!0}),{labelProps:i,fieldProps:j}=(0,x.M)({...a,labelElementType:"span"}),k=((b=(0,v.qE)(b,c,d))-c)/(d-c),l=function(a={}){let{locale:b}=(0,y.Y)();return(0,m.useMemo)(()=>new D(b,a),[b,a])}(g);if(!f&&!e){let a="percent"===g.style?k:b;e=l.format(a)}return{progressBarProps:(0,w.v)(h,{...j,"aria-valuenow":f?void 0:b,"aria-valuemin":c,"aria-valuemax":d,"aria-valuetext":f?void 0:e,role:"progressbar"}),labelProps:i}}({id:i,label:l,value:z,minValue:A,maxValue:B,valueLabel:p,formatOptions:E,isIndeterminate:J,"aria-labelledby":a["aria-labelledby"],"aria-label":a["aria-label"]}),N=(0,m.useMemo)(()=>q({...f,disableAnimation:K}),[(0,s.t6)(f),K]),O=!!K||I,P=(0,m.useMemo)(()=>J||!O?void 0:(0,s.QN)((z-A)/(B-A)*100),[O,J,z,A,B]),Q=(0,m.useCallback)((b={})=>({ref:G,"data-indeterminate":(0,s.sE)(J),"data-disabled":(0,s.sE)(a.isDisabled),className:N.base({class:H}),...(0,s.v6)(L,F,b)}),[G,N,J,a.isDisabled,H,L,F]),R=(0,m.useCallback)((a={})=>({className:N.label({class:null==k?void 0:k.label}),...(0,s.v6)(M,a)}),[N,k,M]);return{Component:h||"div",domRef:G,slots:N,classNames:k,label:l,percentage:P,showValueLabel:C,getProgressBarProps:Q,getLabelProps:R}}({...a,ref:b}),l=j(),p=g||i;return(0,d.jsxs)(c,{...l,children:[p?(0,d.jsxs)("div",{className:e.labelWrapper({class:null==f?void 0:f.labelWrapper}),children:[g&&(0,d.jsx)("span",{...k(),children:g}),i&&(0,d.jsx)("span",{className:e.value({class:null==f?void 0:f.value}),children:l["aria-valuetext"]})]}):null,(0,d.jsx)("div",{className:e.track({class:null==f?void 0:f.track}),children:(0,d.jsx)("div",{className:e.indicator({class:null==f?void 0:f.indicator}),style:{transform:`translateX(-${100-(h||0)}%)`}})})]})});E.displayName="HeroUI.Progress";var F=c(40611),G=c(37624),H=c(14221),I=c(21875),J=c(56093),K=c(55110),L=c(49995),M=c(41871),N=c(55327),O=c(80273),P=c(92241),Q=c(98e3),R=c(76142),S=c(18445),T=c(75378),U=c(99270),V=c(11860);let W=({isOpen:a,onClose:b})=>{let[c,e]=(0,m.useState)([]),[f,g]=(0,m.useState)(!1),[h,i]=(0,m.useState)(""),{showToast:j}=(0,F.d)(),{token:l,axiosJWT:n}=(0,m.useContext)(H.A),o=a=>new Intl.NumberFormat("id-ID",{maximumFractionDigits:0}).format(a),p=async()=>{let a=`
      SELECT 
        prk.kddept,
        td.nmdept,
        SUM(prk.pagu) AS pagu,
        SUM(prk.realisasi) AS realisasi,
        SUM(prk.realisasi) / NULLIF(SUM(prk.pagu), 0) * 100 AS persen
      FROM dashboard.pagu_real_kl prk
      LEFT JOIN dbref.t_dept_2025 td ON prk.kddept = td.kddept
      WHERE prk.thang = '2022' 
      GROUP BY prk.kddept
      ORDER BY pagu DESC
    `.replace(/\s+/g," ").trim();try{g(!0);let b=(0,G.A)(a),c=((await n.post("http://localhost:88/next/referensi",{query:b})).data.result||[]).map(a=>({kddept:a.kddept||"",nmdept:a.nmdept||"",pagu:a.pagu||0,realisasi:a.realisasi||0,persen:a.persen||0}));e(c)}catch(a){j(a.response?.data?.error||"Terjadi kesalahan saat memuat data","error")}finally{g(!1)}};(0,m.useEffect)(()=>{a&&p()},[a]);let q=c.filter(a=>`${a.kddept} ${a.nmdept}`.toLowerCase().includes(h.toLowerCase()));return(0,d.jsx)(I.Y,{isOpen:a,onClose:b,size:"5xl",scrollBehavior:"inside",children:(0,d.jsxs)(J.g,{children:[(0,d.jsx)(K.c,{children:"Detail Performa Kementerian/Lembaga"}),(0,d.jsxs)(L.h,{className:"p-4",children:[(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)(M.r,{placeholder:"Cari Kementerian atau Kode...",value:h,onValueChange:i,startContent:(0,d.jsx)(U.A,{size:18}),variant:"bordered",radius:"sm",size:"sm",className:"w-full sm:w-1/2"})}),(0,d.jsx)("div",{className:"relative min-h-[60vh] overflow-auto",children:(0,d.jsxs)(N.j,{isStriped:!0,isHeaderSticky:!0,"aria-label":"Tabel Performa KL",removeWrapper:!0,children:[(0,d.jsx)(O.X,{children:["Nama Kementerian","Pagu","Realisasi","Persentase"].map((a,b)=>(0,d.jsx)(P.e,{className:"top-0 z-10 bg-gradient-to-r from-blue-150 to-indigo-50 text-center font-semibold text-sm text-gray-800 border-b-2 border-blue-200 shadow-sm",children:a},b))}),(0,d.jsx)(Q.E,{children:(f?Array.from({length:8},(a,b)=>({kddept:`skeleton-${b}`,nmdept:"",pagu:0,realisasi:0,persen:0})):q).map(a=>(0,d.jsxs)(R.s,{children:[(0,d.jsx)(S.w,{className:"text-left align-top",children:f?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"h-4 w-40 bg-gray-200 rounded animate-pulse mb-1"}),(0,d.jsx)("div",{className:"h-3 w-24 bg-gray-100 rounded animate-pulse"})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"font-medium",children:a.nmdept}),(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:["(",a.kddept,")"]})]})}),(0,d.jsx)(S.w,{className:"text-right align-top",children:f?(0,d.jsx)("div",{className:"h-4 w-24 bg-gray-200 rounded animate-pulse ml-auto"}):o(a.pagu)}),(0,d.jsx)(S.w,{className:"text-right align-top",children:f?(0,d.jsx)("div",{className:"h-4 w-24 bg-gray-200 rounded animate-pulse ml-auto"}):o(a.realisasi)}),(0,d.jsx)(S.w,{className:"text-right align-top",children:f?(0,d.jsx)("div",{className:"h-4 w-16 bg-gray-200 rounded animate-pulse ml-auto"}):`${Number(a.persen).toFixed(2)}%`})]},a.kddept))})]})})]}),(0,d.jsx)(T.q,{children:(0,d.jsx)(k.T,{color:"danger",onPress:b,startContent:(0,d.jsx)(V.A,{size:16}),children:"Tutup"})})]})})},X=({selectedKanwil:a,selectedKddept:b})=>{let[c,e]=(0,m.useState)([]),[j,k]=(0,m.useState)(null),[n,o]=(0,m.useState)(!1),{theme:p}=(0,l.D)(),{showToast:q}=(0,F.d)(),[r,s]=(0,m.useState)(!1),{token:t,axiosJWT:u}=(0,m.useContext)(H.A),v=()=>{let a="dark"===p;return{cardBg:a?"bg-gradient-to-br from-slate-800 to-slate-700":"bg-gradient-to-br from-slate-100 to-slate-200",skeletonCardBg:a?"bg-gradient-to-br from-slate-800 to-slate-700":"bg-gradient-to-br from-default-50 to-default-100",skeletonItemBg:a?"bg-slate-700/50":"bg-default-50",textPrimary:a?"text-slate-100":"text-slate-900",textSecondary:a?"text-slate-300":"text-slate-700",textMuted:a?"text-slate-400":"text-slate-600"}},w=a=>new Intl.NumberFormat("id-ID",{minimumFractionDigits:2,maximumFractionDigits:2}).format(a)+" T",x=async()=>{let c="";a&&"00"!==a&&(c=` and kdkanwil='${a}'`);let d="";b&&"000"!==b&&(d=` and kddept='${b}'`);let f=decodeURIComponent(encodeURIComponent(`SELECT 
  ${b&&"000"!==b?"kddept,":""}
  jenbel,
  CASE
    WHEN jenbel = '51' THEN 'Pegawai'
    WHEN jenbel = '52' THEN 'Barang'
    WHEN jenbel = '53' THEN 'Modal'
    WHEN jenbel = '57' THEN 'Sosial'
    ELSE 'Lainnya'
  END AS jenis_belanja,
  SUM(pagu) AS pagu,
  SUM(realisasi) AS realisasi,
  SUM(realisasi) / NULLIF(SUM(pagu),0) * 100 AS persen
FROM dashboard.pagu_real_jenbel 
WHERE thang = '2022' ${c}${d}
GROUP BY jenbel;`)).replace(/\n/g," ").replace(/\s+/g," ").trim(),g=(0,G.A)(f);try{o(!0),k(null);let a=(await u.post("http://localhost:88/next/referensi",{query:g})).data.result||[];e(a)}catch(b){let{data:a}=b.response||{};q(a&&a.error,"error")}finally{o(!1)}};return(0,m.useEffect)(()=>{x()},[a,b]),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(f.Z,{className:`border-none shadow-sm ${n?v().skeletonCardBg:v().cardBg} lg:col-span-6 xl:col-span-3`,children:[(0,d.jsx)(g.d,{className:"pb-2 px-4 md:px-6",children:(0,d.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,d.jsx)("h3",{className:`text-sm md:text-base font-semibold ${v().textPrimary}`,children:"Jenis Belanja Terbesar"}),(0,d.jsx)(i.R,{color:"primary",onClick:()=>s(!0),variant:"flat",size:"sm",className:"w-fit cursor-pointer",children:"Detail"})]})}),n?(0,d.jsx)(h.U,{className:"pt-0 px-4 md:px-6",children:"Loading..."}):0===c.length?(0,d.jsx)(h.U,{className:"pt-0 px-4 md:px-6",children:"Tidak ada data"}):(()=>{let a=c.map(a=>{let b=a.pagu>0?a.realisasi/a.pagu*100:0;return{name:a.jenis_belanja,budget:w(a.pagu/1e12),realized:w(a.realisasi/1e12),percentage:Math.round(b),status:b>=80?"excellent":b>=60?"on-track":"warning"}}).sort((a,b)=>{let c=parseFloat(a.budget.replace(/[^\d.-]/g,""));return parseFloat(b.budget.replace(/[^\d.-]/g,""))-c});return(0,d.jsx)(h.U,{className:"pt-0 px-4 md:px-6",children:(0,d.jsx)("div",{className:"space-y-0",children:a.slice(0,4).map((a,b)=>(0,d.jsx)("div",{className:"flex items-center justify-between p-0.5 md:p-1 rounded-lg hover:bg-black/5 dark:hover:bg-white/5 transition-colors duration-200",children:(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("div",{className:"mb-0.5",children:(0,d.jsx)("h4",{className:`font-medium text-xs truncate pr-2 ${v().textSecondary}`,children:a.name})}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("div",{className:"relative flex-1",children:[(0,d.jsx)(E,{value:a.percentage,color:"excellent"===a.status?"success":"on-track"===a.status?"primary":"warning",size:"md","aria-label":"belanjaterbesar",className:"w-full h-6"}),(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,d.jsxs)("span",{className:`text-xs font-semibold drop-shadow-sm ${"dark"===p?"text-white":"text-slate-800"}`,children:["Rp ",a.realized," / Rp ",a.budget]})})]}),(0,d.jsxs)(i.R,{size:"sm",variant:"flat",color:"excellent"===a.status?"success":"on-track"===a.status?"primary":"warning",className:"text-xs flex-shrink-0 font-semibold shadow-sm",children:[a.percentage,"%"]})]})]})},b))})})})()]}),r&&(0,d.jsx)(W,{isOpen:r,onClose:()=>s(!1)})]})};var Y=c(62688);let Z=(0,Y.A)("wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]]);var $=c(25541);let _=(0,Y.A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var aa=c(17313),ab=c(28947);let ac=(0,Y.A)("arrow-up-right",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]),ad=(0,Y.A)("arrow-down-right",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]]),ae=({selectedKanwil:a,selectedKddept:b})=>{let[c,e]=(0,m.useState)(null),[g,i]=(0,m.useState)(!1),j=(0,m.useContext)(H.A),{theme:k}=(0,l.D)(),{showToast:n}=(0,F.d)(),{token:o,axiosJWT:p}=j||{},q=()=>{let a="dark"===k;return{cardBg:a?"bg-gradient-to-br from-slate-800/90 to-slate-700/90":"bg-gradient-to-br from-white/90 to-slate-50/90",cardBgPrimary:a?"bg-gradient-to-br from-blue-900/80 to-blue-800/70":"bg-gradient-to-br from-blue-50 to-blue-100/90",cardBgSuccess:a?"bg-gradient-to-br from-green-900/80 to-green-800/70":"bg-gradient-to-br from-green-50 to-green-100/90",cardBgWarning:a?"bg-gradient-to-br from-amber-900/80 to-amber-800/70":"bg-gradient-to-br from-amber-50 to-amber-100/90",cardBgSecondary:a?"bg-gradient-to-br from-purple-900/80 to-purple-800/70":"bg-gradient-to-br from-purple-50 to-purple-100/90",textPrimary:a?"text-slate-100":"text-slate-900",textSecondary:a?"text-slate-300":"text-slate-600"}},r=async()=>{let c=a&&"00"!==a?` and kdkanwil='${a}'`:"",d=b&&"000"!==b?` and kddept='${b}'`:"",f=decodeURIComponent(`
      SELECT SUM(pagu)/1000000000000 AS pagu, SUM(realisasi)/1000000000000 AS realisasi,
      (SUM(pagu) - SUM(realisasi))/1000000000000 AS sisa,
      (SELECT SUM(jml) FROM dashboard.dipa_satker_rekap where thang='2022'${c}${d}) AS jumlah_dipa,
      ROUND(SUM(persen_dipa) / 1000000, 2) AS persen
      FROM dashboard.pagu_real_kl prk
      WHERE thang='2022' and kddept<>'999'${c}${d} LIMIT 1;
    `).replace(/\n/g," ").replace(/\s+/g," ").trim();try{i(!0);let a=await p.post("http://localhost:88/next/referensi",{query:(0,G.A)(f)});e(a.data.result?.[0]||null)}catch(b){let{data:a}=b.response||{};n(a&&a.error,"error")}finally{i(!1)}};(0,m.useEffect)(()=>{r()},[a,b]);let s=a=>`${new Intl.NumberFormat("id-ID",{minimumFractionDigits:2,maximumFractionDigits:2}).format(a)} T`,t=a=>new Intl.NumberFormat("id-ID").format(a);if(g||!c)return null;let u=c.realisasi/c.pagu*100,v=c.sisa/c.pagu*100,w=[{title:"Total Pagu",value:s(c.pagu),icon:Z,color:"primary",trend:"up",change:"+2.5%"},{title:"Realisasi",value:s(c.realisasi),icon:$.A,color:"success",trend:u>50?"up":"down",change:`${u.toFixed(1)}%`},{title:"Sisa Anggaran",value:s(c.sisa),icon:_,color:"warning",trend:v>30?"up":"down",change:`${v.toFixed(1)}%`},{title:"Jumlah DIPA",value:t(c.jumlah_dipa),icon:aa.A,color:"secondary",trend:"up",change:`${t(c.jumlah_dipa)} unit`},{title:"Efisiensi Anggaran",value:`${c.persen}%`,icon:ab.A,color:"primary",trend:c.persen>80?"up":"down",change:`${c.persen}%`}];return(0,d.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-12 gap-2 md:gap-3 mt-2 md:mt-3",children:w.map((a,b)=>(0,d.jsx)(f.Z,{className:`border-none shadow-sm hover:shadow-md transition-shadow ${q()[`cardBg${a.color.charAt(0).toUpperCase()+a.color.slice(1)}`]||q().cardBg} ${0===b||1===b||2===b?"lg:col-span-2":"lg:col-span-3"}`,children:(0,d.jsx)(h.U,{className:"p-2 md:p-3",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1 md:gap-2 min-w-0 flex-1",children:[(0,d.jsx)("div",{className:`p-1 md:p-1.5 rounded-lg bg-${a.color}/20 border border-${a.color}/30 flex-shrink-0`,children:(0,d.jsx)(a.icon,{className:`h-3 w-3 md:h-4 md:w-4 text-${a.color}`})}),(0,d.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,d.jsx)("p",{className:`text-xs ${q().textSecondary} truncate`,children:a.title}),(0,d.jsx)("p",{className:`text-sm md:text-lg font-semibold ${q().textPrimary} truncate`,children:a.value})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:["up"===a.trend?(0,d.jsx)(ac,{className:"h-2 w-2 md:h-3 md:w-3 text-success-600"}):(0,d.jsx)(ad,{className:"h-2 w-2 md:h-3 md:w-3 text-danger-600"}),(0,d.jsx)("span",{className:`text-xs font-medium ${"up"===a.trend?"text-success-600":"text-danger-600"}`,children:a.change})]})]})})},b))})};var af=c(49867);let ag=(0,Y.A)("file-x",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m14.5 12.5-5 5",key:"b62r18"}],["path",{d:"m9.5 12.5 5 5",key:"1rk7el"}]]);var ah=c(61611);let ai=({selectedKanwil:a,selectedKddept:b})=>{let[c,j]=(0,m.useState)([]),[k,n]=(0,m.useState)(null),[o,p]=(0,m.useState)(!1),q=(0,m.useContext)(H.A),{theme:r}=(0,l.D)(),[s,t]=(0,m.useState)(!1),{token:u,axiosJWT:v}=q,w=()=>{let a="dark"===r;return{cardBg:a?"bg-gradient-to-br from-slate-800 to-slate-700":"bg-gradient-to-br from-slate-100 to-slate-200",skeletonCardBg:a?"bg-gradient-to-br from-slate-800 to-slate-700":"bg-gradient-to-br from-default-50 to-default-100",skeletonItemBg:a?"bg-slate-700/50":"bg-default-50",textPrimary:a?"text-slate-100":"text-slate-900",textSecondary:a?"text-slate-300":"text-slate-700",textMuted:a?"text-slate-400":"text-slate-600"}},x=a=>new Intl.NumberFormat("id-ID",{minimumFractionDigits:2,maximumFractionDigits:2}).format(a)+" T",y=async()=>{let c=a&&"00"!==a?` and prk.kdkanwil='${a}'`:"",d=b&&"000"!==b?` and prk.kddept='${b}'`:"",e=decodeURIComponent(encodeURIComponent(`SELECT 
      prk.kddept,
      td.nmdept,
      SUM(prk.pagu) AS pagu,
      SUM(prk.realisasi) AS realisasi,
      SUM(prk.realisasi) / NULLIF(SUM(prk.pagu), 0) * 100 AS persen
    FROM dashboard.pagu_real_kl prk
    LEFT JOIN dbref.t_dept_2025 td ON prk.kddept = td.kddept
    WHERE prk.thang = '2022'${c}${d}
    GROUP BY prk.kddept
    ORDER BY pagu DESC
    LIMIT 4;`)).replace(/\n/g," ").replace(/\s+/g," ").trim(),f=(0,G.A)(e);try{p(!0),n(null);let a=(await v.post("http://localhost:88/next/referensi",{query:f})).data.result||[];j(a)}catch(c){let{status:a,data:b}=c.response||{};j([]),(0,af.t)(a,b&&b.error||"Terjadi Permasalahan Koneksi atau Server Backend")}finally{p(!1)}};return(0,m.useEffect)(()=>{y()},[]),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(f.Z,{className:`border-none shadow-sm ${o?w().skeletonCardBg:w().cardBg} lg:col-span-6 xl:col-span-3`,children:[(0,d.jsx)(()=>(0,d.jsx)(g.d,{className:"pb-2 px-4 md:px-6",children:(0,d.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,d.jsx)("h3",{className:`text-sm md:text-base font-semibold ${w().textPrimary}`,children:"Performa K/L Terbesar"}),(0,d.jsx)(i.R,{color:"primary",onClick:()=>t(!0),variant:"flat",size:"sm",className:"w-fit cursor-pointer",children:"Detail"})]})}),{}),o?(0,d.jsx)(h.U,{className:"pt-0 px-4 md:px-6",children:(0,d.jsx)("div",{className:"space-y-2 md:space-y-3",children:Array.from({length:2}).map((a,b)=>(0,d.jsxs)("div",{className:`p-2 md:p-3 ${w().skeletonItemBg} rounded-lg space-y-2`,children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)(e.m,{className:"h-4 w-40 rounded"}),(0,d.jsx)(e.m,{className:"h-6 w-12 rounded-full"})]}),(0,d.jsx)(e.m,{className:"h-3 w-32 rounded"}),(0,d.jsx)(e.m,{className:"h-2 w-full rounded-full"})]},b))})}):0===c.length?(0,d.jsx)(h.U,{className:"pt-0 px-4 md:px-6",children:(0,d.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,d.jsx)(ag,{className:"w-12 h-12 text-default-400 mb-4"}),(0,d.jsx)("div",{className:"mt-4",children:(0,d.jsx)(i.R,{size:"sm",variant:"flat",color:"warning",startContent:(0,d.jsx)(ah.A,{className:"w-3 h-3"}),className:"text-xs",children:"Data Tidak Tersedia"})})]})}):(()=>{let a=c.map(a=>{let b=a.pagu>0?a.realisasi/a.pagu*100:0;return{name:a.nmdept||`K/L ${a.kddept}`,budget:x(a.pagu/1e12),realized:x(a.realisasi/1e12),percentage:Math.round(b),status:b>=90?"excellent":b>=80?"on-track":"warning"}}).sort((a,b)=>b.percentage-a.percentage);return(0,d.jsx)(h.U,{className:"pt-0 px-4 md:px-6",children:(0,d.jsx)("div",{className:"space-y-0",children:a.slice(0,4).map((a,b)=>(0,d.jsx)("div",{className:"flex items-center justify-between p-0.5 md:p-1 rounded-lg hover:bg-black/5 dark:hover:bg-white/5 transition-colors duration-200",children:(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("div",{className:"mb-0.5",children:(0,d.jsx)("h4",{className:`font-medium text-xs truncate pr-2 ${w().textSecondary}`,children:a.name})}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("div",{className:"relative flex-1",children:[(0,d.jsx)(E,{value:a.percentage,color:"excellent"===a.status?"success":"on-track"===a.status?"primary":"warning","aria-label":"performaterbesar",size:"md",className:"w-full h-6",classNames:{track:"h-6 bg-gradient-to-r from-default-100 to-default-200 dark:from-slate-700 dark:to-slate-600 shadow-inner rounded-full border border-default-200 dark:border-slate-600",indicator:`h-6 rounded-full shadow-lg transition-all duration-500 ease-out ${"excellent"===a.status?"bg-gradient-to-r from-green-400 to-emerald-500 shadow-green-200 dark:shadow-green-900/50":"on-track"===a.status?"bg-gradient-to-r from-blue-400 to-indigo-500 shadow-blue-200 dark:shadow-blue-900/50":"bg-gradient-to-r from-amber-400 to-orange-500 shadow-amber-200 dark:shadow-amber-900/50"}`,label:"hidden"}}),(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,d.jsxs)("span",{className:`text-xs font-semibold drop-shadow-sm ${"dark"===r?"text-white":"text-slate-800"}`,children:["Rp ",a.realized," / Rp ",a.budget]})})]}),(0,d.jsxs)(i.R,{size:"sm",variant:"flat",color:"excellent"===a.status?"success":"on-track"===a.status?"primary":"warning",className:"text-xs flex-shrink-0 font-semibold shadow-sm",children:[a.percentage,"%"]})]})]})},b))})})})()]}),s&&(0,d.jsx)(W,{isOpen:s,onClose:()=>t(!1)})]})};var aj=c(8916),ak=c(45175),al=c(7239),am=c(52791),an=c(32168),ao=c(45912);function ap(a,b,c){if(null==a){var d,e;if(null!=b)return null!=(e=null==(d=c.getItem(b))?void 0:d.textValue)?e:""}return a}var aq=c(8283),ar=(0,p.tv)({slots:{base:"group inline-flex flex-column w-full",listboxWrapper:"scroll-py-6 w-full",listbox:"",popoverContent:"w-full p-1 overflow-hidden",endContentWrapper:"relative flex h-full items-center -mr-2",clearButton:["text-medium","translate-x-1","cursor-text","opacity-0","pointer-events-none","text-default-500","group-data-[invalid=true]:text-danger","data-[visible=true]:opacity-100","data-[visible=true]:pointer-events-auto","data-[visible=true]:cursor-pointer","sm:data-[visible=true]:opacity-0","sm:data-[visible=true]:pointer-events-none","sm:group-data-[hover=true]:data-[visible=true]:opacity-100","sm:group-data-[hover=true]:data-[visible=true]:pointer-events-auto"],selectorButton:"text-medium"},variants:{isClearable:{true:{},false:{clearButton:"hidden"}},disableAnimation:{true:{selectorButton:"transition-none"},false:{selectorButton:"transition-transform duration-150 ease motion-reduce:transition-none"}},disableSelectorIconRotation:{true:{},false:{selectorButton:"data-[open=true]:rotate-180"}}},defaultVariants:{isClearable:!0,disableSelectorIconRotation:!1}}),as=c(39665),at={};at={"ar-AE":{buttonLabel:`\u{639}\u{631}\u{636} \u{627}\u{644}\u{645}\u{642}\u{62A}\u{631}\u{62D}\u{627}\u{62A}`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} \u{62E}\u{64A}\u{627}\u{631}`,other:()=>`${b.number(a.optionCount)} \u{62E}\u{64A}\u{627}\u{631}\u{627}\u{62A}`})} \u{645}\u{62A}\u{627}\u{62D}\u{629}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`\u{627}\u{644}\u{645}\u{62C}\u{645}\u{648}\u{639}\u{629} \u{627}\u{644}\u{645}\u{62F}\u{62E}\u{644}\u{629} ${a.groupTitle}, \u{645}\u{639} ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} \u{62E}\u{64A}\u{627}\u{631}`,other:()=>`${b.number(a.groupCount)} \u{62E}\u{64A}\u{627}\u{631}\u{627}\u{62A}`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, \u{645}\u{62D}\u{62F}\u{62F}`,other:""},a.isSelected)}`,listboxLabel:`\u{645}\u{642}\u{62A}\u{631}\u{62D}\u{627}\u{62A}`,selectedAnnouncement:a=>`${a.optionText}\u{60C} \u{645}\u{62D}\u{62F}\u{62F}`},"bg-BG":{buttonLabel:`\u{41F}\u{43E}\u{43A}\u{430}\u{436}\u{438} \u{43F}\u{440}\u{435}\u{434}\u{43B}\u{43E}\u{436}\u{435}\u{43D}\u{438}\u{44F}`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} \u{43E}\u{43F}\u{446}\u{438}\u{44F}`,other:()=>`${b.number(a.optionCount)} \u{43E}\u{43F}\u{446}\u{438}\u{438}`})} \u{43D}\u{430} \u{440}\u{430}\u{437}\u{43F}\u{43E}\u{43B}\u{43E}\u{436}\u{435}\u{43D}\u{438}\u{435}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`\u{412}\u{44A}\u{432}\u{435}\u{434}\u{435}\u{43D}\u{430} \u{433}\u{440}\u{443}\u{43F}\u{430} ${a.groupTitle}, \u{441} ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} \u{43E}\u{43F}\u{446}\u{438}\u{44F}`,other:()=>`${b.number(a.groupCount)} \u{43E}\u{43F}\u{446}\u{438}\u{438}`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, \u{438}\u{437}\u{431}\u{440}\u{430}\u{43D}\u{438}`,other:""},a.isSelected)}`,listboxLabel:`\u{41F}\u{440}\u{435}\u{434}\u{43B}\u{43E}\u{436}\u{435}\u{43D}\u{438}\u{44F}`,selectedAnnouncement:a=>`${a.optionText}, \u{438}\u{437}\u{431}\u{440}\u{430}\u{43D}\u{438}`},"cs-CZ":{buttonLabel:`Zobrazit doporu\u{10D}en\xed`,countAnnouncement:(a,b)=>`K dispozici ${b.plural(a.optionCount,{one:()=>`je ${b.number(a.optionCount)} mo\u{17E}nost`,other:()=>`jsou/je ${b.number(a.optionCount)} mo\u{17E}nosti/-\xed`})}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Zadan\xe1 skupina \u{201E}${a.groupTitle}\u{201C} ${b.plural(a.groupCount,{one:()=>`s ${b.number(a.groupCount)} mo\u{17E}nost\xed`,other:()=>`se ${b.number(a.groupCount)} mo\u{17E}nostmi`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:` (vybr\xe1no)`,other:""},a.isSelected)}`,listboxLabel:`N\xe1vrhy`,selectedAnnouncement:a=>`${a.optionText}, vybr\xe1no`},"da-DK":{buttonLabel:"Vis forslag",countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} mulighed tilg\xe6ngelig`,other:()=>`${b.number(a.optionCount)} muligheder tilg\xe6ngelige`})}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Angivet gruppe ${a.groupTitle}, med ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} mulighed`,other:()=>`${b.number(a.groupCount)} muligheder`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", valgt",other:""},a.isSelected)}`,listboxLabel:"Forslag",selectedAnnouncement:a=>`${a.optionText}, valgt`},"de-DE":{buttonLabel:"Empfehlungen anzeigen",countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} Option`,other:()=>`${b.number(a.optionCount)} Optionen`})} verf\xfcgbar.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Eingetretene Gruppe ${a.groupTitle}, mit ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} Option`,other:()=>`${b.number(a.groupCount)} Optionen`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, ausgew\xe4hlt`,other:""},a.isSelected)}`,listboxLabel:"Empfehlungen",selectedAnnouncement:a=>`${a.optionText}, ausgew\xe4hlt`},"el-GR":{buttonLabel:`\u{3A0}\u{3C1}\u{3BF}\u{3B2}\u{3BF}\u{3BB}\u{3AE} \u{3C0}\u{3C1}\u{3BF}\u{3C4}\u{3AC}\u{3C3}\u{3B5}\u{3C9}\u{3BD}`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} \u{3B5}\u{3C0}\u{3B9}\u{3BB}\u{3BF}\u{3B3}\u{3AE}`,other:()=>`${b.number(a.optionCount)} \u{3B5}\u{3C0}\u{3B9}\u{3BB}\u{3BF}\u{3B3}\u{3AD}\u{3C2} `})} \u{3B4}\u{3B9}\u{3B1}\u{3B8}\u{3AD}\u{3C3}\u{3B9}\u{3BC}\u{3B5}\u{3C2}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`\u{395}\u{3B9}\u{3C3}\u{3B1}\u{3B3}\u{3BC}\u{3AD}\u{3BD}\u{3B7} \u{3BF}\u{3BC}\u{3AC}\u{3B4}\u{3B1} ${a.groupTitle}, \u{3BC}\u{3B5} ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} \u{3B5}\u{3C0}\u{3B9}\u{3BB}\u{3BF}\u{3B3}\u{3AE}`,other:()=>`${b.number(a.groupCount)} \u{3B5}\u{3C0}\u{3B9}\u{3BB}\u{3BF}\u{3B3}\u{3AD}\u{3C2}`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, \u{3B5}\u{3C0}\u{3B9}\u{3BB}\u{3B5}\u{3B3}\u{3BC}\u{3AD}\u{3BD}\u{3BF}`,other:""},a.isSelected)}`,listboxLabel:`\u{3A0}\u{3C1}\u{3BF}\u{3C4}\u{3AC}\u{3C3}\u{3B5}\u{3B9}\u{3C2}`,selectedAnnouncement:a=>`${a.optionText}, \u{3B5}\u{3C0}\u{3B9}\u{3BB}\u{3AD}\u{3C7}\u{3B8}\u{3B7}\u{3BA}\u{3B5}`},"en-US":{focusAnnouncement:(a,b)=>`${b.select({true:()=>`Entered group ${a.groupTitle}, with ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} option`,other:()=>`${b.number(a.groupCount)} options`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", selected",other:""},a.isSelected)}`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} option`,other:()=>`${b.number(a.optionCount)} options`})} available.`,selectedAnnouncement:a=>`${a.optionText}, selected`,buttonLabel:"Show suggestions",listboxLabel:"Suggestions"},"es-ES":{buttonLabel:"Mostrar sugerencias",countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} opci\xf3n`,other:()=>`${b.number(a.optionCount)} opciones`})} disponible(s).`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Se ha unido al grupo ${a.groupTitle}, con ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} opci\xf3n`,other:()=>`${b.number(a.groupCount)} opciones`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", seleccionado",other:""},a.isSelected)}`,listboxLabel:"Sugerencias",selectedAnnouncement:a=>`${a.optionText}, seleccionado`},"et-EE":{buttonLabel:"Kuva soovitused",countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} valik`,other:()=>`${b.number(a.optionCount)} valikud`})} saadaval.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Sisestatud r\xfchm ${a.groupTitle}, valikuga ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} valik`,other:()=>`${b.number(a.groupCount)} valikud`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", valitud",other:""},a.isSelected)}`,listboxLabel:"Soovitused",selectedAnnouncement:a=>`${a.optionText}, valitud`},"fi-FI":{buttonLabel:`N\xe4yt\xe4 ehdotukset`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} vaihtoehto`,other:()=>`${b.number(a.optionCount)} vaihtoehdot`})} saatavilla.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Mentiin ryhm\xe4\xe4n ${a.groupTitle}, ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} vaihtoehdon`,other:()=>`${b.number(a.groupCount)} vaihtoehdon`})} kanssa.`,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", valittu",other:""},a.isSelected)}`,listboxLabel:"Ehdotukset",selectedAnnouncement:a=>`${a.optionText}, valittu`},"fr-FR":{buttonLabel:"Afficher les suggestions",countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} option`,other:()=>`${b.number(a.optionCount)} options`})} disponible(s).`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Groupe ${a.groupTitle} rejoint, avec ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} option`,other:()=>`${b.number(a.groupCount)} options`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, s\xe9lectionn\xe9(s)`,other:""},a.isSelected)}`,listboxLabel:"Suggestions",selectedAnnouncement:a=>`${a.optionText}, s\xe9lectionn\xe9`},"he-IL":{buttonLabel:`\u{5D4}\u{5E6}\u{5D2} \u{5D4}\u{5E6}\u{5E2}\u{5D5}\u{5EA}`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`\u{5D0}\u{5E4}\u{5E9}\u{5E8}\u{5D5}\u{5EA} ${b.number(a.optionCount)}`,other:()=>`${b.number(a.optionCount)} \u{5D0}\u{5E4}\u{5E9}\u{5E8}\u{5D5}\u{5D9}\u{5D5}\u{5EA}`})} \u{5D1}\u{5DE}\u{5E6}\u{5D1} \u{5D6}\u{5DE}\u{5D9}\u{5DF}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`\u{5E0}\u{5DB}\u{5E0}\u{5E1} \u{5DC}\u{5E7}\u{5D1}\u{5D5}\u{5E6}\u{5D4} ${a.groupTitle}, \u{5E2}\u{5DD} ${b.plural(a.groupCount,{one:()=>`\u{5D0}\u{5E4}\u{5E9}\u{5E8}\u{5D5}\u{5EA} ${b.number(a.groupCount)}`,other:()=>`${b.number(a.groupCount)} \u{5D0}\u{5E4}\u{5E9}\u{5E8}\u{5D5}\u{5D9}\u{5D5}\u{5EA}`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, \u{5E0}\u{5D1}\u{5D7}\u{5E8}`,other:""},a.isSelected)}`,listboxLabel:`\u{5D4}\u{5E6}\u{5E2}\u{5D5}\u{5EA}`,selectedAnnouncement:a=>`${a.optionText}, \u{5E0}\u{5D1}\u{5D7}\u{5E8}`},"hr-HR":{buttonLabel:`Prika\u{17E}i prijedloge`,countAnnouncement:(a,b)=>`Dostupno jo\u{161}: ${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} opcija`,other:()=>`${b.number(a.optionCount)} opcije/a`})}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Unesena skupina ${a.groupTitle}, s ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} opcijom`,other:()=>`${b.number(a.groupCount)} opcije/a`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", odabranih",other:""},a.isSelected)}`,listboxLabel:"Prijedlozi",selectedAnnouncement:a=>`${a.optionText}, odabrano`},"hu-HU":{buttonLabel:`Javaslatok megjelen\xedt\xe9se`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} lehet\u{151}s\xe9g`,other:()=>`${b.number(a.optionCount)} lehet\u{151}s\xe9g`})} \xe1ll rendelkez\xe9sre.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Bel\xe9pett a(z) ${a.groupTitle} csoportba, amely ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} lehet\u{151}s\xe9get`,other:()=>`${b.number(a.groupCount)} lehet\u{151}s\xe9get`})} tartalmaz. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, kijel\xf6lve`,other:""},a.isSelected)}`,listboxLabel:"Javaslatok",selectedAnnouncement:a=>`${a.optionText}, kijel\xf6lve`},"it-IT":{buttonLabel:"Mostra suggerimenti",countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} opzione disponibile`,other:()=>`${b.number(a.optionCount)} opzioni disponibili`})}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Ingresso nel gruppo ${a.groupTitle}, con ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} opzione`,other:()=>`${b.number(a.groupCount)} opzioni`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", selezionato",other:""},a.isSelected)}`,listboxLabel:"Suggerimenti",selectedAnnouncement:a=>`${a.optionText}, selezionato`},"ja-JP":{buttonLabel:`\u{5019}\u{88DC}\u{3092}\u{8868}\u{793A}`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} \u{500B}\u{306E}\u{30AA}\u{30D7}\u{30B7}\u{30E7}\u{30F3}`,other:()=>`${b.number(a.optionCount)} \u{500B}\u{306E}\u{30AA}\u{30D7}\u{30B7}\u{30E7}\u{30F3}`})}\u{3092}\u{5229}\u{7528}\u{3067}\u{304D}\u{307E}\u{3059}\u{3002}`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`\u{5165}\u{529B}\u{3055}\u{308C}\u{305F}\u{30B0}\u{30EB}\u{30FC}\u{30D7} ${a.groupTitle}\u{3001}${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} \u{500B}\u{306E}\u{30AA}\u{30D7}\u{30B7}\u{30E7}\u{30F3}`,other:()=>`${b.number(a.groupCount)} \u{500B}\u{306E}\u{30AA}\u{30D7}\u{30B7}\u{30E7}\u{30F3}`})}\u{3092}\u{542B}\u{3080}\u{3002}`,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`\u{3001}\u{9078}\u{629E}\u{6E08}\u{307F}`,other:""},a.isSelected)}`,listboxLabel:`\u{5019}\u{88DC}`,selectedAnnouncement:a=>`${a.optionText}\u{3001}\u{9078}\u{629E}\u{6E08}\u{307F}`},"ko-KR":{buttonLabel:`\u{C81C}\u{C548} \u{C0AC}\u{D56D} \u{D45C}\u{C2DC}`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)}\u{AC1C} \u{C635}\u{C158}`,other:()=>`${b.number(a.optionCount)}\u{AC1C} \u{C635}\u{C158}`})}\u{C744} \u{C0AC}\u{C6A9}\u{D560} \u{C218} \u{C788}\u{C2B5}\u{B2C8}\u{B2E4}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`\u{C785}\u{B825}\u{D55C} \u{ADF8}\u{B8F9} ${a.groupTitle}, ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)}\u{AC1C} \u{C635}\u{C158}`,other:()=>`${b.number(a.groupCount)}\u{AC1C} \u{C635}\u{C158}`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, \u{C120}\u{D0DD}\u{B428}`,other:""},a.isSelected)}`,listboxLabel:`\u{C81C}\u{C548}`,selectedAnnouncement:a=>`${a.optionText}, \u{C120}\u{D0DD}\u{B428}`},"lt-LT":{buttonLabel:`Rodyti pasi\u{16B}lymus`,countAnnouncement:(a,b)=>`Yra ${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} parinktis`,other:()=>`${b.number(a.optionCount)} parinktys (-i\u{173})`})}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`\u{12E}vesta grup\u{117} ${a.groupTitle}, su ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} parinktimi`,other:()=>`${b.number(a.groupCount)} parinktimis (-i\u{173})`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", pasirinkta",other:""},a.isSelected)}`,listboxLabel:`Pasi\u{16B}lymai`,selectedAnnouncement:a=>`${a.optionText}, pasirinkta`},"lv-LV":{buttonLabel:`R\u{101}d\u{12B}t ieteikumus`,countAnnouncement:(a,b)=>`Pieejamo opciju skaits: ${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} opcija`,other:()=>`${b.number(a.optionCount)} opcijas`})}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Ievad\u{12B}ta grupa ${a.groupTitle}, ar ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} opciju`,other:()=>`${b.number(a.groupCount)} opcij\u{101}m`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, atlas\u{12B}ta`,other:""},a.isSelected)}`,listboxLabel:"Ieteikumi",selectedAnnouncement:a=>`${a.optionText}, atlas\u{12B}ta`},"nb-NO":{buttonLabel:"Vis forslag",countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} alternativ`,other:()=>`${b.number(a.optionCount)} alternativer`})} finnes.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Angitt gruppe ${a.groupTitle}, med ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} alternativ`,other:()=>`${b.number(a.groupCount)} alternativer`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", valgt",other:""},a.isSelected)}`,listboxLabel:"Forslag",selectedAnnouncement:a=>`${a.optionText}, valgt`},"nl-NL":{buttonLabel:"Suggesties weergeven",countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} optie`,other:()=>`${b.number(a.optionCount)} opties`})} beschikbaar.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Groep ${a.groupTitle} ingevoerd met ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} optie`,other:()=>`${b.number(a.groupCount)} opties`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", geselecteerd",other:""},a.isSelected)}`,listboxLabel:"Suggesties",selectedAnnouncement:a=>`${a.optionText}, geselecteerd`},"pl-PL":{buttonLabel:`Wy\u{15B}wietlaj sugestie`,countAnnouncement:(a,b)=>`dost\u{119}pna/dost\u{119}pne(-nych) ${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} opcja`,other:()=>`${b.number(a.optionCount)} opcje(-i)`})}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Do\u{142}\u{105}czono do grupy ${a.groupTitle}, z ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} opcj\u{105}`,other:()=>`${b.number(a.groupCount)} opcjami`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", wybrano",other:""},a.isSelected)}`,listboxLabel:"Sugestie",selectedAnnouncement:a=>`${a.optionText}, wybrano`},"pt-BR":{buttonLabel:`Mostrar sugest\xf5es`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} op\xe7\xe3o`,other:()=>`${b.number(a.optionCount)} op\xe7\xf5es`})} dispon\xedvel.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Grupo inserido ${a.groupTitle}, com ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} op\xe7\xe3o`,other:()=>`${b.number(a.groupCount)} op\xe7\xf5es`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", selecionado",other:""},a.isSelected)}`,listboxLabel:`Sugest\xf5es`,selectedAnnouncement:a=>`${a.optionText}, selecionado`},"pt-PT":{buttonLabel:`Apresentar sugest\xf5es`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} op\xe7\xe3o`,other:()=>`${b.number(a.optionCount)} op\xe7\xf5es`})} dispon\xedvel.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Grupo introduzido ${a.groupTitle}, com ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} op\xe7\xe3o`,other:()=>`${b.number(a.groupCount)} op\xe7\xf5es`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", selecionado",other:""},a.isSelected)}`,listboxLabel:`Sugest\xf5es`,selectedAnnouncement:a=>`${a.optionText}, selecionado`},"ro-RO":{buttonLabel:`Afi\u{219}are sugestii`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} op\u{21B}iune`,other:()=>`${b.number(a.optionCount)} op\u{21B}iuni`})} disponibile.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Grup ${a.groupTitle} introdus, cu ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} op\u{21B}iune`,other:()=>`${b.number(a.groupCount)} op\u{21B}iuni`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", selectat",other:""},a.isSelected)}`,listboxLabel:"Sugestii",selectedAnnouncement:a=>`${a.optionText}, selectat`},"ru-RU":{buttonLabel:`\u{41F}\u{43E}\u{43A}\u{430}\u{437}\u{430}\u{442}\u{44C} \u{43F}\u{440}\u{435}\u{434}\u{43B}\u{43E}\u{436}\u{435}\u{43D}\u{438}\u{44F}`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} \u{43F}\u{430}\u{440}\u{430}\u{43C}\u{435}\u{442}\u{440}`,other:()=>`${b.number(a.optionCount)} \u{43F}\u{430}\u{440}\u{430}\u{43C}\u{435}\u{442}\u{440}\u{43E}\u{432}`})} \u{434}\u{43E}\u{441}\u{442}\u{443}\u{43F}\u{43D}\u{43E}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`\u{412}\u{432}\u{435}\u{434}\u{435}\u{43D}\u{43D}\u{430}\u{44F} \u{433}\u{440}\u{443}\u{43F}\u{43F}\u{430} ${a.groupTitle}, \u{441} ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} \u{43F}\u{430}\u{440}\u{430}\u{43C}\u{435}\u{442}\u{440}\u{43E}\u{43C}`,other:()=>`${b.number(a.groupCount)} \u{43F}\u{430}\u{440}\u{430}\u{43C}\u{435}\u{442}\u{440}\u{430}\u{43C}\u{438}`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, \u{432}\u{44B}\u{431}\u{440}\u{430}\u{43D}\u{43D}\u{44B}\u{43C}\u{438}`,other:""},a.isSelected)}`,listboxLabel:`\u{41F}\u{440}\u{435}\u{434}\u{43B}\u{43E}\u{436}\u{435}\u{43D}\u{438}\u{44F}`,selectedAnnouncement:a=>`${a.optionText}, \u{432}\u{44B}\u{431}\u{440}\u{430}\u{43D}\u{43E}`},"sk-SK":{buttonLabel:`Zobrazi\u{165} n\xe1vrhy`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} mo\u{17E}nos\u{165}`,other:()=>`${b.number(a.optionCount)} mo\u{17E}nosti/-\xed`})} k dispoz\xedcii.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Zadan\xe1 skupina ${a.groupTitle}, s ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} mo\u{17E}nos\u{165}ou`,other:()=>`${b.number(a.groupCount)} mo\u{17E}nos\u{165}ami`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, vybrat\xe9`,other:""},a.isSelected)}`,listboxLabel:`N\xe1vrhy`,selectedAnnouncement:a=>`${a.optionText}, vybrat\xe9`},"sl-SI":{buttonLabel:`Prika\u{17E}i predloge`,countAnnouncement:(a,b)=>`Na voljo je ${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} opcija`,other:()=>`${b.number(a.optionCount)} opcije`})}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Vnesena skupina ${a.groupTitle}, z ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} opcija`,other:()=>`${b.number(a.groupCount)} opcije`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", izbrano",other:""},a.isSelected)}`,listboxLabel:"Predlogi",selectedAnnouncement:a=>`${a.optionText}, izbrano`},"sr-SP":{buttonLabel:`Prika\u{17E}i predloge`,countAnnouncement:(a,b)=>`Dostupno jo\u{161}: ${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} opcija`,other:()=>`${b.number(a.optionCount)} opcije/a`})}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Unesena grupa ${a.groupTitle}, s ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} opcijom`,other:()=>`${b.number(a.groupCount)} optione/a`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", izabranih",other:""},a.isSelected)}`,listboxLabel:"Predlozi",selectedAnnouncement:a=>`${a.optionText}, izabrano`},"sv-SE":{buttonLabel:`Visa f\xf6rslag`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} alternativ`,other:()=>`${b.number(a.optionCount)} alternativ`})} tillg\xe4ngliga.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Ingick i gruppen ${a.groupTitle} med ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} alternativ`,other:()=>`${b.number(a.groupCount)} alternativ`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:", valda",other:""},a.isSelected)}`,listboxLabel:`F\xf6rslag`,selectedAnnouncement:a=>`${a.optionText}, valda`},"tr-TR":{buttonLabel:`\xd6nerileri g\xf6ster`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} se\xe7enek`,other:()=>`${b.number(a.optionCount)} se\xe7enekler`})} kullan\u{131}labilir.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`Girilen grup ${a.groupTitle}, ile ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} se\xe7enek`,other:()=>`${b.number(a.groupCount)} se\xe7enekler`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, se\xe7ildi`,other:""},a.isSelected)}`,listboxLabel:`\xd6neriler`,selectedAnnouncement:a=>`${a.optionText}, se\xe7ildi`},"uk-UA":{buttonLabel:`\u{41F}\u{43E}\u{43A}\u{430}\u{437}\u{430}\u{442}\u{438} \u{43F}\u{440}\u{43E}\u{43F}\u{43E}\u{437}\u{438}\u{446}\u{456}\u{457}`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} \u{43F}\u{430}\u{440}\u{430}\u{43C}\u{435}\u{442}\u{440}`,other:()=>`${b.number(a.optionCount)} \u{43F}\u{430}\u{440}\u{430}\u{43C}\u{435}\u{442}\u{440}\u{438}(-\u{456}\u{432})`})} \u{434}\u{43E}\u{441}\u{442}\u{443}\u{43F}\u{43D}\u{43E}.`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`\u{412}\u{432}\u{435}\u{434}\u{435}\u{43D}\u{430} \u{433}\u{440}\u{443}\u{43F}\u{430} ${a.groupTitle}, \u{437} ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} \u{43F}\u{430}\u{440}\u{430}\u{43C}\u{435}\u{442}\u{440}`,other:()=>`${b.number(a.groupCount)} \u{43F}\u{430}\u{440}\u{430}\u{43C}\u{435}\u{442}\u{440}\u{438}(-\u{456}\u{432})`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, \u{432}\u{438}\u{431}\u{440}\u{430}\u{43D}\u{43E}`,other:""},a.isSelected)}`,listboxLabel:`\u{41F}\u{440}\u{43E}\u{43F}\u{43E}\u{437}\u{438}\u{446}\u{456}\u{457}`,selectedAnnouncement:a=>`${a.optionText}, \u{432}\u{438}\u{431}\u{440}\u{430}\u{43D}\u{43E}`},"zh-CN":{buttonLabel:`\u{663E}\u{793A}\u{5EFA}\u{8BAE}`,countAnnouncement:(a,b)=>`\u{6709} ${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} \u{4E2A}\u{9009}\u{9879}`,other:()=>`${b.number(a.optionCount)} \u{4E2A}\u{9009}\u{9879}`})}\u{53EF}\u{7528}\u{3002}`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`\u{8FDB}\u{5165}\u{4E86} ${a.groupTitle} \u{7EC4}\u{FF0C}\u{5176}\u{4E2D}\u{6709} ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} \u{4E2A}\u{9009}\u{9879}`,other:()=>`${b.number(a.groupCount)} \u{4E2A}\u{9009}\u{9879}`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, \u{5DF2}\u{9009}\u{62E9}`,other:""},a.isSelected)}`,listboxLabel:`\u{5EFA}\u{8BAE}`,selectedAnnouncement:a=>`${a.optionText}, \u{5DF2}\u{9009}\u{62E9}`},"zh-TW":{buttonLabel:`\u{986F}\u{793A}\u{5EFA}\u{8B70}`,countAnnouncement:(a,b)=>`${b.plural(a.optionCount,{one:()=>`${b.number(a.optionCount)} \u{9078}\u{9805}`,other:()=>`${b.number(a.optionCount)} \u{9078}\u{9805}`})} \u{53EF}\u{7528}\u{3002}`,focusAnnouncement:(a,b)=>`${b.select({true:()=>`\u{8F38}\u{5165}\u{7684}\u{7FA4}\u{7D44} ${a.groupTitle}, \u{6709} ${b.plural(a.groupCount,{one:()=>`${b.number(a.groupCount)} \u{9078}\u{9805}`,other:()=>`${b.number(a.groupCount)} \u{9078}\u{9805}`})}. `,other:""},a.isGroupChange)}${a.optionText}${b.select({true:`, \u{5DF2}\u{9078}\u{53D6}`,other:""},a.isSelected)}`,listboxLabel:`\u{5EFA}\u{8B70}`,selectedAnnouncement:a=>`${a.optionText}, \u{5DF2}\u{9078}\u{53D6}`}};var au=c(38131),av=c(15545),aw=c(72238),ax=c(66775),ay=c(72406),az=c(37313),aA=c(53570),aB=c(61256),aC=c(60364),aD=c(31272),aE=c(92359),aF=c(30616),aG=c(53004),aH=c(17905),aI=c(55904),aJ=c(18206),aK=c(46235),aL=c(28767),aM=c(64297),aN=c(98360),aO=c(82325),aP=c(41789),aQ=c(77254),aR=c(48294),aS=c(88920),aT=(0,o.Rf)(function(a,b){var c;let{Component:e,isOpen:f,disableAnimation:g,selectorIcon:h=(0,d.jsx)(aP.D,{}),clearIcon:i=(0,d.jsx)(aQ.U,{}),endContent:j,getBaseProps:l,getSelectorButtonProps:p,getInputProps:q,getListBoxProps:t,getPopoverProps:u,getEmptyPopoverProps:v,getClearButtonProps:x,getListBoxWrapperProps:y,getEndContentWrapperProps:z}=function(a){var b,c,d,e,f;let g,h,i,j,k=(0,n.o)(),{validationBehavior:l}=(0,aL.CC)(aM.c)||{},[p,q]=(0,o.rE)(a,ar.variantKeys),t=null!=(c=null!=(b=a.disableAnimation)?b:null==k?void 0:k.disableAnimation)&&c,u=void 0!==a.disableClearable?!a.disableClearable:!a.isReadOnly&&a.isClearable,{ref:v,as:x,label:y,isLoading:z,menuTrigger:A="focus",filterOptions:B={sensitivity:"base"},children:C,selectorIcon:D,clearIcon:E,scrollRef:F,defaultFilter:G,endContent:H,allowsEmptyCollection:I=!0,shouldCloseOnBlur:J=!0,popoverProps:K={},inputProps:L={},scrollShadowProps:M={},listboxProps:N={},selectorButtonProps:O={},clearButtonProps:P={},showScrollIndicators:Q=!0,allowsCustomValue:R=!1,isVirtualized:S,maxListboxHeight:T=256,itemHeight:U=32,validationBehavior:V=null!=(d=null!=l?l:null==k?void 0:k.validationBehavior)?d:"native",className:W,classNames:X,errorMessage:Y,onOpenChange:Z,onClose:$,onClear:_,isReadOnly:aa=!1,...ab}=p,{contains:ac}=(g=(0,aq.Q)({usage:"search",...B}),h=(0,m.useCallback)((a,b)=>0===b.length||(a=a.normalize("NFC"),b=b.normalize("NFC"),0===g.compare(a.slice(0,b.length),b)),[g]),i=(0,m.useCallback)((a,b)=>0===b.length||(a=a.normalize("NFC"),b=b.normalize("NFC"),0===g.compare(a.slice(-b.length),b)),[g]),j=(0,m.useCallback)((a,b)=>{if(0===b.length)return!0;a=a.normalize("NFC");let c=0,d=(b=b.normalize("NFC")).length;for(;c+d<=a.length;c++){let e=a.slice(c,c+d);if(0===g.compare(b,e))return!0}return!1},[g]),(0,m.useMemo)(()=>({startsWith:h,endsWith:i,contains:j}),[h,i,j])),ad=function(a){var b,c,d,e,f,g,h;let{defaultFilter:i,menuTrigger:j="input",allowsEmptyCollection:k=!1,allowsCustomValue:l,shouldCloseOnBlur:n=!0}=a,[o,p]=(0,m.useState)(!1),[q,r]=(0,m.useState)(!1),[s,t]=(0,m.useState)(null),{collection:u,selectionManager:v,selectedKey:w,setSelectedKey:x,selectedItem:y,disabledKeys:z}=(0,al.V)({...a,onSelectionChange:b=>{a.onSelectionChange&&a.onSelectionChange(b),b===w&&(P(),M())},items:null!=(c=a.items)?c:a.defaultItems}),[A,B]=(0,an.P)(a.inputValue,ap(a.defaultInputValue,w,u)||"",a.onInputChange),[C]=(0,m.useState)(w),[D]=(0,m.useState)(A),E=(0,m.useMemo)(()=>{var b,c,d;return null==a.items&&i?(b=u,c=A,d=i,new(0,am.J)(function a(b,c,d,e){let f=[];for(let g of c)if("section"===g.type&&g.hasChildNodes){let c=a(b,(0,ak.iQ)(g,b),d,e);[...c].some(a=>"item"===a.type)&&f.push({...g,childNodes:c})}else"item"===g.type&&e(g.textValue,d)?f.push({...g}):"item"!==g.type&&f.push({...g});return f}(b,b,c,d))):u},[u,A,i,a.items]),[F,G]=(0,m.useState)(E),H=(0,m.useRef)("focus"),I=(0,ao.T)({...a,onOpenChange:b=>{a.onOpenChange&&a.onOpenChange(b,b?H.current:void 0),v.setFocused(b),b||v.setFocusedKey(null)},isOpen:void 0,defaultOpen:void 0}),J=(b=null,c)=>{let d="manual"===c||"focus"===c&&"focus"===j;(k||E.size>0||d&&u.size>0||a.items)&&(d&&!I.isOpen&&void 0===a.items&&p(!0),H.current=c,t(b),I.open())},K=(0,m.useCallback)(()=>{G(o?u:E)},[o,u,E]),L=(0,m.useCallback)((a=null)=>{I.isOpen&&K(),t(a),I.toggle()},[I,K]),M=(0,m.useCallback)(()=>{I.isOpen&&(K(),I.close())},[I,K]),[N,O]=(0,m.useState)(A),P=()=>{var a,b;let c=null!=w&&null!=(b=null==(a=u.getItem(w))?void 0:a.textValue)?b:"";O(c),B(c)},Q=(0,m.useRef)(null!=(e=null!=(d=a.selectedKey)?d:a.defaultSelectedKey)?e:null),R=(0,m.useRef)(null!=w&&null!=(f=null==(b=u.getItem(w))?void 0:b.textValue)?f:"");(0,m.useEffect)(()=>{var b,c;q&&(E.size>0||k)&&!I.isOpen&&A!==N&&"manual"!==j&&J(null,"input"),o||k||!I.isOpen||0!==E.size||M(),null!=w&&w!==Q.current&&M(),A!==N&&(v.setFocusedKey(null),p(!1),""===A&&(void 0===a.inputValue||void 0===a.selectedKey)&&x(null)),w!==Q.current&&(void 0===a.inputValue||void 0===a.selectedKey)?P():N!==A&&O(A);let d=null!=w&&null!=(c=null==(b=u.getItem(w))?void 0:b.textValue)?c:"";q||null==w||void 0!==a.inputValue||w!==Q.current||R.current===d||(O(d),B(d)),Q.current=w,R.current=d});let S=(0,aj.KZ)({...a,value:(0,m.useMemo)(()=>({inputValue:A,selectedKey:w}),[A,w])}),T=()=>{Q.current=null,x(null),M()},U=()=>{if(void 0!==a.selectedKey&&void 0!==a.inputValue){var b,c,d;null==(b=a.onSelectionChange)||b.call(a,w),O(null!=w&&null!=(d=null==(c=u.getItem(w))?void 0:c.textValue)?d:""),M()}else P(),M()},V=()=>{if(l){var a,b;A===(null!=w&&null!=(b=null==(a=u.getItem(w))?void 0:a.textValue)?b:"")?U():T()}else U()},W=(0,m.useRef)(A),X=(0,m.useMemo)(()=>I.isOpen?o?u:E:F,[I.isOpen,u,E,o,F]),Y=null!=(g=a.defaultSelectedKey)?g:C;return{...S,...I,focusStrategy:s,toggle:(b=null,c)=>{let d="manual"===c||"focus"===c&&"focus"===j;(k||E.size>0||d&&u.size>0||a.items||I.isOpen)&&(d&&!I.isOpen&&void 0===a.items&&p(!0),I.isOpen||(H.current=c),L(b))},open:J,close:V,selectionManager:v,selectedKey:w,defaultSelectedKey:Y,setSelectedKey:x,disabledKeys:z,isFocused:q,setFocused:b=>{b?(W.current=A,"focus"!==j||a.isReadOnly||J(null,"focus")):(n&&V(),A!==W.current&&S.commitValidation()),r(b)},selectedItem:y,collection:X,inputValue:A,defaultInputValue:null!=(h=ap(a.defaultInputValue,Y,u))?h:D,setInputValue:B,commit:()=>{I.isOpen&&null!=v.focusedKey?w===v.focusedKey?U():x(v.focusedKey):V()},revert:()=>{l&&null==w?T():U()}}}({...a,children:C,menuTrigger:A,validationBehavior:V,shouldCloseOnBlur:J,allowsEmptyCollection:I,defaultFilter:G&&"function"==typeof G?G:ac,onOpenChange:(a,b)=>{null==Z||Z(a,b),a||null==$||$()}});ad={...ad,...aa&&{disabledKeys:new Set([...ad.collection.getKeys()])}};let ae=(0,m.useRef)(null),af=(0,m.useRef)(null),ag=(0,m.useRef)(null),ah=(0,m.useRef)(null),ai=(0,r.zD)(v),aN=(0,r.zD)(F),{buttonProps:aO,inputProps:aP,listBoxProps:aQ,isInvalid:aR,validationDetails:aS,validationErrors:aT}=function(a,b){var c,d,e;let{buttonRef:f,popoverRef:g,inputRef:h,listBoxRef:i,keyboardDelegate:j,layoutDelegate:k,shouldFocusWrap:l,isReadOnly:n,isDisabled:o}=a,p=(0,m.useRef)(null);f=null!=f?f:p;let q=(0,aI.o)((e=at)&&e.__esModule?e.default:e,"@react-aria/combobox"),{menuTriggerProps:r,menuProps:s}=(0,aJ.V)({type:"listbox",isDisabled:o||n},b,f);aw.b.set(b,{id:s.id});let{collection:t}=b,{disabledKeys:u}=b.selectionManager,v=(0,m.useMemo)(()=>j||new(0,aG.n)({collection:t,disabledKeys:u,ref:i,layoutDelegate:k}),[j,k,t,u,i]),{collectionProps:x}=(0,aH.y)({selectionManager:b.selectionManager,keyboardDelegate:v,disallowTypeAhead:!0,disallowEmptySelection:!0,shouldFocusWrap:l,ref:h,isVirtualized:!0}),y=(0,ax.rd)(),{isInvalid:z,validationErrors:A,validationDetails:B}=b.displayValidation,{labelProps:C,inputProps:D,descriptionProps:E,errorMessageProps:F}=(0,aK.v)({...a,onChange:b.setInputValue,onKeyDown:n?a.onKeyDown:(0,ay.c)(b.isOpen&&x.onKeyDown,c=>{if(!c.nativeEvent.isComposing)switch(c.key){case"Enter":case"Tab":if(b.isOpen&&"Enter"===c.key&&c.preventDefault(),b.isOpen&&i.current&&null!=b.selectionManager.focusedKey&&b.selectionManager.isLink(b.selectionManager.focusedKey)){let a=i.current.querySelector(`[data-key="${CSS.escape(b.selectionManager.focusedKey.toString())}"]`);if("Enter"===c.key&&a instanceof HTMLAnchorElement){let d=b.collection.getItem(b.selectionManager.focusedKey);d&&y.open(a,c,d.props.href,d.props.routerOptions)}b.close()}else b.commit();break;case"Escape":(null!==b.selectedKey||""===b.inputValue||a.allowsCustomValue)&&c.continuePropagation(),b.revert();break;case"ArrowDown":b.open("first","manual");break;case"ArrowUp":b.open("last","manual");break;case"ArrowLeft":case"ArrowRight":b.selectionManager.setFocusedKey(null)}},a.onKeyDown),onBlur:c=>{var d;let e=(null==f?void 0:f.current)&&f.current===c.relatedTarget,h=null==(d=g.current)?void 0:d.contains(c.relatedTarget);e||h||(a.onBlur&&a.onBlur(c),b.setFocused(!1))},value:b.inputValue,defaultValue:b.defaultInputValue,onFocus:c=>{b.isFocused||(a.onFocus&&a.onFocus(c),b.setFocused(!0))},autoComplete:"off",validate:void 0,[aj.Lf]:b},h),G=(0,az.b)({id:r.id,"aria-label":q.format("buttonLabel"),"aria-labelledby":a["aria-labelledby"]||C.id}),H=(0,az.b)({id:s.id,"aria-label":q.format("listboxLabel"),"aria-labelledby":a["aria-labelledby"]||C.id}),I=(0,m.useRef)(0),J=null!=b.selectionManager.focusedKey&&b.isOpen?b.collection.getItem(b.selectionManager.focusedKey):void 0,K=null!=(c=null==J?void 0:J.parentKey)?c:null,L=null!=(d=b.selectionManager.focusedKey)?d:null,M=(0,m.useRef)(K),N=(0,m.useRef)(L);(0,m.useEffect)(()=>{if((0,aA.lg)()&&null!=J&&null!=L&&L!==N.current){var a;let c=b.selectionManager.isSelected(L),d=null!=K?b.collection.getItem(K):null,e=(null==d?void 0:d["aria-label"])||("string"==typeof(null==d?void 0:d.rendered)?d.rendered:"")||"",f=q.format("focusAnnouncement",{isGroupChange:null!=(a=d&&K!==M.current)&&a,groupTitle:e,groupCount:d?[...(0,ak.iQ)(d,b.collection)].length:0,optionText:J["aria-label"]||J.textValue||"",isSelected:c});(0,au.iP)(f)}M.current=K,N.current=L});let O=(0,aF.v)(b.collection),P=(0,m.useRef)(O),Q=(0,m.useRef)(b.isOpen);(0,m.useEffect)(()=>{let a=b.isOpen!==Q.current&&(null==b.selectionManager.focusedKey||(0,aA.lg)());if(b.isOpen&&(a||O!==P.current)){let a=q.format("countAnnouncement",{optionCount:O});(0,au.iP)(a)}P.current=O,Q.current=b.isOpen});let R=(0,m.useRef)(b.selectedKey);return(0,m.useEffect)(()=>{if((0,aA.lg)()&&b.isFocused&&b.selectedItem&&b.selectedKey!==R.current){let a=b.selectedItem["aria-label"]||b.selectedItem.textValue||"",c=q.format("selectedAnnouncement",{optionText:a});(0,au.iP)(c)}R.current=b.selectedKey}),(0,m.useEffect)(()=>{if(b.isOpen)return(0,av.h)([h.current,g.current].filter(a=>null!=a))},[b.isOpen,h,g]),(0,aB.w)(()=>{!J&&h.current&&(0,aC.bq)((0,aD.TW)(h.current))===h.current&&(0,aE.Ig)(h.current,null)},[J]),{labelProps:C,buttonProps:{...r,...G,excludeFromTabOrder:!0,preventFocusOnPress:!0,onPress:a=>{if("touch"===a.pointerType){var c;null==(c=h.current)||c.focus(),b.toggle(null,"manual")}},onPressStart:a=>{if("touch"!==a.pointerType){var c;null==(c=h.current)||c.focus(),b.toggle("keyboard"===a.pointerType||"virtual"===a.pointerType?"first":null,"manual")}},isDisabled:o||n},inputProps:(0,w.v)(D,{role:"combobox","aria-expanded":r["aria-expanded"],"aria-controls":b.isOpen?s.id:void 0,"aria-autocomplete":"list","aria-activedescendant":J?(0,aw.H)(b,J.key):void 0,onTouchEnd:a=>{var c,d;if(o||n)return;if(a.timeStamp-I.current<500){a.preventDefault(),null==(c=h.current)||c.focus();return}let e=a.target.getBoundingClientRect(),f=a.changedTouches[0],g=Math.ceil(e.left+.5*e.width),i=Math.ceil(e.top+.5*e.height);f.clientX===g&&f.clientY===i&&(a.preventDefault(),null==(d=h.current)||d.focus(),b.toggle(null,"manual"),I.current=a.timeStamp)},autoCorrect:"off",spellCheck:"false"}),listBoxProps:(0,w.v)(s,H,{autoFocus:b.focusStrategy||!0,shouldUseVirtualFocus:!0,shouldSelectOnPressUp:!0,shouldFocusOnHover:!0,linkBehavior:"selection"}),descriptionProps:E,errorMessageProps:F,isInvalid:z,validationErrors:A,validationDetails:B}}({validationBehavior:V,...a,inputRef:ai,buttonRef:ae,listBoxRef:ag,popoverRef:ah},ad),aU=a.isInvalid||aR,aV={inputProps:(0,s.v6)({label:y,ref:ai,wrapperRef:af,onClick:()=>{!ad.isOpen&&ad.selectedItem&&ad.open()},isClearable:!1,disableAnimation:t},L),popoverProps:(0,s.v6)({offset:5,placement:"bottom",triggerScaleOnOpen:!1,disableAnimation:t},K),scrollShadowProps:(0,s.v6)({ref:aN,isEnabled:null==(e=Q&&ad.collection.size>5)||e,hideScrollBar:!0,offset:15},M),listboxProps:(0,s.v6)({hideEmptyContent:R,emptyContent:"No results found.",disableAnimation:t},N),selectorButtonProps:(0,s.v6)({isLoading:z,size:"sm",variant:"light",radius:"full",color:aU?"danger":null==a?void 0:a.color,isIconOnly:!0,disableAnimation:t},O),clearButtonProps:(0,s.v6)({size:"sm",variant:"light",radius:"full",color:aU?"danger":null==a?void 0:a.color,isIconOnly:!0,disableAnimation:t},P)},aW=(0,s.$z)(null==X?void 0:X.base,W),aX=(null==(f=aV.listboxProps)?void 0:f.hideEmptyContent)?ad.isOpen&&!!ad.collection.size:ad.isOpen;if((0,as.U)(()=>{if(!ai.current)return;let a=ai.current.value,b=ad.collection.getItem(a);b&&ad.inputValue!==b.textValue&&(ad.setSelectedKey(a),ad.setInputValue(b.textValue))},[ai.current]),aP.onKeyDown){let a=aP.onKeyDown;aP.onKeyDown=b=>("continuePropagation"in b&&(b.stopPropagation=()=>{}),a(b))}let aY=(0,m.useMemo)(()=>ar({...q,isClearable:u,disableAnimation:t}),[(0,s.t6)(q),u,t]),aZ="native"===V&&!1===ad.displayValidation.isInvalid&&!0===ad.realtimeValidation.isInvalid;return{Component:x||"div",inputRef:ai,label:y,state:ad,slots:aY,classNames:X,isLoading:z,clearIcon:E,isOpen:aX,endContent:H,isClearable:u,disableAnimation:t,allowsCustomValue:R,selectorIcon:D,getBaseProps:()=>({"data-invalid":(0,s.sE)(aU),"data-open":(0,s.sE)(ad.isOpen),className:aY.base({class:aW})}),getInputProps:()=>({...ab,...aP,...aV.inputProps,isInvalid:aZ?void 0:aU,validationBehavior:V,errorMessage:"function"==typeof Y?Y({isInvalid:aU,validationErrors:aT,validationDetails:aS}):Y||(null==aT?void 0:aT.join(" ")),onClick:(0,s.cy)(aV.inputProps.onClick,ab.onClick)}),getListBoxProps:()=>{let a=null!=S?S:ad.collection.size>50;return{state:ad,ref:ag,isVirtualized:a,virtualization:a?{maxListboxHeight:T,itemHeight:U}:void 0,scrollShadowProps:aV.scrollShadowProps,...(0,s.v6)(aV.listboxProps,aQ,{shouldHighlightOnFocus:!0})}},getPopoverProps:(a={})=>{var b,c,d;let e=(0,s.v6)(aV.popoverProps,a);return{state:ad,ref:ah,triggerRef:af,scrollRef:ag,triggerType:"listbox",...e,classNames:{...null==(b=aV.popoverProps)?void 0:b.classNames,content:aY.popoverContent({class:(0,s.$z)(null==X?void 0:X.popoverContent,null==(d=null==(c=aV.popoverProps)?void 0:c.classNames)?void 0:d.content,a.className)})},disableDialogFocus:!0}},getEmptyPopoverProps:()=>({ref:ah,className:"hidden"}),getClearButtonProps:()=>{var a,b;return{...(0,s.v6)(aO,aV.clearButtonProps),onPressStart:()=>{var a;null==(a=ai.current)||a.focus()},onPress:a=>{var b,c;null==(c=null==(b=aV.clearButtonProps)?void 0:b.onPress)||c.call(b,a),ad.selectedItem&&ad.setSelectedKey(null),ad.setInputValue(""),ad.open(),null==_||_()},"data-visible":!!ad.selectedItem||(null==(a=ad.inputValue)?void 0:a.length)>0,className:aY.clearButton({class:(0,s.$z)(null==X?void 0:X.clearButton,null==(b=aV.clearButtonProps)?void 0:b.className)})}},getSelectorButtonProps:()=>{var a;return{ref:ae,...(0,s.v6)(aO,aV.selectorButtonProps),"data-open":(0,s.sE)(ad.isOpen),className:aY.selectorButton({class:(0,s.$z)(null==X?void 0:X.selectorButton,null==(a=aV.selectorButtonProps)?void 0:a.className)})}},getListBoxWrapperProps:(b={})=>{var c,d;return{...(0,s.v6)(aV.scrollShadowProps,b),className:aY.listboxWrapper({class:(0,s.$z)(null==X?void 0:X.listboxWrapper,null==(c=aV.scrollShadowProps)?void 0:c.className,null==b?void 0:b.className)}),style:{maxHeight:null!=(d=a.maxListboxHeight)?d:256}}},getEndContentWrapperProps:(a={})=>({className:aY.endContentWrapper({class:(0,s.$z)(null==X?void 0:X.endContentWrapper,null==a?void 0:a.className)}),onPointerDown:(0,s.cy)(a.onPointerDown,a=>{var b;0===a.button&&a.currentTarget===a.target&&(null==(b=ai.current)||b.focus())}),onMouseDown:(0,s.cy)(a.onMouseDown,a=>{0===a.button&&a.currentTarget===a.target&&a.preventDefault()})})}}({...a,ref:b}),A=t(),B=f?(0,d.jsx)(aN.j,{...u(),children:(0,d.jsx)(aO.H,{...y(),children:(0,d.jsx)(aR.K,{...A})})}):(null==(c=A.state)?void 0:c.collection.size)===0?(0,d.jsx)("div",{...v()}):null;return(0,d.jsxs)(e,{...l(),children:[(0,d.jsx)(M.r,{...q(),endContent:(0,d.jsxs)("div",{...z(),children:[j||(0,d.jsx)(k.T,{...x(),children:i}),h&&(0,d.jsx)(k.T,{...p(),children:h})]})}),g?B:(0,d.jsx)(aS.N,{children:B})]})}),aU=c(21988);let aV=({onKanwilChange:a,selectedKanwil:b})=>{let[c,f]=(0,m.useState)([]),[g,h]=(0,m.useState)(null),[i,j]=(0,m.useState)(!1),[k,l]=(0,m.useState)(b||"00"),n=(0,m.useContext)(H.A),{showToast:o}=(0,F.d)(),{token:p,axiosJWT:q,statusLogin:r}=n,s=async()=>{let a=decodeURIComponent(encodeURIComponent("SELECT kdkanwil, nmkanwil FROM dbref.t_kanwil_2025 where kdkanwil<>'00' order by kdkanwil asc;")).replace(/\n/g," ").replace(/\s+/g," ").trim(),b=(0,G.A)(a);try{j(!0);let a=await q.post("http://localhost:88/next/referensi",{query:b},{headers:{"Content-Type":"application/json",Accept:"application/json, text/plain, */*",Authorization:`Bearer ${p}`}}),c=[{kdkanwil:"00",nmkanwil:"SEMUA KANWIL"},...a.data.result||[]];f(c)}catch(b){let{data:a}=b.response||{};o(a&&a.error,"error")}finally{j(!1)}};return((0,m.useEffect)(()=>{s()},[]),(0,m.useEffect)(()=>{void 0!==b&&l(b)},[b]),i)?(0,d.jsx)(e.m,{className:"w-full sm:w-44 lg:w-52 h-10 rounded-lg"}):(0,d.jsx)(aT,{placeholder:"Pilih Kanwil",className:"w-full sm:w-44 lg:w-52 h-10","aria-label":"kanwil",size:"md",variant:"flat",color:"default",selectedKey:k,onSelectionChange:b=>(b=>{l(b),a&&a(b)})(b),classNames:{base:"rounded-lg bg-slate-50/80 dark:bg-slate-800/80",selectorButton:"rounded-lg bg-slate-50/80 dark:bg-slate-800/80 border-slate-200/60 dark:border-slate-700/60 hover:bg-slate-100/80 dark:hover:bg-slate-700/80 data-[hover=true]:bg-slate-100/80 dark:data-[hover=true]:bg-slate-700/80",listbox:"rounded-lg bg-slate-50/95 dark:bg-slate-800/95 p-2",popoverContent:"rounded-lg bg-slate-50/95 dark:bg-slate-800/95 backdrop-blur-sm border-slate-200/60 dark:border-slate-700/60 !z-50 !mt-2 shadow-lg"},allowsCustomValue:!0,defaultItems:c,children:a=>(0,d.jsx)(aU.y,{textValue:"00"===a.kdkanwil?a.nmkanwil:`${a.nmkanwil}`,children:"00"===a.kdkanwil?a.nmkanwil:`${a.nmkanwil}`},a.kdkanwil)})},aW=({onKddeptChange:a,selectedKddept:b})=>{let[c,f]=(0,m.useState)([]),[g,h]=(0,m.useState)(null),[i,j]=(0,m.useState)(!1),k=(0,m.useContext)(H.A),{showToast:l}=(0,F.d)(),{token:n,axiosJWT:o,statusLogin:p}=k,q=async()=>{let a=decodeURIComponent(encodeURIComponent("SELECT kddept, nmdept FROM dbref.t_dept_2025  order by kddept asc;")).replace(/\n/g," ").replace(/\s+/g," ").trim(),b=(0,G.A)(a);try{j(!0);let a=await o.post("http://localhost:88/next/referensi",{query:b},{headers:{"Content-Type":"application/json",Accept:"application/json, text/plain, */*",Authorization:`Bearer ${n}`}}),c=[{kddept:"000",nmdept:"SEMUA KL"},...a.data.result||[]];f(c)}catch(b){let{data:a}=b.response||{};l(a&&a.error,"error")}finally{j(!1)}};return((0,m.useEffect)(()=>{q()},[]),i)?(0,d.jsx)(e.m,{className:"w-full sm:w-44 lg:w-52 h-10 rounded-lg"}):(0,d.jsx)(aT,{placeholder:"Pilih Kementerian","aria-label":"kddept",className:"w-full sm:w-44 lg:w-52 h-10",size:"md",variant:"flat",color:"default",selectedKey:b,onSelectionChange:b=>a(b),classNames:{base:"rounded-lg bg-slate-50/80 dark:bg-slate-800/80",selectorButton:"rounded-lg bg-slate-50/80 dark:bg-slate-800/80 border-slate-200/60 dark:border-slate-700/60 hover:bg-slate-100/80 dark:hover:bg-slate-700/80 data-[hover=true]:bg-slate-100/80 dark:data-[hover=true]:bg-slate-700/80",listbox:"rounded-lg bg-slate-50/95 dark:bg-slate-800/95 p-2",popoverContent:"rounded-lg bg-slate-50/95 dark:bg-slate-800/95 backdrop-blur-sm border-slate-200/60 dark:border-slate-700/60 !z-50 !mt-2 shadow-lg"},allowsCustomValue:!0,defaultItems:c,children:a=>(0,d.jsx)(aU.y,{textValue:"000"===a.kddept?a.nmdept:`${a.nmdept} ${a.kddept}`,children:"000"===a.kddept?a.nmdept:`${a.nmdept} - (${a.kddept})`},a.kddept)})};var aX=c(44301);c(71021);let aY=({jenlap:a,onChange:b,value:c})=>{let e=[{value:"2014",label:"2014"},{value:"2015",label:"2015"},{value:"2016",label:"2016"},{value:"2017",label:"2017"},{value:"2018",label:"2018"},{value:"2019",label:"2019"},{value:"2020",label:"2020"},{value:"2021",label:"2021"},{value:"2022",label:"2022"},{value:"2023",label:"2023"},{value:"2024",label:"2024"},{value:"2025",label:"2025"}].filter(b=>!(b=>"1"===a&&b<"2019"||"6"===a&&b<"2018"||"7"===a&&b<"2024")(b.value));return(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:"w-1 h-4 bg-gradient-to-b from-blue-500 to-indigo-500 rounded-full"}),(0,d.jsx)("label",{className:"text-sm font-semibold text-slate-700 dark:text-slate-300",children:"Tahun"})]}),(0,d.jsx)(aX.d,{placeholder:"Pilih Tahun",selectedKeys:c?new Set([c]):new Set,onSelectionChange:a=>{a&&a.size>0&&b(Array.from(a)[0])},variant:"bordered",size:"sm",classNames:{trigger:"bg-white dark:bg-slate-700 border-blue-200 dark:border-blue-600 hover:border-blue-300 dark:hover:border-blue-500",value:"text-slate-700 dark:text-slate-300",label:"text-slate-600 dark:text-slate-400"},children:e.map(a=>(0,d.jsx)(aU.y,{value:a.value,className:"text-slate-700 dark:text-slate-300",children:a.label},a.value))})]})};var aZ=c(49587),a$=c.n(aZ);let a_=a$()(async()=>{},{loadableGenerated:{modules:["components\\ui\\charts\\fungsi.jsx -> ./client-chart"]},ssr:!1,loading:()=>(0,d.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,d.jsx)(e.m,{className:"w-full h-64"})})}),a0=({selectedKanwil:a,selectedKddept:b})=>{let[c,g]=(0,m.useState)([]),[j,k]=(0,m.useState)(!1),{theme:n}=(0,l.D)(),{showToast:o}=(0,F.d)(),{token:p,axiosJWT:q}=(0,m.useContext)(H.A),r=a=>new Intl.NumberFormat("id-ID",{minimumFractionDigits:2,maximumFractionDigits:2}).format(a/1e12)+" T",s=async()=>{let c=a&&"00"!==a?` and a.kdkanwil='${a}'`:"",d=b&&"000"!==b?` and a.kddept='${b}'`:"",e=decodeURIComponent(encodeURIComponent(`
      SELECT a.thang, a.kddept, c.nmdept, a.kdfungsi, b.nmfungsi, a.kdkanwil, k.nmkanwil,
             SUM(a.pagu) AS pagu, SUM(a.realisasi) AS realisasi
      FROM dashboard.dipa_per_fungsi a
      LEFT JOIN dbref.t_fungsi_2025 b ON a.kdfungsi = b.kdfungsi
      LEFT JOIN dbref.t_kanwil_2025 k ON a.kdkanwil = k.kdkanwil
      LEFT JOIN dbref.t_dept_2025 c ON a.kddept = c.kddept
      WHERE a.thang='2025' ${c} ${d}
      GROUP BY a.kdfungsi
      ORDER BY a.thang, a.kdfungsi;
    `)).replace(/\n/g," ").replace(/\s+/g," ").trim(),f=(0,G.A)(e);try{k(!0);let a=await q.post("http://localhost:88/next/referensi",{query:f});g(a.data.result||[])}catch(a){o("Terjadi Permasalahan Koneksi atau Server Backend","error")}finally{k(!1)}};if((0,m.useEffect)(()=>{s()},[a,b]),j)return(0,d.jsx)(f.Z,{className:"border-none shadow-sm bg-gradient-to-br from-default-50 to-default-100",children:(0,d.jsx)(h.U,{className:"p-6 space-y-4",children:Array.from({length:5}).map((a,b)=>(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)(e.m,{className:"h-4 w-32 rounded"}),(0,d.jsx)(e.m,{className:"h-6 w-16 rounded-full"})]},b))})});if(0===c.length||Object.values(c[0]).slice(2).every(a=>0===a||null===a))return(0,d.jsx)("div",{className:"w-full h-full",children:(0,d.jsx)(f.Z,{className:`border-none shadow-sm ${"dark"===n?"bg-gradient-to-br from-slate-800/90 to-slate-700/90":"bg-gradient-to-br from-white/90 to-slate-50/90"} h-full`,children:(0,d.jsxs)(h.U,{className:"pt-0 px-4 md:px-6 flex flex-col items-center justify-center text-center py-8",children:[(0,d.jsx)(ag,{className:"w-12 h-12 text-default-400 mb-4"}),(0,d.jsx)(i.R,{size:"sm",variant:"flat",color:"warning",startContent:(0,d.jsx)(ah.A,{className:"w-3 h-3"}),className:"text-xs",children:"Data Tidak Tersedia"})]})})});let t=new Map;c.forEach(a=>{let b=a.kdfungsi,c=t.get(b);c?(c.pagu+=a.pagu||0,c.realisasi+=a.realisasi||0):t.set(b,{nmfungsi:a.nmfungsi||`Fungsi ${b}`,pagu:a.pagu||0,realisasi:a.realisasi||0})});let u=Array.from(t.entries()).sort(([a],[b])=>a.localeCompare(b)),v=u.map(([a])=>a),w=u.map(([a,b])=>b.nmfungsi),x=u.map(([a,b])=>b.pagu),y=u.map(([a,b])=>b.realisasi),z=(()=>{let a="dark"===n;return{foreColor:a?"#a1a1aa":"#52525b",borderColor:a?"#3f3f46":"#e4e4e7",gridColor:a?"#27272a":"#f4f4f5",axisColor:a?"#52525b":"#71717a",primary:"#3B82F6",success:"#10B981",textPrimary:a?"#f3f4f6":"#374151"}})(),A={chart:{type:"area",animations:{speed:300},stacked:!0,toolbar:{show:!1},parentHeightOffset:0,foreColor:z.foreColor},dataLabels:{enabled:!0,formatter:a=>r(a)},legend:{position:"top",horizontalAlign:"center",fontSize:"12px",fontWeight:500,labels:{colors:z.textPrimary},markers:{size:8},itemMargin:{horizontal:10,vertical:5}},xaxis:{categories:v,labels:{style:{colors:z.foreColor}},axisBorder:{color:z.borderColor},axisTicks:{color:z.borderColor}},yaxis:{labels:{style:{colors:z.foreColor},formatter:a=>r(a)}},tooltip:{theme:"dark"===n?"dark":"light",style:{fontSize:"12px"},x:{formatter:(a,{dataPointIndex:b})=>w[b]},y:{formatter:a=>r(a)}},colors:[z.primary,z.success],grid:{show:!0,borderColor:z.gridColor,strokeDashArray:0,position:"back"},stroke:{curve:"smooth"},markers:{show:!1}};return(0,d.jsx)("div",{className:"w-full h-full relative",children:(0,d.jsx)(a_,{options:A,series:[{name:"Pagu",data:x},{name:"Realisasi",data:y}],type:"area",height:"100%",width:"100%"},n)})};var a1=c(53411);let a2=(0,Y.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var a3=c(14719),a4=c(43649),a5=c(85814),a6=c.n(a5);let a7=a$()(async()=>{},{loadableGenerated:{modules:["app\\(dashboard)\\dashboard\\loading.jsx -> @/components/ui/charts/dukmanTeknis"]},ssr:!1,loading:()=>(0,d.jsx)(e.m,{className:"w-full h-64"})}),a8=[{title:"Pencairan Anggaran Pendidikan",ministry:"Kemendikbud",amount:"Rp 125.4 M",time:"2 jam lalu",status:"completed"},{title:"Revisi DIPA Kesehatan",ministry:"Kemenkes",amount:"Rp 89.2 M",time:"4 jam lalu",status:"pending"},{title:"Penyesuaian Alokasi PUPR",ministry:"Kemen PUPR",amount:"Rp 67.8 M",time:"6 jam lalu",status:"in-review"}];function a9(){let{theme:a}=(0,l.D)(),[b,c]=(0,m.useState)("00"),[n,o]=(0,m.useState)("000"),p="dark"===a?"bg-gradient-to-br from-slate-800 to-slate-700":"bg-gradient-to-br from-slate-100 to-slate-200",q=a$()(async()=>{},{loadableGenerated:{modules:["app\\(dashboard)\\dashboard\\loading.jsx -> @/components/ui/charts/trenApbn"]},ssr:!1,loading:()=>(0,d.jsx)(e.m,{className:"w-full h-64"})});return(0,d.jsxs)("div",{className:"h-full lg:px-6 pt-4",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-2 pt-2 px-4 lg:px-0 max-w-[90rem] mx-auto w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4",children:[(0,d.jsxs)("div",{children:[" ",(0,d.jsx)("h1",{className:"text-xl md:text-2xl font-medium text-foreground tracking-wide",children:"Dashboard Realisasi APBN"})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-3",children:[(0,d.jsx)(aY,{}),(0,d.jsx)(aW,{onKddeptChange:a=>{o(a)},selectedKddept:n}),(0,d.jsx)(aV,{onKanwilChange:a=>{c(a)},selectedKanwil:b})]})]}),(0,d.jsx)(ae,{selectedKanwil:b,selectedKddept:n})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-6 pt-6 px-4 lg:px-0 max-w-[90rem] mx-auto w-full",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-6 xl:grid-cols-12 gap-6",children:[(0,d.jsx)(X,{selectedKanwil:b,selectedKddept:n}),(0,d.jsx)(ai,{selectedKanwil:b,selectedKddept:n}),(0,d.jsxs)(f.Z,{className:`border-none shadow-sm ${p} sm:col-span-2 lg:col-span-12 xl:col-span-6`,children:[(0,d.jsx)(g.d,{className:"pb-1 px-4 md:px-6",children:(0,d.jsx)("div",{className:"flex flex-col gap-1",children:(0,d.jsx)("h3",{className:"text-sm md:text-base font-semibold",children:"Tren Realisasi APBN"})})}),(0,d.jsx)(h.U,{className:"pt-0 px-2 md:px-4 pb-1",children:(0,d.jsx)("div",{className:"h-[150px] md:h-[200px] w-full flex flex-col overflow-hidden",children:(0,d.jsx)(q,{selectedKanwil:b,selectedKddept:n})})})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-3",children:[(0,d.jsxs)(f.Z,{className:`border-none shadow-sm ${p} lg:col-span-2`,children:[(0,d.jsx)(g.d,{className:"pb-2 px-4 md:px-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(a1.A,{className:"h-3 w-3 text-default-500"}),(0,d.jsx)("h3",{className:"text-sm md:text-base font-semibold",children:"Realisasi Fungsi"})]}),(0,d.jsx)(i.R,{color:"primary",variant:"flat",size:"sm",className:"w-fit",children:"Per Sektor"})]})}),(0,d.jsx)(h.U,{className:"pt-0 px-2 md:px-4 pb-1",children:(0,d.jsx)("div",{className:"h-[200px] md:h-[250px] w-full flex flex-col overflow-hidden",children:(0,d.jsx)(a0,{selectedKanwil:b,selectedKddept:n})})})]}),(0,d.jsxs)(f.Z,{className:`border-none shadow-sm ${p}`,children:[(0,d.jsx)(g.d,{className:"pb-2 px-4 md:px-6",children:(0,d.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(a1.A,{className:"h-3 w-3 text-default-500"}),(0,d.jsx)("h3",{className:"text-sm md:text-base font-semibold",children:"Pagu Dukman vs Teknis"})]}),(0,d.jsx)(i.R,{color:"primary",variant:"flat",size:"sm",className:"w-fit",children:"2024"})]})}),(0,d.jsx)(h.U,{className:"pt-0 px-2 md:px-4 pb-1",children:(0,d.jsx)("div",{className:"h-[200px] md:h-[250px] w-full flex flex-col overflow-hidden",children:(0,d.jsx)(a7,{selectedKanwil:b,selectedKddept:n})})})]}),(0,d.jsxs)(f.Z,{className:`border-none shadow-sm ${p}`,children:[(0,d.jsx)(g.d,{className:"pb-2 px-4 md:px-6",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(a2,{className:"h-3 w-3 text-default-500"}),(0,d.jsx)("h3",{className:"text-sm md:text-base font-semibold",children:"Aktivitas Terkini"})]})}),(0,d.jsxs)(h.U,{className:"pt-0 px-4 md:px-6",children:[(0,d.jsx)("div",{className:"space-y-2",children:a8.map((a,b)=>(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:"completed"===a.status?(0,d.jsx)(a3.A,{className:"h-3 w-3 text-success"}):"pending"===a.status?(0,d.jsx)(a2,{className:"h-3 w-3 text-warning"}):(0,d.jsx)(a4.A,{className:"h-3 w-3 text-primary"})}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("p",{className:"text-xs font-medium truncate",children:a.title}),(0,d.jsx)("p",{className:"text-xs text-default-500",children:a.ministry}),(0,d.jsxs)("div",{className:"flex justify-between items-center mt-0.5",children:[(0,d.jsx)("span",{className:"text-xs font-medium text-primary",children:a.amount}),(0,d.jsx)("span",{className:"text-xs text-default-400",children:a.time})]})]})]},b))}),(0,d.jsx)(j.y,{className:"my-2"}),(0,d.jsx)(k.T,{variant:"flat",color:"primary",size:"sm",className:"w-full",as:a6(),href:"/mbg/kertas-kerja",children:"Lihat Semua Aktivitas"})]})]})]})]})]})}},77317:(a,b,c)=>{"use strict";c.d(b,{m:()=>k});var d=c(62948),e=(0,c(98462).tv)({slots:{base:["group","relative","overflow-hidden","bg-content3 dark:bg-content2","pointer-events-none","before:opacity-100","before:absolute","before:inset-0","before:-translate-x-full","before:animate-shimmer","before:border-t","before:border-content4/30","before:bg-gradient-to-r","before:from-transparent","before:via-content4","dark:before:via-default-700/10","before:to-transparent","after:opacity-100","after:absolute","after:inset-0","after:-z-10","after:bg-content3","dark:after:bg-content2","data-[loaded=true]:pointer-events-auto","data-[loaded=true]:overflow-visible","data-[loaded=true]:!bg-transparent","data-[loaded=true]:before:opacity-0 data-[loaded=true]:before:-z-10 data-[loaded=true]:before:animate-none","data-[loaded=true]:after:opacity-0"],content:["opacity-0","group-data-[loaded=true]:opacity-100"]},variants:{disableAnimation:{true:{base:"before:animate-none before:transition-none after:transition-none",content:"transition-none"},false:{base:"transition-background !duration-300",content:"transition-opacity motion-reduce:transition-none !duration-300"}}},defaultVariants:{}}),f=c(79910),g=c(43210),h=c(58445),i=c(60687),j=(0,d.Rf)((a,b)=>{let{Component:c,children:j,getSkeletonProps:k,getContentProps:l}=function(a){var b,c;let i=(0,h.o)(),[j,k]=(0,d.rE)(a,e.variantKeys),{as:l,children:m,isLoaded:n=!1,className:o,classNames:p,...q}=j,r=null!=(c=null!=(b=a.disableAnimation)?b:null==i?void 0:i.disableAnimation)&&c,s=(0,g.useMemo)(()=>e({...k,disableAnimation:r}),[(0,f.t6)(k),r,m]),t=(0,f.$z)(null==p?void 0:p.base,o);return{Component:l||"div",children:m,slots:s,classNames:p,getSkeletonProps:(a={})=>({"data-loaded":(0,f.sE)(n),className:s.base({class:(0,f.$z)(t,null==a?void 0:a.className)}),...q}),getContentProps:(a={})=>({className:s.content({class:(0,f.$z)(null==p?void 0:p.content,null==a?void 0:a.className)})})}}({...a});return(0,i.jsx)(c,{ref:b,...k(),children:(0,i.jsx)("div",{...l(),children:j})})});j.displayName="HeroUI.Skeleton";var k=j},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},80352:(a,b,c)=>{Promise.resolve().then(c.bind(c,75627))},81630:a=>{"use strict";a.exports=require("http")},83004:(a,b,c)=>{"use strict";c.d(b,{a:()=>e});var d=c(43210);function e(a={}){let{rerender:b=!1,delay:c=0}=a,f=(0,d.useRef)(!1),[g,h]=(0,d.useState)(!1);return(0,d.useEffect)(()=>{f.current=!0;let a=null;return b&&(c>0?a=setTimeout(()=>{h(!0)},c):h(!0)),()=>{f.current=!1,b&&h(!1),a&&clearTimeout(a)}},[b]),[(0,d.useCallback)(()=>f.current,[]),g]}},83997:a=>{"use strict";a.exports=require("tty")},85858:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,69037)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\dashboard\\page.jsx"]}]},{loading:[()=>Promise.resolve().then(c.bind(c,2254)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\dashboard\\loading.jsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,8413)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\layout.jsx"],loading:[()=>Promise.resolve().then(c.bind(c,48221)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\loading.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,53361)),"D:\\sintesaNEXT2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\sintesaNEXT2\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["D:\\sintesaNEXT2\\src\\app\\(dashboard)\\dashboard\\page.jsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/dashboard/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,3598,6159,9697,901],()=>b(b.s=85858));module.exports=c})();