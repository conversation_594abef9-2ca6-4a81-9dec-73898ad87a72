#!/usr/bin/env node

import http from 'http';
import process from 'process';

const options = {
  host: 'localhost',
  port: process.env.PORT || 3001,
  path: '/api/health',
  method: 'GET',
  timeout: 3000
};

const request = http.request(options, (response) => {
  let data = '';
  
  response.on('data', (chunk) => {
    data += chunk;
  });
  
  response.on('end', () => {
    try {
      const result = JSON.parse(data);
      
      if (response.statusCode === 200 && result.status === 'ok') {
        console.log('Health check passed');
        process.exit(0);
      } else {
        console.error('Health check failed:', result);
        process.exit(1);
      }
    } catch (error) {
      console.error('Health check failed - Invalid JSON response:', error.message);
      process.exit(1);
    }
  });
});

request.on('error', (error) => {
  console.error('Health check failed - Request error:', error.message);
  process.exit(1);
});

request.on('timeout', () => {
  console.error('Health check failed - Request timeout');
  request.destroy();
  process.exit(1);
});

request.setTimeout(3000);
request.end();