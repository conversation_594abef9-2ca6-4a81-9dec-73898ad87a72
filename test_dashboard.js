import jwt from "jsonwebtoken";
import CryptoJS from "crypto-js";

// JWT configuration (matching the backend)
const JWT_SECRET = "sintesa-super-secret-jwt-key-2024-change-in-production";
const ENCRYPTION_SECRET = "mebe23";

// Create a test JWT token
const payload = {
  id: 1,
  username: "testuser",
  role: "admin",
  kdkanwil: "01",
  kdkppn: "001",
  kdlokasi: "001",
  active: 1,
};

const accessToken = jwt.sign(payload, JWT_SECRET, {
  expiresIn: "8h",
  issuer: "sintesa-backend",
  audience: "sintesa-frontend",
});

// Encrypt the token (matching frontend encryption)
function encryptString(data, key = ENCRYPTION_SECRET) {
  const encJson = CryptoJS.AES.encrypt(data, key).toString();
  const encData = CryptoJS.enc.Base64.stringify(
    CryptoJS.enc.Utf8.parse(encJson)
  );
  return encData;
}

const encryptedToken = encryptString(accessToken);

// Test dashboard query (not encrypted, used directly)
const dashboardQuery = "SELECT 1 as test_value, 'Dashboard Working' as message";

async function testDashboard() {
  try {
    console.log("🧪 Testing Dashboard Endpoint...");
    console.log(
      "Token (first 50 chars):",
      encryptedToken.substring(0, 50) + "..."
    );
    console.log("Query:", dashboardQuery);

    const response = await fetch("http://localhost:88/next/dashboard", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${encryptedToken}`,
      },
      body: JSON.stringify({
        query: dashboardQuery,
      }),
    });

    console.log("\nResponse Headers:");
    for (const [key, value] of response.headers.entries()) {
      console.log(`  '${key}': '${value}',`);
    }

    const responseText = await response.text();
    console.log(`\nResponse: ${responseText}`);

    if (response.ok) {
      console.log("\n✅ SUCCESS: Dashboard endpoint working!");
    } else {
      console.log(`\n❌ FAILED: Status code ${response.status}`);
    }
  } catch (error) {
    console.error("❌ Test failed:", error.message);
  }
}

testDashboard();
