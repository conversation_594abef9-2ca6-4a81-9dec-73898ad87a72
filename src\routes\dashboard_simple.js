// Dashboard routes - Simplified for backward compatibility
export default async function dashboardRoutes(fastify, options) {
  // Backward compatibility route for old Express format (dashboard queries)
  fastify.post(
    "/",
    {
      schema: {
        body: {
          type: "object",
          required: ["query"],
          properties: {
            query: {
              type: "string",
              description: "SQL query for dashboard data",
            },
          },
        },
        response: {
          200: {
            type: "object",
            properties: {
              result: { type: "array" },
            },
          },
        },
      },
      preHandler: [fastify.verifyToken],
    },
    async (request, reply) => {
      try {
        const { query } = request.body;

        if (!query) {
          return reply.code(400).send({
            success: false,
            message: "Query parameter is required",
          });
        }

        fastify.log.info(
          "Executing legacy dashboard query:",
          query.substring(0, 100) + "..."
        );

        // Execute the SQL query directly (matching old Express behavior)
        // Note: The old dashboard controller didn't decrypt the query, it used it directly
        const queryResult = await fastify.db.rawQuery(query);
        const results = queryResult.rows;

        // Return in the same format as old Express backend
        return reply.send({
          result: results,
        });
      } catch (error) {
        fastify.log.error("Legacy dashboard query error:", error);

        // Match old Express error handling
        const errorMessage =
          error.sqlMessage ||
          error.message ||
          "Terjadi kesalahan dalam memproses permintaan.";

        return reply.code(500).send({
          error: errorMessage,
        });
      }
    }
  );
}
