exports.id=9697,exports.ids=[9697],exports.modules={7425:()=>{},14221:(a,b,c)=>{"use strict";c.d(b,{j:()=>n,A:()=>o});var d=c(60687),e=c(77567);c(7425);var f=c(31514),g=c(49867),h=c(51060),i=c(53117),j=c(16189),k=c(43210),l=c(57405);let m=(0,k.createContext)({role:"",telp:"",verified:"",name:"",statusLogin:!1,token:"",axiosJWT:null,isInitializing:!0}),n=({children:a})=>{let b=(0,j.useRouter)(),[c,n]=(0,k.useState)([]),[o,p]=(0,k.useState)(null),[q,r]=(0,k.useState)(null),[s,t]=(0,k.useState)(""),[u,v]=(0,k.useState)(""),[w,x]=(0,k.useState)(""),[y,z]=(0,k.useState)(""),[A,B]=(0,k.useState)(""),[C,D]=(0,k.useState)(""),[E,F]=(0,k.useState)(""),[G,H]=(0,k.useState)(""),[I,J]=(0,k.useState)(""),[K,L]=(0,k.useState)(""),[M,N]=(0,k.useState)(""),[O,P]=(0,k.useState)(""),[Q,R]=(0,k.useState)(""),[S,T]=(0,k.useState)(""),[U,V]=(0,k.useState)(!1),[W,X]=(0,k.useState)(""),[Y,Z]=(0,k.useState)(""),[$,_]=(0,k.useState)(""),[aa,ab]=(0,k.useState)(""),[ac,ad]=(0,k.useState)(""),[ae,af]=(0,k.useState)([]),[ag,ah]=(0,k.useState)(!1),[ai,aj]=(0,k.useState)(""),[ak,al]=(0,k.useState)(""),[am,an]=(0,k.useState)(""),[ao,ap]=(0,k.useState)(!1),[aq,ar]=(0,k.useState)(0),[as,at]=(0,k.useState)([]),[au,av]=(0,k.useState)({}),[aw,ax]=(0,k.useState)(!1),[ay,az]=(0,k.useState)(""),[aA,aB]=(0,k.useState)(""),[aC,aD]=(0,k.useState)(!1),[aE,aF]=(0,k.useState)(null),[aG,aH]=(0,k.useState)({}),[aI,aJ]=(0,k.useState)("sppg"),[aK,aL]=(0,k.useState)(!0);(0,k.useEffect)(()=>{},[]),(0,k.useEffect)(()=>{},[U,aK]);let aM=async()=>{try{ah(!0),X("");try{await h.A.delete("http://localhost:88/logout")}catch(a){console.log("Backend logout failed (might be expected):",a.message)}V(!1),ax(!1),n([]),_(!1),ab(!1),r(null),aB(""),aF(null),await deleteAuthCookie(),"true"===ai&&e.A.fire({position:"top",icon:"success",title:"Logout Berhasil",timer:2e3,showConfirmButton:!1})}catch(a){console.error("❌ Logout process failed:",a),e.A.fire({position:"top",icon:"error",title:"Logout Gagal",timer:2e3,showConfirmButton:!1})}finally{ah(!1),b.push("/login")}},aN=h.A.create({withCredentials:!0});aN.interceptors.request.use(async a=>{let b=localStorage.getItem("token");if(!b)return aM(),Promise.reject(Error("No token available"));try{let c=(0,i.s)((0,f.a)(b)),d=Date.now()/1e3;if(c.exp<d)return await deleteAuthCookie(),aM(),Promise.reject(Error("Token expired"));a.headers.Authorization=`Bearer ${b}`}catch(a){return console.error("Invalid token in interceptor:",a),await deleteAuthCookie(),aM(),Promise.reject(Error("Invalid token"))}return a},a=>Promise.reject(a)),(0,k.useEffect)(()=>{U&&"1"===u&&""!==q&&aO()},[q]),(0,k.useEffect)(()=>{"false"===ai&&e.A.fire({html:"<div className='text-danger mt-4'>System melakukan logout otomatis terhadap akun anda.</div>",icon:"error",position:"top",buttonsStyling:!1,customClass:{popup:"swal2-animation",container:"swal2-animation",confirmButton:"swal2-confirm ",icon:"swal2-icon"},confirmButtonText:"Tutup"}),"false"===ai&&aM()},[ai]);let aO=async()=>{try{let a=await h.A.get(`http://localhost:88/next/auth/profile?username=${W}`);aj(a.data)}catch(a){(0,g.t)("Terjadi Permasalahan Koneksi atau Server Backend "),V(!1),_(!1),ab(!1)}};return(0,k.useEffect)(()=>{if(U){let a=(0,l.io)("http://localhost:88");return a.on("nmuser",a=>{if(a){let b=a.toUpperCase();n(a=>[...a,b]),r(b)}else r(null)}),a.on("loginBy",a=>{a&&aF(a)}),()=>{a.off("userLoggedin"),a.off("nmuser"),a.off("loginBy")}}},[U,aE]),(0,k.useEffect)(()=>{if(U){let a=(0,l.io)("http://localhost:88",{transports:["websocket","polling"],timeout:1e4,reconnection:!0,reconnectionDelay:1e3,reconnectionAttempts:5});return a.on("running_querys",a=>{if(a){let b=a.toLowerCase();al(a=>[...a,b])}else al([])}),a.on("error_querys",a=>{if(a){let b=a.toLowerCase();an(a=>[...a,b])}else an([])}),()=>{a.off("running_querys"),a.off("error_querys")}}},[U]),(0,d.jsx)(m.Provider,{value:{processquery:ak,errorprocessquery:am,setLoggedinUsers:n,setNamelogin:r,namelogin:q,setLoggedInUser2:p,loggedInUser2:o,logout:aM,loggedinUsers:c,setSession:v,setExpire:N,setToken:P,setMode:Z,setTampil:_,setTampilverify:ab,setStatus:ad,url:S,setUrl:T,statusLogin:U,name:s,setName:t,setstatusLogin:V,role:w,kdkanwil:G,kdkppn:K,kdlokasi:C,setRole:x,setKdkanwil:H,setKdkppn:L,setKdlokasi:D,setActive:B,expire:M,token:O,axiosJWT:aN,active:A,deptlimit:I,setDeptlimit:J,setNmrole:z,nmrole:y,setIduser:R,iduser:Q,setUsername:X,username:W,mode:Y,status:ac,tampil:$,tampilverify:aa,persentase:ae,setPersentase:af,loadingExcell:ao,setloadingExcell:ap,setVerified:F,verified:E,totNotif:aq,settotNotif:ar,listNotif:as,setlistNotif:at,visibilityStatuses:au,setVisibilityStatuses:av,offline:aw,setOffline:ax,offlinest:ay,setOfflinest:az,telp:aA,setTelp:aB,tampilAI:aC,settampilAI:aD,loginDengan:aE,setloginDengan:aF,dataEpa:aG,setDataEpa:aH,viewMode:aI,setViewMode:aJ,isInitializing:aK},children:a})},o=m},18094:(a,b,c)=>{Promise.resolve().then(c.bind(c,29519)),Promise.resolve().then(c.bind(c,66799)),Promise.resolve().then(c.bind(c,78571))},21741:(a,b,c)=>{"use strict";c.d(b,{Providers:()=>j});var d=c(60687),e=c(78995),f=c(10218),g=c(84819),h=c(40611),i=c(14221);function j({children:a,themeProps:b}){return(0,d.jsx)(i.j,{children:(0,d.jsx)(e.M,{children:(0,d.jsx)(f.N,{defaultTheme:"system",attribute:"class",enableSystem:!0,enableColorScheme:!0,storageKey:"sintesa-theme",...b,children:(0,d.jsx)(g.p,{children:(0,d.jsx)(h.t,{children:a})})})})})}},24793:(a,b,c)=>{"use strict";c.d(b,{H:()=>k});var d=c(14221),e=c(4765),f=c.n(e);let g="mebe23",h=(a,b=g)=>{let c=f().AES.encrypt(JSON.stringify(a),b).toString();return f().enc.Base64.stringify(f().enc.Utf8.parse(c))};var i=c(43210);let j=a=>({id:a.kdsatker,kode:a.kdsatker,nama:a.nmsatker,kdsatker:a.kdsatker,nmsatker:a.nmsatker,email:a.email,kddept:a.kddept,nmdept:a.nmdept,kdunit:a.kdunit,nmunit:a.nmunit,kddekon:a.kddekon,nmdekon:a.nmdekon,kdkanwil:a.kdkanwil,nmkanwil:a.nmkanwil,kdkppn:a.kdkppn,nmkppn:a.nmkppn,thang:a.thang,kpa:a.kpa,bendahara:a.bendahara,ppspm:a.ppspm,npwp:a.npwp,statusblu:a.statusblu,kdjendok:a.kdjendok,level:a.kddept,alamat:`${a.kdunit}`}),k=()=>{let{token:a,axiosJWT:b}=(0,i.useContext)(d.A)||{};return{cariSatker:async a=>{let c=`
        select kdsatker,nmsatker,kddept,kdunit from dbref.t_satker_2025 
        where kdsatker like '%${a}%' or nmsatker like '%${a}%'
        order by kdsatker limit 10;
      `.replace(/\n/g," ").replace(/\s+/g," ").trim();try{let a=await b.post("http://localhost:88/next/referensi",{query:h(c)});if(200!==a.status)throw Error(`HTTP error! status: ${a.status}`);return a.data.result.map(a=>j(a))}catch(a){throw console.error("Error dalam cariSatker:",a),Error("Gagal mencari data satker")}},ambilDetailSatker:async a=>{let c=`
    SELECT a.thang,a.kdjendok,a.kdsatker,c.kdkppn,b.nmsatker,a.kddept,d.nmdept,g.kdkanwil,a.kdunit,e.nmunit,
    a.kddekon,a.kdlokasi,c.nmkppn,a.kpa,a.bendahara,a.ppspm,f.nmdekon,g.nmkanwil,a.npwp,a.statusblu,
    a.email FROM dbdipa25.d_kpa a LEFT JOIN dbref.t_satker_2025 b ON a.kdsatker=b.kdsatker AND a.kddept=b.kddept 
    AND a.kdunit=b.kdunit LEFT JOIN dbref.t_kppn_2023 c ON b.kdkppn=c.kdkppn LEFT JOIN dbref.t_dept_2025 d 
    ON b.kddept=d.kddept LEFT JOIN dbref.t_unit_2025 e ON a.kddept=e.kddept AND b.kdunit=e.kdunit 
    LEFT JOIN dbref.t_dekon_2025 f ON a.kddekon=f.kddekon LEFT JOIN dbref.t_kanwil_2014 g 
    ON c.kdkanwil=g.kdkanwil WHERE a.kdsatker='${a}' GROUP BY a.thang,a.kdjendok,a.kdsatker,c.kdkppn,b.nmsatker,a.kddept,d.nmdept,g.kdkanwil,a.kdunit,e.nmunit,a.kddekon,a.kdlokasi,c.nmkppn,a.kpa,a.bendahara,a.ppspm,f.nmdekon,g.nmkanwil,a.npwp,a.statusblu,a.email ORDER BY a.kdsatker limit 1;
      
      `.replace(/\n/g," ").replace(/\s+/g," ").trim();try{let a=await b.post("http://localhost:88/next/referensi",{query:h(c)});if(200!==a.status)throw Error(`HTTP error! status: ${a.status}`);if(0===a.data.result.length)return null;return j(a.data.result[0])}catch(a){throw Error("Gagal mengambil detail satker")}}}}},29519:(a,b,c)=>{"use strict";c.d(b,{Providers:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\app\\providers.tsx","Providers")},31514:(a,b,c)=>{"use strict";c.d(b,{a:()=>f});var d=c(4765),e=c.n(d);let f=a=>{let b=e().enc.Base64.parse(a).toString(e().enc.Utf8);return e().AES.decrypt(b,"mebe23").toString(e().enc.Utf8)}},35692:()=>{},39727:()=>{},40611:(a,b,c)=>{"use strict";c.d(b,{d:()=>g,t:()=>h});var d=c(60687),e=c(43210);let f=(0,e.createContext)(void 0),g=()=>{let a=(0,e.useContext)(f);if(void 0===a)throw Error("useToast must be used within a ToastProvider");return a},h=({children:a})=>{let[b,c]=(0,e.useState)([]),g=(0,e.useCallback)(a=>{c(b=>b.filter(b=>b.id!==a))},[]),h=(0,e.useCallback)((a,b,d=5e3)=>{let e=Math.random().toString(36).substr(2,9),f={id:e,message:a,type:b,duration:d};c(a=>[...a,f]),d>0&&setTimeout(()=>{g(e)},d)},[g]);return(0,d.jsx)(f.Provider,{value:{toasts:b,showToast:h,removeToast:g},children:a})}},47990:()=>{},49867:(a,b,c)=>{"use strict";c.d(b,{t:()=>f});var d=c(77567);let e=(a,b,c="error")=>{d.A.fire({title:a,text:b,icon:c,position:"top-end",toast:!0,showConfirmButton:!1,timer:5e3,showCloseButton:!0,background:"red",color:"white",timerProgressBar:!0})},f=(a,b)=>{switch(a){case 400:e(`Kesalahan Permintaan, Permintaan tidak valid. (${b})`);break;case 401:e(`Tidak Diotorisasi, Anda tidak memiliki izin untuk akses. (${b})`);break;case 403:e(`Akses Ditolak, Akses ke sumber daya dilarang. (${b})`);break;case 404:e(`Error Refresh Token. Silahkan Login Ulang... (${b})`);break;case 429:e(`Terlalu Banyak Permintaan, Anda telah melebihi batas permintaan. (${b})`);break;case 422:e(`Unprocessable Entity, Permintaan tidak dapat diolah. (${b})`);break;case 500:e("Kesalahan Pada Query",b);break;case 503:e(`Layanan Tidak Tersedia, Layanan tidak tersedia saat ini. (${b})`);break;case 504:e(`Waktu Habis, Permintaan waktu habis. (${b})`);break;case 505:e(`Versi HTTP Tidak Didukung, Versi HTTP tidak didukung. (${b})`);break;case 507:e(`Penyimpanan Tidak Cukup, Penyimpanan tidak mencukupi. (${b})`);break;case 511:e(`Autentikasi Diperlukan, Autentikasi diperlukan. (${b})`);break;default:e(`Kesalahan Server, ${b} `)}}},51714:(a,b,c)=>{"use strict";c.d(b,{default:()=>j});var d=c(43210),e=c(16189);c(57405);let f=null,g=new Set,h=0;var i=c(14221);let j=({children:a})=>{let b=(0,d.useContext)(i.A),c=(0,e.useRouter)(),j=(0,e.usePathname)();if(!b)return a;let k="/offline"===j,l=b?.setOffline,{isOnline:m,isConnected:n}=((a={})=>{let{autoReconnect:b=!0,checkInterval:c=3e4,onStatusChange:e=null,onOffline:i=null,disabled:j=!1}=a,[k,l]=(0,d.useState)(null),[m,n]=(0,d.useState)(!1),[o,p]=(0,d.useState)(null),q=(0,d.useRef)(null),r=(0,d.useRef)(null),s=(0,d.useRef)(Math.random().toString(36).substr(2,9)),t=()=>{console.log("\uD83D\uDD07 Socket connections disabled via environment variable"),n(!1),l(!0),p(new Date)},u=()=>{g.forEach(a=>{a.id===s.current&&g.delete(a)})},v=()=>{f?.connected?f.emit("ping"):b&&h<5?(console.log("\uD83D\uDD04 Socket not connected, attempting to reconnect..."),t()):(l(!1),n(!1),p(new Date),e&&e(!1),h>=5&&i&&i())};return(0,d.useEffect)(()=>{if(!j)return t(),c>0&&(q.current=setInterval(v,c)),()=>{q.current&&clearInterval(q.current),r.current&&clearTimeout(r.current),u()}},[j]),{isOnline:k,isConnected:m,lastCheck:o,checkStatus:v,disconnect:()=>{q.current&&clearInterval(q.current),r.current&&clearTimeout(r.current),u(),0===g.size&&f&&(console.log("\uD83D\uDD0C No more listeners, disconnecting global socket"),f.disconnect(),f=null,h=0),n(!1),l(null)},reconnect:()=>{h=0,t()},reconnectAttempts:h,maxReconnectAttempts:5}})({autoReconnect:!0,checkInterval:3e4,disabled:k,onStatusChange:a=>{console.log("Backend status changed:",a)},onOffline:()=>{k||(console.log("Backend is offline, redirecting..."),l&&l(!0),c.push("/offline"))}});return a}},53361:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>o,metadata:()=>n});var d=c(37413);c(35692);var e=c(29519),f=c(45241),g=c.n(f),h=c(31696),i=c.n(h),j=c(78571);function k(){let a=`
    (function() {
      try {
        // First check for theme in localStorage
        const storedTheme = localStorage.getItem('sintesa-theme');
        
        if (storedTheme === 'dark') {
          document.documentElement.classList.add('dark');
        } else if (storedTheme === 'light') {
          document.documentElement.classList.remove('dark');
        } else {
          // If no stored theme, check system preference
          const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
          if (systemPrefersDark) {
            document.documentElement.classList.add('dark');
          }
        }
      } catch (e) {
        console.error('Failed to initialize theme', e);
      }
    })();
  `;return(0,d.jsx)("script",{dangerouslySetInnerHTML:{__html:a}})}var l=c(66799),m=c(75986);let n={title:"sintesaNEXT | PDPSIPA",description:"Sistem Informasi Terpadu Pelaksanaan Anggaran - Next Generation"};function o({children:a}){return(0,d.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,d.jsx)("head",{children:(0,d.jsx)(k,{})}),(0,d.jsx)("body",{className:(0,m.A)("font-sans antialiased",g().variable,i().variable),suppressHydrationWarning:!0,children:(0,d.jsxs)(e.Providers,{children:[(0,d.jsx)(l.default,{children:a}),(0,d.jsx)(j.ToastContainer,{})]})})]})}},54413:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\sintesaNEXT2\\\\src\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\app\\not-found.tsx","default")},57347:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j});var d=c(60687),e=c(57420),f=c(85015),g=c(36220),h=c(27580),i=c(16189);function j(){let a=(0,i.useRouter)();return(0,d.jsx)(e.LayoutJSX,{children:(0,d.jsx)("div",{className:"flex items-center justify-center min-h-[calc(100vh-200px)] p-4",children:(0,d.jsx)(f.Z,{className:"max-w-md w-full shadow-xl",children:(0,d.jsxs)(g.U,{className:"text-center p-8",children:[(0,d.jsx)("div",{className:"text-6xl font-bold text-primary mb-4",children:"404"}),(0,d.jsx)("h1",{className:"text-2xl font-semibold text-foreground mb-2",children:"Halaman Tidak Ditemukan"}),(0,d.jsx)("p",{className:"text-default-500 mb-6",children:"Maaf, halaman yang Anda cari tidak dapat ditemukan"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(h.T,{color:"primary",size:"lg",className:"w-full",onPress:()=>a.back(),children:"Kembali ke Halaman Sebelumnya"}),(0,d.jsx)(h.T,{variant:"ghost",size:"lg",className:"w-full",onPress:()=>a.push("/dashboard"),children:"Ke Dashboard"})]})]})})})})}},57420:(a,b,c)=>{"use strict";c.d(b,{LayoutJSX:()=>aH});var d,e,f=c(60687),g=c(44536),h=c(7566),i=c(90087),j=c(43210),k=c.n(j);let l=()=>(0,f.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 512 512",children:(0,f.jsxs)("g",{id:"_1528549580000",children:[(0,f.jsx)("polygon",{className:"fill-default-400",fillRule:"evenodd",clipRule:"evenodd",points:"59.794,487.879 59.794,24.406 416.532,24.121    416.532,202.489 380.858,202.489 380.858,60.08 95.468,60.08 95.468,452.206 416.532,452.063 416.532,487.594  "}),(0,f.jsx)("path",{className:"fill-default-400",d:"M357.884,416.817c-17.98,0-34.104-3.853-48.516-11.558c-14.27-7.706-25.542-18.408-33.676-31.964   c-8.134-13.556-12.272-28.824-12.272-45.662c0-16.981,4.138-32.249,12.272-45.662c8.134-13.556,19.407-24.258,33.676-31.964   c14.412-7.706,30.537-11.558,48.516-11.558c17.837,0,33.961,3.853,48.231,11.558c14.27,7.706,25.542,18.408,33.819,31.964   c8.134,13.413,12.272,28.682,12.272,45.662c0,16.838-4.138,32.106-12.272,45.662c-8.276,13.556-19.549,24.258-33.819,31.964   C391.846,412.965,375.721,416.817,357.884,416.817z M357.884,382.856c10.131,0,19.407-2.426,27.54-7.135   c8.276-4.709,14.698-11.13,19.407-19.549c4.566-8.419,6.992-17.837,6.992-28.539c0-10.702-2.426-20.263-6.992-28.682   c-4.709-8.276-11.13-14.84-19.407-19.549c-8.134-4.709-17.409-6.992-27.54-6.992c-10.274,0-19.407,2.283-27.683,6.992   c-8.134,4.709-14.555,11.273-19.264,19.549c-4.709,8.419-6.992,17.98-6.992,28.682c0,10.702,2.283,20.12,6.992,28.539   c4.709,8.419,11.13,14.84,19.264,19.549C338.478,380.43,347.61,382.856,357.884,382.856z"})]})});var m=c(24793),n=c(41871),o=c(16189);let p=()=>(0,f.jsxs)("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:18,role:"presentation",viewBox:"0 0 24 24",width:18,children:[(0,f.jsx)("path",{className:"stroke-default-400",d:"M11.5 21C16.7467 21 21 16.7467 21 11.5C21 6.25329 16.7467 2 11.5 2C6.25329 2 2 6.25329 2 11.5C2 16.7467 6.25329 21 11.5 21Z",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"}),(0,f.jsx)("path",{className:"stroke-default-400",d:"M22 22L20 20",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round"})]});c(51215);var q=c(85015),r=c(36220),s=c(69087),t=c(10473);let u=({hasil:a,sedangMemuat:b,onPilihSatker:c,kataPencarian:d,inputRef:e})=>{let[g,h]=(0,j.useState)({top:0,left:0,width:0});(0,j.useEffect)(()=>{if(e?.current){let a=e.current.getBoundingClientRect();h({top:a.bottom+window.scrollY,left:a.left+window.scrollX,width:a.width})}},[e,a]);let i=(a,b)=>{if(!b)return a;let c=RegExp(`(${b})`,"gi");return a.split(c).map((a,b)=>c.test(a)?(0,f.jsx)("mark",{className:"bg-yellow-200 dark:bg-yellow-800 px-1 rounded",children:a},b):a)};return b?(q.Z,g.top,g.left,g.width,r.U,s.o):0===a.length&&d.length>=3?(q.Z,g.top,g.left,g.width,r.U):0===a.length||(q.Z,g.top,g.left,g.width,r.U,a.map((b,e)=>(0,f.jsx)("div",{onClick:()=>c(b),className:`
              p-4 cursor-pointer transition-colors hover:bg-gray-100 dark:hover:bg-gray-800
              ${e!==a.length-1?"border-b border-gray-200 dark:border-gray-700":""}
            `,children:(0,f.jsx)("div",{className:"flex items-start justify-between gap-3",children:(0,f.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,f.jsx)("div",{className:"flex items-center gap-2 mb-1",children:(0,f.jsxs)(t.R,{size:"sm",variant:"flat",color:"primary",className:"text-xs font-mono",children:[b.level,".",b.alamat," ",i(b.kode,d)]})}),(0,f.jsx)("h4",{className:"font-medium text-sm leading-tight",children:i(b.nama,d)})]})})},b.id)),a.length>=10&&a.length),null},v=({placeholder:a="Ketik Kode atau Nama Satker...",className:b="w-full"})=>{let[c,d]=(0,j.useState)(""),[e,g]=(0,j.useState)([]),[h,i]=(0,j.useState)(!1),[k,l]=(0,j.useState)(!1),q=(0,j.useRef)(null),r=(0,j.useRef)(null),s=(0,o.useRouter)(),{cariSatker:t}=(0,m.H)();(0,j.useEffect)(()=>{let a=setTimeout(()=>{c.length>=3?v():(g([]),l(!1))},300);return()=>clearTimeout(a)},[c]),(0,j.useEffect)(()=>{let a=a=>{q.current&&!q.current.contains(a.target)&&l(!1)};return document.addEventListener("mousedown",a),()=>{document.removeEventListener("mousedown",a)}},[]);let v=async()=>{if(!(c.length<3)){i(!0);try{let a=await t(c);g(a),l(!0)}catch(a){console.error("Error saat mencari satker:",a),g([])}finally{i(!1)}}};return(0,f.jsxs)("div",{ref:q,className:`relative ${b}`,children:[(0,f.jsx)(n.r,{ref:r,startContent:(0,f.jsx)(p,{}),isClearable:!0,value:c,onValueChange:d,onFocus:()=>{e.length>0&&l(!0)},className:"w-full",classNames:{input:"w-full",mainWrapper:"w-full"},placeholder:a}),k&&(0,f.jsx)(u,{hasil:e,sedangMemuat:h,onPilihSatker:a=>{d(a.nama),l(!1),s.push(`/satker/${a.id}`)},kataPencarian:c,inputRef:r})]})};var w=c(12941);let x=(0,j.createContext)({collapsed:!1,setCollapsed:()=>{}}),y=()=>(0,j.useContext)(x);var z=c(98462);let A=(0,z.tv)({base:"flex flex-col justify-around w-6 h-6 bg-transparent border-none cursor-pointer padding-0 z-[202] focus:outline-none [&_div]:w-6 [&_div]:h-px [&_div]:bg-default-900 [&_div]:rounded-xl  [&_div]:transition-all  [&_div]:relative  [&_div]:origin-[1px] ",variants:{open:{true:"[&"}}}),B=()=>{let{collapsed:a,setCollapsed:b}=y();return(0,f.jsx)("div",{className:A(),onClick:b,children:(0,f.jsx)(w.A,{size:24})})};var C=c(10218),D=c(80505);let E=()=>{let{setTheme:a,resolvedTheme:b,theme:c}=(0,C.D)(),[d,e]=k().useState(!1);k().useEffect(()=>{e(!0)},[]),k().useEffect(()=>{if(d)try{let b=localStorage.getItem("sintesa-theme");b&&c!==b&&(console.log("Setting theme from localStorage:",b),a(b))}catch(a){console.error("Error syncing theme:",a)}},[d,c,a]);let g=()=>(0,f.jsx)("svg",{"aria-hidden":"true",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",children:(0,f.jsx)("path",{d:"M21.53 15.93c-.16-.27-.61-.69-1.73-.49a8.46 8.46 0 01-1.88.13 8.409 8.409 0 01-5.91-2.82 8.068 8.068 0 01-1.44-8.66c.44-1.01.13-1.54-.09-1.76s-.77-.55-1.83-.11a10.318 10.318 0 00-6.32 10.21 10.475 10.475 0 007.04 8.99 10 10 0 002.89.55c.16.01.32.02.48.02a10.5 10.5 0 008.47-4.27c.67-.93.49-1.519.32-1.79z",fill:"currentColor"})}),h=()=>(0,f.jsx)("svg",{"aria-hidden":"true",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",children:(0,f.jsxs)("g",{fill:"currentColor",children:[(0,f.jsx)("path",{d:"M19 12a7 7 0 11-7-7 7 7 0 017 7z"}),(0,f.jsx)("path",{d:"M12 22.96a.969.969 0 01-1-.96v-.08a1 1 0 012 0 1.038 1.038 0 01-1 1.04zm7.14-2.82a1.024 1.024 0 01-.71-.29l-.13-.13a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.984.984 0 01-.7.29zm-14.28 0a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a1 1 0 01-.7.29zM22 13h-.08a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zM2.08 13H2a1 1 0 010-2 1.038 1.038 0 011.04 1 .969.969 0 01-.96 1zm16.93-7.01a1.024 1.024 0 01-.71-.29 1 1 0 010-1.41l.13-.13a1 1 0 011.41 1.41l-.13.13a.984.984 0 01-.7.29zm-14.02 0a1.024 1.024 0 01-.71-.29l-.13-.14a1 1 0 011.41-1.41l.13.13a1 1 0 010 1.41.97.97 0 01-.7.3zM12 3.04a.969.969 0 01-1-.96V2a1 1 0 012 0 1.038 1.038 0 01-1 1.04z"})]})});return d?(0,f.jsx)(D.Z,{isSelected:"dark"===b,onValueChange:b=>{let c=b?"dark":"light";a(c),console.log("Theme changed to:",c)},"aria-label":"Toggle dark mode",color:"warning",thumbIcon:({isSelected:a,className:b})=>a?(0,f.jsx)(g,{}):(0,f.jsx)(h,{})}):(0,f.jsx)(D.Z,{isSelected:!1,"aria-label":"Toggle dark mode",className:"opacity-0"})};var F=c(84819),G=c(94206),H=c(27146),I=c(69194),J=c(42042),K=c(68478);let L=()=>{let{notifications:a,unreadCount:b,markAsRead:c}=(0,F.E)();return(0,f.jsxs)("div",{className:"relative flex items-center",children:[" ",(0,f.jsxs)(G.A,{placement:"bottom-end",className:"min-w-[320px]",closeOnSelect:!1,children:[(0,f.jsx)(H.b,{children:(0,f.jsxs)("button",{className:"relative p-2 rounded-xl hover:bg-zinc-100 dark:hover:bg-zinc-700 transition-all duration-150 cursor-pointer flex items-center justify-center focus:outline-none focus:ring-0 focus-visible:outline-none focus-visible:ring-0 active:outline-none","aria-label":"Toggle notifications",type:"button",style:{outline:"none",boxShadow:"none"},children:[(0,f.jsx)(K.A,{size:24,className:"text-default-400 dark:text-default-400"}),b>0&&(0,f.jsx)("span",{className:"absolute -top-1 -right-1 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-red-600 rounded-full",children:b})]})}),(0,f.jsx)(I.y,{"aria-label":"Notifications",className:"w-full",onAction:a=>{"empty"!==a&&c(Number(a))},children:0===a.length?(0,f.jsx)(J.Y,{className:"text-default-500",isReadOnly:!0,children:"No notifications"},"empty"):a.map(a=>(0,f.jsxs)(J.Y,{className:`text-default-900 transition-colors ${a.read?"opacity-60":""}`,children:[" ",(0,f.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,f.jsx)("div",{className:`text-sm font-medium ${a.read?"text-gray-500 dark:text-gray-400":"text-gray-900 dark:text-gray-100"}`,children:a.header}),(0,f.jsx)("div",{className:`text-xs ${a.read?"text-gray-400 dark:text-gray-500":"text-gray-600 dark:text-gray-300"}`,children:a.message})]})]},a.id))})]})]})};var M=c(67492);function N(){return(N=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}let O=function(a){return j.createElement("svg",N({xmlns:"http://www.w3.org/2000/svg",id:"snext_light_svg__Layer_2","data-name":"Layer 2",viewBox:"0 0 3399.57 600"},a),d||(d=j.createElement("defs",null,j.createElement("style",null,".snext_light_svg__cls-1{fill:#eaa221}"))),e||(e=j.createElement("g",{id:"snext_light_svg__Layer_1-2","data-name":"Layer 1"},j.createElement("path",{d:"M525 150c-41.46 0-75-33.54-75-75S416.42 0 375 0H75C33.58 0 0 33.58 0 75v300c0 41.42 33.58 75 75 75s75 33.54 75 75 33.58 75 75 75h300c41.42 0 75-33.58 75-75V225c0-41.42-33.58-75-75-75m-75 37.5V375c0 41.42-33.58 75-75 75H112.5c20.71 0 37.5-16.79 37.5-37.5V225c0-41.42 33.58-75 75-75h262.5c-20.71 0-37.5 16.79-37.5 37.5M1031.72 600c-27.04 0-51.91-4.06-74.62-12.17s-42.18-19.59-58.4-34.47c-14.95-13.7-26.7-29.59-35.21-47.67a4.987 4.987 0 0 1 2.5-6.68L935.94 468c2.32-1.03 5.02-.14 6.31 2.03 8.03 13.45 19.33 24.88 33.91 34.26 15.94 10.28 33.66 15.41 53.12 15.41 21.09 0 37.98-3.65 50.69-10.95 12.7-7.3 19.06-17.44 19.06-30.42s-4.87-22.03-14.6-28.79-23.52-12.29-41.36-16.63l-38.12-10.54c-38.4-9.73-68.41-25.27-90.03-46.64-21.63-21.35-32.44-45.83-32.44-73.4 0-40.55 13.1-71.91 39.34-94.08 26.22-22.17 63.94-33.25 113.14-33.25 24.86 0 47.71 3.65 68.54 10.95 20.81 7.3 38.79 17.58 53.94 30.82 13.87 12.15 23.88 26.21 30.04 42.22.95 2.46-.22 5.24-2.62 6.32l-66.69 30.12c-2.44 1.1-5.3.07-6.51-2.32-5.77-11.41-15.39-20.36-28.85-26.86-15.14-7.3-31.91-10.95-50.29-10.95s-32.72 3.93-42.99 11.76c-10.28 7.84-15.41 18.79-15.41 32.85 0 8.11 4.59 15.55 13.79 22.3 9.19 6.77 22.43 12.31 39.74 16.63l47.85 11.36q39.735 9.735 64.89 30.42c16.75 13.79 29.2 29.2 37.31 46.23s12.17 34.2 12.17 51.5c0 24.33-6.89 45.7-20.68 64.07-13.79 18.39-32.58 32.58-56.37 42.58-23.8 10-50.83 15-81.11 15ZM1313.84 108.26c-14.88 0-27.69-5.37-38.43-16.12-10.74-10.74-16.12-23.55-16.12-38.43s5.37-27.54 16.12-38.02c10.74-10.46 23.55-15.7 38.43-15.7s27.69 5.24 38.43 15.7c10.74 10.47 16.12 23.14 16.12 38.02s-5.37 27.69-16.12 38.43c-10.74 10.74-23.55 16.12-38.43 16.12m-46.28 486.66V191.86a5.08 5.08 0 0 1 5.08-5.08h81.57a5.08 5.08 0 0 1 5.08 5.08v403.06a5.08 5.08 0 0 1-5.08 5.08h-81.57a5.08 5.08 0 0 1-5.08-5.08M1462.03 594.9V190.07c0-2.82 2.29-5.1 5.1-5.1h77.3c2.68 0 4.9 2.07 5.09 4.75l2.77 39.27c.33 4.74 6.39 6.48 9.21 2.65 11.96-16.25 26.53-29.18 43.72-38.78 21.3-11.89 45.78-17.85 73.46-17.85q49.8 0 85.08 19.92c23.51 13.28 41.5 33.9 53.96 61.84 12.45 27.95 18.68 64.33 18.68 109.16v228.98c0 2.82-2.29 5.1-5.1 5.1h-82.76c-2.82 0-5.1-2.29-5.1-5.1V387.52c0-33.2-3.73-58.79-11.21-76.78-7.47-17.98-17.99-30.57-31.54-37.77-13.57-7.19-29.47-10.79-47.73-10.79-31-.54-55.07 9.69-72.22 30.71-17.16 21.04-25.73 51.19-25.73 90.48v211.55c0 2.82-2.29 5.1-5.1 5.1h-82.76c-2.82 0-5.1-2.29-5.1-5.1ZM2086.2 600c-42.82 0-75.48-10.7-97.97-32.11-22.5-21.41-33.74-52.16-33.74-92.28V266.79c0-2.76-2.24-5-5-5h-57.48c-2.76 0-5-2.24-5-5v-68.05c0-2.76 2.24-5 5-5h57.48c2.76 0 5-2.24 5-5V61.91c0-2.76 2.24-5 5-5h80.24c2.76 0 5 2.24 5 5v116.83c0 2.76 2.24 5 5 5h99.76c2.76 0 5 2.24 5 5v68.05c0 2.76-2.24 5-5 5h-99.76c-2.76 0-5 2.24-5 5v191.75c0 18.98 4.47 33.33 13.41 43.09s22.08 14.63 39.43 14.63c5.41 0 11.38-1.08 17.89-3.25 5.21-1.73 10.95-4.69 17.21-8.86 2.53-1.69 5.98-.78 7.3 1.97l28.9 59.92c1.09 2.26.33 4.97-1.76 6.36-12.89 8.56-25.76 15.19-38.64 19.89-14.1 5.14-28.19 7.72-42.28 7.72ZM2420.33 600c-39.48 0-74.49-9.06-105.03-27.17-30.55-18.11-54.62-43.11-72.19-75.02-17.58-31.9-26.36-68.66-26.36-110.31s8.92-78.4 26.76-110.31c17.84-31.9 42.3-56.9 73.4-75.02C2348 184.06 2383.54 175 2423.57 175c36.22 0 68.94 9.33 98.14 27.98s52.31 45.56 69.35 80.7c16.44 33.93 24.94 74.13 25.52 120.64.03 2.79-2.2 5.07-4.99 5.07h-295.26c-2.97 0-5.3 2.58-4.96 5.53 3.57 31.44 15.97 56.37 37.18 74.77 22.43 19.47 49.07 29.2 79.89 29.2 24.86 0 45.42-5.54 61.64-16.63 15.01-10.25 27.01-23.41 36.01-39.46a4.98 4.98 0 0 1 6.29-2.16l71.06 30.15c2.74 1.16 3.86 4.44 2.41 7.04-10.93 19.61-24.52 36.9-40.75 51.88-17.58 16.22-38.4 28.67-62.45 37.31-24.07 8.64-51.5 12.98-82.32 12.98m-98.11-260.35h190.7c2.99 0 5.33-2.61 4.96-5.57-2.18-16.98-7.66-31.21-16.45-42.69-9.73-12.7-21.63-22.3-35.69-28.79-14.07-6.49-28.93-9.73-44.61-9.73s-30.42 3.12-45.83 9.33c-15.41 6.22-28.53 15.82-39.34 28.79-9.73 11.67-15.96 25.96-18.68 42.87a4.99 4.99 0 0 0 4.93 5.79ZM2834.96 600c-27.04 0-51.91-4.06-74.62-12.17s-42.18-19.59-58.4-34.47c-14.95-13.7-26.7-29.59-35.21-47.67a4.987 4.987 0 0 1 2.5-6.68l69.95-31.01c2.32-1.03 5.02-.14 6.31 2.03 8.03 13.45 19.33 24.88 33.91 34.26 15.94 10.28 33.66 15.41 53.12 15.41 21.09 0 37.98-3.65 50.69-10.95 12.7-7.3 19.06-17.44 19.06-30.42s-4.87-22.03-14.6-28.79-23.52-12.29-41.36-16.63l-38.12-10.54c-38.4-9.73-68.41-25.27-90.03-46.64-21.63-21.35-32.44-45.83-32.44-73.4 0-40.55 13.1-71.91 39.34-94.08C2751.28 186.08 2789 175 2838.2 175c24.86 0 47.71 3.65 68.53 10.95 20.81 7.3 38.79 17.58 53.94 30.82 13.87 12.15 23.88 26.21 30.04 42.22.95 2.46-.22 5.24-2.62 6.32l-66.69 30.12c-2.44 1.1-5.3.07-6.51-2.32-5.77-11.41-15.39-20.36-28.85-26.86-15.14-7.3-31.91-10.95-50.29-10.95s-32.72 3.93-42.99 11.76c-10.28 7.84-15.41 18.79-15.41 32.85 0 8.11 4.59 15.55 13.79 22.3 9.19 6.77 22.43 12.31 39.74 16.63l47.85 11.36q39.735 9.735 64.89 30.42c16.75 13.79 29.2 29.2 37.31 46.23s12.17 34.2 12.17 51.5c0 24.33-6.89 45.7-20.68 64.07-13.79 18.39-32.58 32.58-56.37 42.58-23.8 10-50.83 15-81.11 15ZM3196.81 600c-48.66 0-86.38-10.81-113.14-32.44-26.76-21.62-40.15-52.17-40.15-91.65 0-42.18 14.19-74.34 42.58-96.52 28.39-22.16 67.99-33.25 118.82-33.25h97.37c3.01 0 5.35-2.66 4.95-5.64-3.65-26.98-11.81-47.81-24.45-62.49-13.52-15.68-33.53-23.52-60.02-23.52-19.47 0-36.5 4.06-51.1 12.17-13.62 7.57-25.37 19.14-35.22 34.71a4.97 4.97 0 0 1-5.92 2.01l-70.4-25.86c-2.82-1.04-4.1-4.32-2.71-6.99 8.31-15.94 18.96-30.88 31.93-44.82 14.32-15.41 32.44-27.7 54.34-36.9 21.9-9.19 48.26-13.79 79.08-13.79 39.46 0 72.45 7.71 98.95 23.12 26.49 15.41 46.09 37.45 58.8 66.1 12.7 28.67 19.06 63.26 19.06 103.82l-2.38 217.3a4.985 4.985 0 0 1-4.99 4.93h-73.86c-2.66 0-4.85-2.09-4.98-4.74l-1.45-29.5c-.23-4.66-6.15-6.47-8.96-2.75-9.8 12.99-22.07 23.43-36.8 31.31-19.2 10.27-42.31 15.41-69.35 15.41Zm11.35-76.24q30 0 53.13-13.38c23.13-13.38 27.3-20.95 35.69-36.09 8.38-15.13 12.57-31.9 12.57-50.29v-3.12c0-2.75-2.23-4.99-4.99-4.99h-70.44c-36.23 0-61.64 5.01-76.24 15-14.6 10.01-21.9 24.21-21.9 42.58 0 15.69 6.35 27.98 19.06 36.9 12.7 8.92 30.42 13.38 53.12 13.38Z",className:"snext_light_svg__cls-1"}))))};function P(){return(P=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}let Q=function(a){return j.createElement("svg",P({xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 2",viewBox:"0 0 1600 1600"},a),j.createElement("path",{d:"M1400 400c-110.55 0-200-89.45-200-200S1110.46 0 1000 0H200C89.54 0 0 89.54 0 200v800c0 110.46 89.54 200 200 200s200 89.45 200 200 89.54 200 200 200h800c110.46 0 200-89.54 200-200V600c0-110.46-89.54-200-200-200m-200 100v500c0 110.46-89.54 200-200 200H300c55.23 0 100-44.77 100-100V600c0-110.46 89.54-200 200-200h700c-55.23 0-100 44.77-100 100","data-name":"Layer 1",style:{fill:"#0f52ba"}}))};function R(){return(R=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}let S=function(a){return j.createElement("svg",R({xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 2",viewBox:"0 0 1600 1600"},a),j.createElement("path",{d:"M1400 400c-110.55 0-200-89.45-200-200S1110.46 0 1000 0H200C89.54 0 0 89.54 0 200v800c0 110.46 89.54 200 200 200s200 89.45 200 200 89.54 200 200 200h800c110.46 0 200-89.54 200-200V600c0-110.46-89.54-200-200-200m-200 100v500c0 110.46-89.54 200-200 200H300c55.23 0 100-44.77 100-100V600c0-110.46 89.54-200 200-200h700c-55.23 0-100 44.77-100 100","data-name":"Layer 1",style:{fill:"#eaa221"}}))};var T=c(66236),U=c(92231),V=c(85814),W=c.n(V),X=c(14221);let Y=()=>{(0,o.useRouter)();let{name:a,logout:b}=(0,j.useContext)(X.A)||{name:"User"},c=(0,j.useCallback)(async()=>{await b()},[]);return(0,f.jsxs)(G.A,{children:[(0,f.jsx)(T.p,{children:(0,f.jsx)(H.b,{children:(0,f.jsx)(U.Q,{as:"button",color:"secondary",size:"md",src:"https://i.pravatar.cc/150?u=a042581f4e29026704d"})})}),(0,f.jsxs)(I.y,{disabledKeys:["username"],"aria-label":"User menu actions",onAction:a=>console.log({actionKey:a}),children:[(0,f.jsxs)(J.Y,{className:"flex flex-col justify-start w-full items-start",children:[(0,f.jsx)("p",{children:"Signed in as"}),(0,f.jsx)("p",{children:a})]},"username"),(0,f.jsx)(J.Y,{children:(0,f.jsx)(W(),{href:"/profil-user",className:"dropdown-item",children:"Profil User"})},"profil-user"),(0,f.jsx)(J.Y,{children:"Settings"},"settings"),(0,f.jsx)(J.Y,{children:(0,f.jsx)(W(),{href:"/accounts",className:"dropdown-item",children:"User Management"})},"user-manage"),(0,f.jsx)(J.Y,{color:"danger",className:"text-danger",onPress:c,children:"Log Out"},"logout")]})]})},Z=({children:a})=>(0,f.jsxs)("div",{className:"w-full relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden",children:[(0,f.jsxs)(g.H,{isBordered:!0,className:"w-full fixed top-0 z-50 bg-white dark:bg-black",classNames:{wrapper:"w-full max-w-full"},children:[(0,f.jsxs)(h.t,{className:"xl:hidden",justify:"start",children:[(0,f.jsxs)("span",{className:"flex items-center mr-4",children:[(0,f.jsx)(Q,{className:"h-6 w-auto block dark:hidden"}),(0,f.jsx)(S,{className:"h-6 w-auto hidden dark:block"})]}),(0,f.jsx)(B,{})]}),(0,f.jsxs)(h.t,{className:"xl:hidden",justify:"center",children:[(0,f.jsx)("div",{className:"flex-1",children:(0,f.jsx)(v,{})}),(0,f.jsx)(L,{}),(0,f.jsx)(E,{}),(0,f.jsx)(Y,{})]}),(0,f.jsxs)(h.t,{className:"w-full max-xl:hidden",children:[(0,f.jsx)("div",{className:"flex-shrink-0"}),(0,f.jsxs)("span",{className:"flex items-center mr-4",children:[(0,f.jsx)(M.A,{className:"h-6 w-auto block dark:hidden"}),(0,f.jsx)(O,{className:"h-6 w-auto hidden dark:block"})]}),(0,f.jsx)(v,{placeholder:"Ketik Kode atau Nama Satker..."})]}),(0,f.jsxs)(h.t,{justify:"end",className:"w-fit data-[justify=end]:flex-grow-0 max-xl:hidden",children:[(0,f.jsx)(L,{}),(0,f.jsx)(i.h,{href:"https://spanint.kemenkeu.go.id/",target:"_blank",children:(0,f.jsx)(l,{})}),(0,f.jsx)(E,{}),(0,f.jsx)(Y,{})]})]}),(0,f.jsx)("div",{className:"pt-16",children:a})]});var $=c(77611),_=c(32192),aa=c(49410),ab=c(41312),ac=c(5014),ad=c(61611),ae=c(49653),af=c(96882),ag=c(84027),ah=c(78272),ai=c(49384),aj=c(53411),ak=c(10022),al=c(17313);let am=({icon:a,title:b,items:c,isActive:d,children:e})=>{let g=(0,o.useRouter)(),h=(0,o.usePathname)(),i=k().useMemo(()=>0===c.length?[]:"string"==typeof c[0]?c.map((a,c)=>{let d,e;if("epa"===b.toLowerCase()){let b=a.toLowerCase().replace(/\s+/g,"-").replace(/[\/]/g,"-");switch(e=`/epa/${b}`,a.toLowerCase()){case"summary":d=(0,f.jsx)(aj.A,{size:20}),e="/epa/summary";break;case"analisa":d=(0,f.jsx)(ak.A,{size:20}),e="/epa/analisa";break;default:d=(0,f.jsx)(al.A,{size:20})}}else d=(0,f.jsx)(al.A,{size:20}),e=`/${a.toLowerCase().replace(/\s+/g,"-")}`;return{label:a,href:e,icon:d}}):c,[c,b]);return(0,f.jsx)("div",{className:"relative",children:(0,f.jsxs)(G.A,{placement:"bottom-start",className:"min-w-[240px]",children:[(0,f.jsx)(H.b,{children:(0,f.jsxs)("div",{"data-testid":"hover-dropdown-button",className:(0,ai.A)(d?"bg-success-100 [&_svg]:stroke-success-500":"hover:bg-success-100","flex gap-2 w-full min-h-[44px] h-full items-center px-3.5 rounded-xl cursor-pointer transition-all duration-150 active:scale-[0.98]"),children:[a,(0,f.jsx)("span",{className:"text-default-900",children:b}),e]})}),(0,f.jsx)(I.y,{"aria-label":`${b} menu`,className:"w-full font-sans py-2",onAction:a=>{var b;let c=i[Number(a)];c&&(b=c.href,g.push(b))},children:i.map((a,b)=>{let c=h===a.href;return(0,f.jsx)(J.Y,{className:(0,ai.A)(c?"bg-success-100 [&_*]:text-primary":"data-[hover=true]:bg-default-100","font-sans text-sm py-3 px-4 min-h-[44px] group"),children:(0,f.jsxs)("div",{className:"flex items-center gap-3",children:[" ",(0,f.jsx)("span",{className:(0,ai.A)(c?"text-default-900 [&_svg]:stroke-success":"text-default-900 group-hover:text-success [&_svg]:group-hover:stroke-success","flex-shrink-0 transition-colors"),children:k().cloneElement(a.icon,{strokeWidth:2.5})}),(0,f.jsx)("span",{className:(0,ai.A)(c?"!text-success font-medium":"text-default-900 group-hover:text-success font-medium","text-base transition-colors"),children:a.label})]})},b)})})]})})};var an=c(69050),ao=c(87352);let ap=({...a})=>(0,f.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",...a,children:(0,f.jsx)("path",{className:"fill-default-400",d:"m6.293 10.707 1.414-1.414L12 13.586l4.293-4.293 1.414 1.414L12 16.414z"})}),aq=({icon:a,items:b,title:c,isActive:d=!1})=>{let[e,g]=(0,j.useState)(!1),h=(0,o.useRouter)(),i=(0,o.usePathname)(),{collapsed:k,setCollapsed:l}=y();return(0,f.jsx)("div",{className:"flex gap-4 h-full items-center cursor-pointer",children:(0,f.jsx)(an.D,{className:"px-0 w-full",children:(0,f.jsx)(ao.R,{indicator:(0,f.jsx)(ap,{}),classNames:{indicator:"data-[open=true]:-rotate-180",trigger:(0,ai.A)(d?"bg-primary-100 [&_svg]:stroke-primary-500":"hover:bg-default-100","py-0 min-h-[44px] rounded-xl active:scale-[0.98] transition-transform px-3.5"),title:"px-0 flex text-base gap-2 h-full items-center cursor-pointer",content:"px-0 pb-2"},"aria-label":"Accordion 1",title:(0,f.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,f.jsx)("span",{children:a}),(0,f.jsx)("span",{children:c})]}),children:(0,f.jsx)("div",{className:"space-y-1",children:b.map((a,b)=>{let d=(a=>"string"==typeof a?a:a.label)(a),e=(a=>{if("string"!=typeof a)return i===a.href;if("epa"===c.toLowerCase())switch(a.toLowerCase()){case"summary":return"/epa/summary"===i;case"analisa":return"/epa/analisa"===i;default:let b=a.toLowerCase().replace(/\s+/g,"-").replace(/[\/]/g,"-");return i===`/epa/${b}`}return i===`/${a.toLowerCase().replace(/\s+/g,"-")}`})(a);return(0,f.jsxs)("div",{onClick:()=>(a=>{if("string"==typeof a){let b;if("epa"===c.toLowerCase())switch(a.toLowerCase()){case"summary":b="/epa/summary";break;case"analisa":b="/epa/analisa";break;default:let d=a.toLowerCase().replace(/\s+/g,"-").replace(/[\/]/g,"-");b=`/epa/${d}`}else b=`/${a.toLowerCase().replace(/\s+/g,"-")}`;h.push(b)}else h.push(a.href);window.innerWidth<1280&&l()})(a),className:(0,ai.A)(e?"bg-primary-100 text-primary [&_svg]:text-primary [&_svg]:stroke-primary":"hover:text-primary hover:bg-default-100","w-full flex py-2 pl-8 pr-2 rounded-lg transition-colors cursor-pointer min-h-[36px] items-center gap-2 group"),children:[(0,f.jsx)("span",{className:(0,ai.A)(e?"text-primary":"text-default-400 group-hover:text-primary","transition-colors"),children:(a=>{if("epa"!==c.toLowerCase())return(0,f.jsx)(al.A,{size:16});switch(a.toLowerCase()){case"summary":return(0,f.jsx)(aj.A,{size:16});case"analisa":return(0,f.jsx)(ak.A,{size:16});default:return(0,f.jsx)(al.A,{size:16})}})(d)}),(0,f.jsx)("span",{className:(0,ai.A)(e?"text-primary":"group-hover:text-primary","text-base transition-colors"),children:d})]},b)})})})})})};var ar=c(85778),as=c(62100);let at=({icon:a,title:b,items:c,isActive:d,children:e})=>{let g=(0,o.useRouter)(),h=(0,o.usePathname)(),i=k().useMemo(()=>0===c.length?[]:"string"==typeof c[0]?c.map((a,c)=>{let d,e;if("inquiry data"===b.toLowerCase()){let b=a.toLowerCase().replace(/\s+/g,"-").replace(/[\/]/g,"-");switch(e=`/inquiry-data/${b}`,a.toLowerCase()){case"belanja":case"bansos":d=(0,f.jsx)(ar.A,{size:20});break;case"tematik":case"up/tup":case"rkakl detail":default:d=(0,f.jsx)(al.A,{size:20});break;case"kontrak":case"deviasi":d=(0,f.jsx)(as.A,{size:20})}}else d=(0,f.jsx)(al.A,{size:20}),e=`/${a.toLowerCase().replace(/\s+/g,"-")}`;return{label:a,href:e,icon:d}}):c,[c,b]),j=k().useMemo(()=>i.some(a=>h===a.href),[i,h]);return(0,f.jsx)("div",{className:"relative",children:(0,f.jsxs)(G.A,{placement:"bottom-start",className:"min-w-[240px]",children:[(0,f.jsx)(H.b,{children:(0,f.jsxs)("div",{"data-testid":"hover-dropdown-button",className:(0,ai.A)(("boolean"==typeof d?d:j)?"bg-secondary-100 [&_svg]:stroke-secondary-500":"hover:bg-secondary-100","flex gap-2 w-full min-h-[44px] h-full items-center px-3.5 rounded-xl cursor-pointer transition-all duration-150 active:scale-[0.98]"),children:[a,(0,f.jsx)("span",{className:"text-default-900",children:b}),e]})}),(0,f.jsx)(I.y,{"aria-label":`${b} menu`,className:"w-full font-sans py-2",onAction:a=>{var b;let c=i[Number(a)];c&&(b=c.href,g.push(b))},children:i.map((a,b)=>{let c=h===a.href;return(0,f.jsx)(J.Y,{className:(0,ai.A)(c?"bg-secondary-100 [&_*]:text-secondary":"data-[hover=true]:bg-default-100","font-sans text-sm py-3 px-4 min-h-[44px] group"),children:(0,f.jsxs)("div",{className:"flex items-center gap-3",children:[" ",(0,f.jsx)("span",{className:(0,ai.A)(c?"text-default-900 [&_svg]:stroke-secondary":"text-default-900 group-hover:text-secondary [&_svg]:group-hover:stroke-secondary","flex-shrink-0 transition-colors"),children:k().cloneElement(a.icon,{strokeWidth:2.5})}),(0,f.jsx)("span",{className:(0,ai.A)(c?"text-secondary font-medium":"text-default-900 group-hover:text-secondary font-medium","text-base transition-colors"),children:a.label})]})},b)})})]})})},au=({icon:a,items:b,title:c,isActive:d=!1})=>{let[e,g]=(0,j.useState)(!1),h=(0,o.useRouter)(),i=(0,o.usePathname)(),{collapsed:k,setCollapsed:l}=y();return(0,f.jsx)("div",{className:"flex gap-4 h-full items-center cursor-pointer",children:(0,f.jsx)(an.D,{className:"px-0 w-full",children:(0,f.jsx)(ao.R,{indicator:(0,f.jsx)(ap,{}),classNames:{indicator:"data-[open=true]:-rotate-180",trigger:(0,ai.A)(d?"bg-primary-100 [&_svg]:stroke-primary-500":"hover:bg-default-100","py-0 min-h-[44px] rounded-xl active:scale-[0.98] transition-transform px-3.5"),title:"px-0 flex text-base gap-2 h-full items-center cursor-pointer",content:"px-0 pb-2"},"aria-label":"Accordion 1",title:(0,f.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,f.jsx)("span",{children:a}),(0,f.jsx)("span",{children:c})]}),children:(0,f.jsx)("div",{className:"space-y-1",children:b.map((a,b)=>{let d=(a=>"string"==typeof a?a:a.label)(a),e=(a=>{if("string"!=typeof a)return i===a.href;if("inquiry data"===c.toLowerCase()){let b=a.toLowerCase().replace(/\s+/g,"-").replace(/[\/]/g,"-");return i===`/inquiry-data/${b}`}return i===`/${a.toLowerCase().replace(/\s+/g,"-")}`})(a);return(0,f.jsxs)("div",{onClick:()=>(a=>{if("string"==typeof a){let b;if("inquiry data"===c.toLowerCase()){let c=a.toLowerCase().replace(/\s+/g,"-").replace(/[\/]/g,"-");b=`/inquiry-data/${c}`}else b=`/${a.toLowerCase().replace(/\s+/g,"-")}`;h.push(b)}else h.push(a.href);window.innerWidth<1280&&l()})(a),className:(0,ai.A)(e?"bg-primary-100 text-primary [&_svg]:text-primary":"hover:text-primary hover:bg-default-100","w-full flex py-2 pl-8 pr-2 rounded-xl transition-colors cursor-pointer min-h-[36px] items-center gap-2 group"),children:[(0,f.jsx)("span",{className:(0,ai.A)(e?"text-primary":"text-default-400 group-hover:text-primary","transition-colors"),children:(a=>{if("inquiry data"!==c.toLowerCase())return(0,f.jsx)(al.A,{size:16});switch(a.toLowerCase()){case"belanja":case"bansos":return(0,f.jsx)(ar.A,{size:16});case"tematik":case"up/tup":case"rkakl detail":default:return(0,f.jsx)(al.A,{size:16});case"kontrak":case"deviasi":return(0,f.jsx)(as.A,{size:16})}})(d)}),(0,f.jsx)("span",{className:(0,ai.A)(e?"text-primary":"group-hover:text-primary","text-base transition-colors"),children:d})]},b)})})})})})},av=({icon:a,title:b,items:c,isActive:d,children:e})=>{let g=(0,o.useRouter)(),h=(0,o.usePathname)(),i=k().useMemo(()=>0===c.length?[]:"string"==typeof c[0]?c.map((a,c)=>{let d,e;if("mbg"===b.toLowerCase()||"makan bergizi"===b.toLowerCase()){let b=a.toLowerCase().replace(/\s+/g,"-").replace(/[\/]/g,"-");switch(e=`/mbg/${b}`,a.toLowerCase()){case"dashboard":d=(0,f.jsx)(aj.A,{size:20}),e="/mbg/dashboard-mbg";break;case"kertas kerja":d=(0,f.jsx)(ak.A,{size:20}),e="/mbg/kertas-kerja";break;case"data":d=(0,f.jsx)(ad.A,{size:20}),e="/mbg/data-update";break;default:d=(0,f.jsx)(al.A,{size:20})}}else d=(0,f.jsx)(al.A,{size:20}),e=`/${a.toLowerCase().replace(/\s+/g,"-")}`;return{label:a,href:e,icon:d}}):c,[c,b]);return(0,f.jsx)("div",{className:"relative",children:(0,f.jsxs)(G.A,{placement:"bottom-start",className:"min-w-[240px]",children:[(0,f.jsx)(H.b,{children:(0,f.jsxs)("div",{"data-testid":"hover-dropdown-button",className:(0,ai.A)(d?"bg-amber-100 [&_svg]:stroke-amber-700":"hover:bg-amber-100","flex gap-2 w-full min-h-[44px] h-full items-center px-3.5 rounded-xl cursor-pointer transition-all duration-150 active:scale-[0.98]"),children:[a,(0,f.jsx)("span",{className:"text-default-900 ",children:b}),e]})}),(0,f.jsx)(I.y,{"aria-label":`${b} menu`,className:"w-full font-sans py-2",onAction:a=>{var b;let c=i[Number(a)];c&&(b=c.href,g.push(b))},children:i.map((a,b)=>{let c=h===a.href;return(0,f.jsx)(J.Y,{className:(0,ai.A)(c?"bg-amber-100 [&_*]:text-primary":"data-[hover=true]:bg-default-100","font-sans font-semibold text-sm py-3 px-4 min-h-[44px] group"),children:(0,f.jsxs)("div",{className:"flex items-center gap-3",children:[" ",(0,f.jsx)("span",{className:(0,ai.A)(c?"text-default-900 [&_svg]:stroke-amber-700 ":"text-default-900 group-hover:text-amber-700 [&_svg]:group-hover:stroke-amber-700","flex-shrink-0 transition-colors"),children:k().cloneElement(a.icon,{strokeWidth:2.5})}),(0,f.jsx)("span",{className:(0,ai.A)(c?"!text-amber-700 font-medium":"text-default-900 group-hover:text-amber-700 font-medium","text-base transition-colors"),children:a.label})]})},b)})})]})})},aw=({icon:a,items:b,title:c,isActive:d=!1})=>{let[e,g]=(0,j.useState)(!1),h=(0,o.useRouter)(),i=(0,o.usePathname)(),{collapsed:k,setCollapsed:l}=y();return(0,f.jsx)("div",{className:"flex gap-4 h-full items-center cursor-pointer",children:(0,f.jsx)(an.D,{className:"px-0 w-full",children:(0,f.jsx)(ao.R,{indicator:(0,f.jsx)(ap,{}),classNames:{indicator:"data-[open=true]:-rotate-180",trigger:(0,ai.A)(d?"bg-primary-100 [&_svg]:stroke-primary-500":"hover:bg-default-100","py-0 min-h-[44px] rounded-xl active:scale-[0.98] transition-transform px-3.5"),title:"px-0 flex text-base gap-2 h-full items-center cursor-pointer",content:"px-0 pb-2"},"aria-label":"Accordion 1",title:(0,f.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,f.jsx)("span",{children:a}),(0,f.jsx)("span",{children:c})]}),children:(0,f.jsx)("div",{className:"space-y-1",children:b.map((a,b)=>{let d=(a=>"string"==typeof a?a:a.label)(a),e=(a=>{if("string"!=typeof a)return i===a.href;if("mbg"===c.toLowerCase()||"makan bergizi"===c.toLowerCase())switch(a.toLowerCase()){case"dashboard":case"dashboard mbg":return"/mbg/dashboard-mbg"===i;case"kertas kerja":case"kertas kerja mbg":return"/mbg/kertas-kerja"===i;case"data":case"data mbg":return"/mbg/data-update"===i;default:let b=a.toLowerCase().replace(/\s+/g,"-").replace(/[\/]/g,"-");return i===`/mbg/${b}`}return i===`/${a.toLowerCase().replace(/\s+/g,"-")}`})(a);return(0,f.jsxs)("div",{onClick:()=>(a=>{if("string"==typeof a){let b;if("mbg"===c.toLowerCase()||"makan bergizi"===c.toLowerCase())switch(a.toLowerCase()){case"dashboard":case"dashboard mbg":b="/mbg/dashboard-mbg";break;case"kertas kerja":case"kertas kerja mbg":b="/mbg/kertas-kerja";break;case"data":case"data mbg":b="/mbg/data-update";break;default:let d=a.toLowerCase().replace(/\s+/g,"-").replace(/[\/]/g,"-");b=`/mbg/${d}`}else b=`/${a.toLowerCase().replace(/\s+/g,"-")}`;h.push(b)}else h.push(a.href);window.innerWidth<1280&&l()})(a),className:(0,ai.A)(e?"bg-primary-100 text-primary [&_svg]:text-primary [&_svg]:stroke-primary":"hover:text-primary hover:bg-default-100","w-full flex py-2 pl-8 pr-2 rounded-lg transition-colors cursor-pointer min-h-[36px] items-center gap-2 group"),children:[(0,f.jsx)("span",{className:(0,ai.A)(e?"text-primary":"text-default-400 group-hover:text-primary","transition-colors"),children:(a=>{if("mbg"!==c.toLowerCase()&&"makan bergizi"!==c.toLowerCase())return(0,f.jsx)(al.A,{size:16});switch(a.toLowerCase()){case"dashboard":case"dashboard mbg":return(0,f.jsx)(aj.A,{size:16});case"kertas kerja":case"kertas kerja mbg":return(0,f.jsx)(ak.A,{size:16});case"data":case"data mbg":return(0,f.jsx)(ad.A,{size:16});default:return(0,f.jsx)(al.A,{size:16})}})(d)}),(0,f.jsx)("span",{className:(0,ai.A)(e?"text-primary":"group-hover:text-primary","text-base transition-colors"),children:d})]},b)})})})})})},ax=({icon:a,title:b,isActive:c,href:d="",color:e="text-default-900",bgColor:g,hoverColor:h="hover:bg-default-100",activeColor:i,activeBgColor:j="bg-primary-100",className:k=""})=>{let{collapsed:l,setCollapsed:m}=y();return(0,f.jsx)(W(),{href:d,className:"text-default-900 active:bg-none max-w-full",children:(0,f.jsxs)("div",{className:(0,ai.A)((()=>{if(c){let a=i?`[&_svg]:stroke-${i.replace("text-","")}`:"[&_svg]:stroke-primary-500";return`${g||j} ${a}`}return h})(),"flex gap-2 w-full min-h-[44px] h-full items-center px-3.5 rounded-xl cursor-pointer transition-all duration-150 active:scale-[0.98]",k),onClick:()=>{window.innerWidth<1280&&m()},children:[a,(0,f.jsx)("span",{className:(0,ai.A)(e),children:b})]})})},ay=({title:a,children:b})=>(0,f.jsxs)("div",{className:"flex gap-2 flex-col",children:[(0,f.jsx)("span",{className:"text-xs font-normal ",children:a}),b]}),az=(0,z.tv)({base:"bg-background transition-transform h-full fixed -translate-x-full w-64 shrink-0 z-50 overflow-y-auto border-r border-divider flex-col py-6 px-3 xl:ml-0 xl:flex xl:static xl:h-screen xl:translate-x-0 [&::-webkit-scrollbar]:w-3 [&::-webkit-scrollbar-track]:bg-transparent [&::-webkit-scrollbar-thumb]:bg-default-300/30 [&::-webkit-scrollbar-thumb]:rounded-full hover:[&::-webkit-scrollbar-thumb]:bg-default-300/40 [&::-webkit-scrollbar-thumb]:transition-colors",variants:{collapsed:{true:"translate-x-0 ml-0 pt-20 [display:inherit]"}}}),aA=(0,z.tv)({base:"bg-[rgb(15_23_42/0.1)] fixed inset-0 z-40 opacity-40 transition-opacity xl:hidden xl:z-auto xl:opacity-100"}),aB=(0,z.tv)({base:"flex gap-8 items-center px-6"}),aC=Object.assign(az,{Header:aB,Body:(0,z.tv)({base:"flex flex-col gap-6 mt-9 px-2"}),Overlay:aA,Footer:(0,z.tv)({base:"flex items-center justify-center gap-6 pt-16 pb-8 px-8 xl:pt-10 xl:pb-0"})});var aD=c(16023);let aE=({icon:a,title:b,items:c,isActive:d,children:e})=>{let g=(0,o.useRouter)(),h=(0,o.usePathname)(),i=k().useMemo(()=>0===c.length?[]:"string"==typeof c[0]?c.map(a=>{let c,d;if("tkd"===b.toLowerCase()||"transfer ke daerah"===b.toLowerCase()){let b=a.toLowerCase().replace(/\s+/g,"-").replace(/[\/]/g,"-");switch(d=`/tkd/${b}`,a.toLowerCase()){case"dashboard":case"insentif fiskal":c=(0,f.jsx)(aj.A,{size:20});break;case"upload laporan":c=(0,f.jsx)(aD.A,{size:20});break;case"dau":default:c=(0,f.jsx)(al.A,{size:20});break;case"dbh":c=(0,f.jsx)(ak.A,{size:20});break;case"bok":c=(0,f.jsx)(ad.A,{size:20})}}else c=(0,f.jsx)(al.A,{size:20}),d=`/${a.toLowerCase().replace(/\s+/g,"-")}`;return{label:a,href:d,icon:c}}):c,[c,b]),j=k().useMemo(()=>i.some(a=>h===a.href),[i,h]);return(0,f.jsx)("div",{className:"relative",children:(0,f.jsxs)(G.A,{placement:"bottom-start",className:"min-w-[240px]",children:[(0,f.jsx)(H.b,{children:(0,f.jsxs)("div",{"data-testid":"hover-dropdown-button",className:(0,ai.A)(("boolean"==typeof d?d:j)?"bg-secondary-100 [&_svg]:stroke-secondary-500":"hover:bg-secondary-100","flex gap-2 w-full min-h-[44px] h-full items-center px-3.5 rounded-xl cursor-pointer transition-all duration-150 active:scale-[0.98]"),children:[a,(0,f.jsx)("span",{className:"text-default-900",children:b}),e]})}),(0,f.jsx)(I.y,{"aria-label":`${b} menu`,className:"w-full font-sans py-2",onAction:a=>{var b;let c=i[Number(a)];c&&(b=c.href,g.push(b))},children:i.map((a,b)=>{let c=h===a.href;return(0,f.jsx)(J.Y,{className:(0,ai.A)(c?"bg-secondary-100 [&_*]:text-secondary":"data-[hover=true]:bg-default-100","font-sans text-sm py-3 px-4 min-h-[44px] group"),children:(0,f.jsxs)("div",{className:"flex items-center gap-3",children:[(0,f.jsx)("span",{className:(0,ai.A)(c?"text-default-900 [&_svg]:stroke-secondary":"text-default-900 group-hover:text-secondary [&_svg]:group-hover:stroke-secondary","flex-shrink-0 transition-colors"),children:k().cloneElement(a.icon,{strokeWidth:2.5})}),(0,f.jsx)("span",{className:(0,ai.A)(c?"text-secondary font-medium":"text-default-900 group-hover:text-secondary font-medium","text-base transition-colors"),children:a.label})]})},b)})})]})})},aF=({icon:a,items:b,title:c,isActive:d=!1})=>{let[e,g]=(0,j.useState)(!1),h=(0,o.useRouter)(),i=(0,o.usePathname)(),{collapsed:k,setCollapsed:l}=y();return(0,f.jsx)("div",{className:"flex gap-4 h-full items-center cursor-pointer",children:(0,f.jsx)(an.D,{className:"px-0 w-full",children:(0,f.jsx)(ao.R,{indicator:(0,f.jsx)(ap,{}),classNames:{indicator:"data-[open=true]:-rotate-180",trigger:(0,ai.A)(d?"bg-primary-100 [&_svg]:stroke-primary-500":"hover:bg-default-100","py-0 min-h-[44px] rounded-xl active:scale-[0.98] transition-transform px-3.5"),title:"px-0 flex text-base gap-2 h-full items-center cursor-pointer",content:"px-0 pb-2"},"aria-label":"Accordion 1",title:(0,f.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,f.jsx)("span",{children:a}),(0,f.jsx)("span",{children:c})]}),children:(0,f.jsx)("div",{className:"space-y-1",children:b.map((a,b)=>{let d=(a=>"string"==typeof a?a:a.label)(a),e=(a=>{if("string"!=typeof a)return i===a.href;if("tkd"===c.toLowerCase()||"transfer ke daerah"===c.toLowerCase()){let b=a.toLowerCase().replace(/\s+/g,"-").replace(/[\/]/g,"-");return i===`/tkd/${b}`}return i===`/${a.toLowerCase().replace(/\s+/g,"-")}`})(a);return(0,f.jsxs)("div",{onClick:()=>(a=>{if("string"==typeof a){let b;if("tkd"===c.toLowerCase()||"transfer ke daerah"===c.toLowerCase()){let c=a.toLowerCase().replace(/\s+/g,"-").replace(/[\/]/g,"-");b=`/tkd/${c}`}else b=`/${a.toLowerCase().replace(/\s+/g,"-")}`;h.push(b)}else h.push(a.href);window.innerWidth<1280&&l()})(a),className:(0,ai.A)(e?"bg-primary-100 text-primary [&_svg]:text-primary":"hover:text-primary hover:bg-default-100","w-full flex py-2 pl-8 pr-2 rounded-xl transition-colors cursor-pointer min-h-[36px] items-center gap-2 group"),children:[(0,f.jsx)("span",{className:(0,ai.A)(e?"text-primary":"text-default-400 group-hover:text-primary","transition-colors"),children:(a=>{if("tkd"!==c.toLowerCase()&&"transfer ke daerah"!==c.toLowerCase())return(0,f.jsx)(al.A,{size:16});switch(a.toLowerCase()){case"dashboard":case"insentif fiskal":return(0,f.jsx)(aj.A,{size:16});case"upload laporan":return(0,f.jsx)(aD.A,{size:16});case"dau":default:return(0,f.jsx)(al.A,{size:16});case"dbh":return(0,f.jsx)(ak.A,{size:16});case"bok":return(0,f.jsx)(ad.A,{size:16})}})(d)}),(0,f.jsx)("span",{className:(0,ai.A)(e?"text-primary":"group-hover:text-primary","text-base transition-colors"),children:d})]},b)})})})})})},aG=()=>{let a=(0,o.usePathname)(),{collapsed:b,setCollapsed:c}=y();return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsxs)("aside",{className:"xl:hidden h-screen z-[20] sticky top-0",children:[b?(0,f.jsx)("div",{className:aC.Overlay(),onClick:c}):null,(0,f.jsx)("div",{className:aC({collapsed:b}),children:(0,f.jsxs)("div",{className:"flex flex-col justify-between h-full",children:[(0,f.jsxs)("div",{className:aC.Body(),children:[(0,f.jsx)(ax,{title:"Dashboard",icon:(0,f.jsx)(_.A,{className:"text-primary"}),isActive:"/"===a,href:"/"}),(0,f.jsxs)(ay,{title:"Menu Utama",children:[(0,f.jsx)(aw,{icon:(0,f.jsx)(aa.A,{className:"text-amber-700"}),items:["Dashboard","Kertas Kerja","Data"],title:"Makan Bergizi",isActive:a.startsWith("/mbg")}),(0,f.jsx)(ax,{isActive:"/profilkl"===a,title:"Profil K/L",icon:(0,f.jsx)(ab.A,{className:"text-warning"}),href:"/profilkl"}),(0,f.jsx)(aq,{icon:(0,f.jsx)(ac.A,{className:"text-success"}),items:["Summary","Analisa"],title:"EPA",isActive:a.startsWith("/epa")}),(0,f.jsx)(au,{icon:(0,f.jsx)(ad.A,{className:"text-secondary"}),items:["Belanja","Tematik","Kontrak","UP/TUP","Bansos","Deviasi","RKAKL Detail"],title:"Inquiry Data",isActive:a.startsWith("/inquiry-data")}),(0,f.jsx)(ax,{isActive:"/test-belanja"===a,title:"Spending Review",icon:(0,f.jsx)(ae.A,{}),href:"/test-belanja"}),(0,f.jsx)(aF,{icon:(0,f.jsx)(ad.A,{className:"text-secondary",strokeWidth:2.5,size:26}),items:["DAU","Upload Laporan"],title:"TKD",isActive:a.startsWith("/tkd")})]}),(0,f.jsx)(ay,{title:"Lainnya",children:(0,f.jsx)(ax,{isActive:"/tentang-kami"===a,title:"Tentang Kami",icon:(0,f.jsx)(af.A,{}),href:"/tentang-kami"})})]}),(0,f.jsx)("div",{className:aC.Footer(),children:(0,f.jsx)($.I,{content:"Settings",color:"primary",children:(0,f.jsx)("div",{className:"max-w-fit",children:(0,f.jsx)(ag.A,{})})})})]})})]}),(0,f.jsx)("nav",{className:"hidden xl:block w-full z-[50] bg-white dark:bg-black border-b border-gray-200 dark:border-gray-700 shadow-md fixed top-16",children:(0,f.jsx)("div",{className:"px-6 pt-1 pb-3",children:(0,f.jsxs)("div",{className:"flex items-center justify-center font-medium",children:[(0,f.jsxs)("div",{className:"flex items-center space-x-4 overflow-x-auto overflow-y-visible",children:[(0,f.jsx)(ax,{title:"Dashboard",icon:(0,f.jsx)(_.A,{className:"text-primary",strokeWidth:2.5,size:25}),isActive:"/"===a,href:"/"}),(0,f.jsx)(av,{icon:(0,f.jsx)(aa.A,{className:"text-amber-700",strokeWidth:2.5,size:25}),items:["Dashboard","Kertas Kerja","Data"],title:"Makan Bergizi",isActive:a.startsWith("/mbg"),children:(0,f.jsx)(ah.A,{size:20,strokeWidth:2.5})}),(0,f.jsx)(ax,{isActive:"/profilkl"===a,title:"Profil K/L",icon:(0,f.jsx)(ab.A,{className:"text-warning",strokeWidth:2.5,size:25}),href:"/profilkl",bgColor:"bg-warning-100",color:"text-default-900",activeBgColor:"bg-warning-100",activeColor:"text-warning",hoverColor:"hover:bg-warning-100"}),(0,f.jsx)(am,{icon:(0,f.jsx)(ac.A,{className:"text-success",strokeWidth:2.5,size:26}),items:["Summary","Analisa"],title:"EPA",isActive:a.startsWith("/epa"),children:(0,f.jsx)(ah.A,{size:20,strokeWidth:2.5})}),(0,f.jsx)(ax,{isActive:"/test-belanja"===a,title:"Spending Review",icon:(0,f.jsx)(ae.A,{className:"text-teal-500",strokeWidth:2.5,size:25}),href:"/test-belanja",color:"text-default-900",activeBgColor:"bg-teal-100",activeColor:"text-teal-500",hoverColor:"hover:bg-teal-100"}),(0,f.jsx)(aE,{icon:(0,f.jsx)(ad.A,{className:"text-secondary",strokeWidth:2.5,size:26}),items:["DAU 2024","DAU 2025","Upload Laporan"],title:"TKD",isActive:a.startsWith("/tkd"),children:(0,f.jsx)(ah.A,{size:20,strokeWidth:2.5})}),(0,f.jsx)(at,{icon:(0,f.jsx)(ad.A,{className:"text-secondary",strokeWidth:2.5,size:26}),items:["Belanja","Tematik","Kontrak","UP/TUP","Bansos","Deviasi","RKAKL Detail"],title:"Inquiry Data",isActive:a.startsWith("/inquiry-data"),children:(0,f.jsx)(ah.A,{size:20,strokeWidth:2.5})}),(0,f.jsx)(ax,{isActive:"/tentang-kami"===a,title:"Tentang Kami",icon:(0,f.jsx)(af.A,{className:"text-default-500",strokeWidth:2.5,size:25}),href:"/tentang-kami",color:"text-default-900",activeBgColor:"bg-default-200",activeColor:"text-default-500",hoverColor:"hover:bg-default-200"})]}),(0,f.jsx)("div",{className:"flex items-center space-x-3"})]})})})]})},aH=({children:a})=>{let[b,c]=k().useState(!1),[d,e]=function(a=!1){let[b,c]=(0,j.useState)(a);return[b,c]}(!1);return(0,f.jsx)(x.Provider,{value:{collapsed:b,setCollapsed:()=>{c(!b),e(!b)}},children:(0,f.jsxs)("div",{className:"flex",children:[(0,f.jsx)("div",{className:"xl:hidden",children:(0,f.jsx)(aG,{})}),(0,f.jsx)("div",{className:"flex-1",children:(0,f.jsxs)(Z,{children:[(0,f.jsx)("div",{className:"hidden xl:block",children:(0,f.jsx)(aG,{})}),(0,f.jsx)("div",{className:"xl:pt-16",children:a})]})})]})})}},60131:(a,b,c)=>{Promise.resolve().then(c.bind(c,57347))},65647:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},66799:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\sintesaNEXT2\\\\src\\\\components\\\\features\\\\dashboard\\\\BackendStatusMonitor.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\components\\features\\dashboard\\BackendStatusMonitor.tsx","default")},67492:(a,b,c)=>{"use strict";c.d(b,{A:()=>h});var d,e,f=c(43210);function g(){return(g=Object.assign?Object.assign.bind():function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)({}).hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a}).apply(null,arguments)}let h=function(a){return f.createElement("svg",g({xmlns:"http://www.w3.org/2000/svg",id:"snext_dark_svg__Layer_2","data-name":"Layer 2",viewBox:"0 0 3399.57 600"},a),d||(d=f.createElement("defs",null,f.createElement("style",null,".snext_dark_svg__cls-1{fill:#0f52ba}"))),e||(e=f.createElement("g",{id:"snext_dark_svg__Layer_1-2","data-name":"Layer 1"},f.createElement("path",{d:"M525 150c-41.46 0-75-33.54-75-75S416.42 0 375 0H75C33.58 0 0 33.58 0 75v300c0 41.42 33.58 75 75 75s75 33.54 75 75 33.58 75 75 75h300c41.42 0 75-33.58 75-75V225c0-41.42-33.58-75-75-75m-75 37.5V375c0 41.42-33.58 75-75 75H112.5c20.71 0 37.5-16.79 37.5-37.5V225c0-41.42 33.58-75 75-75h262.5c-20.71 0-37.5 16.79-37.5 37.5M1031.72 600c-27.04 0-51.91-4.06-74.62-12.17s-42.18-19.59-58.4-34.47c-14.95-13.7-26.7-29.59-35.21-47.67a4.987 4.987 0 0 1 2.5-6.68L935.94 468c2.32-1.03 5.02-.14 6.31 2.03 8.03 13.45 19.33 24.88 33.91 34.26 15.94 10.28 33.66 15.41 53.12 15.41 21.09 0 37.98-3.65 50.69-10.95 12.7-7.3 19.06-17.44 19.06-30.42s-4.87-22.03-14.6-28.79-23.52-12.29-41.36-16.63l-38.12-10.54c-38.4-9.73-68.41-25.27-90.03-46.64-21.63-21.35-32.44-45.83-32.44-73.4 0-40.55 13.1-71.91 39.34-94.08 26.22-22.17 63.94-33.25 113.14-33.25 24.86 0 47.71 3.65 68.54 10.95 20.81 7.3 38.79 17.58 53.94 30.82 13.87 12.15 23.88 26.21 30.04 42.22.95 2.46-.22 5.24-2.62 6.32l-66.69 30.12c-2.44 1.1-5.3.07-6.51-2.32-5.77-11.41-15.39-20.36-28.85-26.86-15.14-7.3-31.91-10.95-50.29-10.95s-32.72 3.93-42.99 11.76c-10.28 7.84-15.41 18.79-15.41 32.85 0 8.11 4.59 15.55 13.79 22.3 9.19 6.77 22.43 12.31 39.74 16.63l47.85 11.36q39.735 9.735 64.89 30.42c16.75 13.79 29.2 29.2 37.31 46.23s12.17 34.2 12.17 51.5c0 24.33-6.89 45.7-20.68 64.07-13.79 18.39-32.58 32.58-56.37 42.58-23.8 10-50.83 15-81.11 15ZM1313.84 108.26c-14.88 0-27.69-5.37-38.43-16.12-10.74-10.74-16.12-23.55-16.12-38.43s5.37-27.54 16.12-38.02c10.74-10.46 23.55-15.7 38.43-15.7s27.69 5.24 38.43 15.7c10.74 10.47 16.12 23.14 16.12 38.02s-5.37 27.69-16.12 38.43c-10.74 10.74-23.55 16.12-38.43 16.12m-46.28 486.66V191.86a5.08 5.08 0 0 1 5.08-5.08h81.57a5.08 5.08 0 0 1 5.08 5.08v403.06a5.08 5.08 0 0 1-5.08 5.08h-81.57a5.08 5.08 0 0 1-5.08-5.08M1462.03 594.9V190.07c0-2.82 2.29-5.1 5.1-5.1h77.3c2.68 0 4.9 2.07 5.09 4.75l2.77 39.27c.33 4.74 6.39 6.48 9.21 2.65 11.96-16.25 26.53-29.18 43.72-38.78 21.3-11.89 45.78-17.85 73.46-17.85q49.8 0 85.08 19.92c23.51 13.28 41.5 33.9 53.96 61.84 12.45 27.95 18.68 64.33 18.68 109.16v228.98c0 2.82-2.29 5.1-5.1 5.1h-82.76c-2.82 0-5.1-2.29-5.1-5.1V387.52c0-33.2-3.73-58.79-11.21-76.78-7.47-17.98-17.99-30.57-31.54-37.77-13.57-7.19-29.47-10.79-47.73-10.79-31-.54-55.07 9.69-72.22 30.71-17.16 21.04-25.73 51.19-25.73 90.48v211.55c0 2.82-2.29 5.1-5.1 5.1h-82.76c-2.82 0-5.1-2.29-5.1-5.1ZM2086.2 600c-42.82 0-75.48-10.7-97.97-32.11-22.5-21.41-33.74-52.16-33.74-92.28V266.79c0-2.76-2.24-5-5-5h-57.48c-2.76 0-5-2.24-5-5v-68.05c0-2.76 2.24-5 5-5h57.48c2.76 0 5-2.24 5-5V61.91c0-2.76 2.24-5 5-5h80.24c2.76 0 5 2.24 5 5v116.83c0 2.76 2.24 5 5 5h99.76c2.76 0 5 2.24 5 5v68.05c0 2.76-2.24 5-5 5h-99.76c-2.76 0-5 2.24-5 5v191.75c0 18.98 4.47 33.33 13.41 43.09s22.08 14.63 39.43 14.63c5.41 0 11.38-1.08 17.89-3.25 5.21-1.73 10.95-4.69 17.21-8.86 2.53-1.69 5.98-.78 7.3 1.97l28.9 59.92c1.09 2.26.33 4.97-1.76 6.36-12.89 8.56-25.76 15.19-38.64 19.89-14.1 5.14-28.19 7.72-42.28 7.72ZM2420.33 600c-39.48 0-74.49-9.06-105.03-27.17-30.55-18.11-54.62-43.11-72.19-75.02-17.58-31.9-26.36-68.66-26.36-110.31s8.92-78.4 26.76-110.31c17.84-31.9 42.3-56.9 73.4-75.02C2348 184.06 2383.54 175 2423.57 175c36.22 0 68.94 9.33 98.14 27.98s52.31 45.56 69.35 80.7c16.44 33.93 24.94 74.13 25.52 120.64.03 2.79-2.2 5.07-4.99 5.07h-295.26c-2.97 0-5.3 2.58-4.96 5.53 3.57 31.44 15.97 56.37 37.18 74.77 22.43 19.47 49.07 29.2 79.89 29.2 24.86 0 45.42-5.54 61.64-16.63 15.01-10.25 27.01-23.41 36.01-39.46a4.98 4.98 0 0 1 6.29-2.16l71.06 30.15c2.74 1.16 3.86 4.44 2.41 7.04-10.93 19.61-24.52 36.9-40.75 51.88-17.58 16.22-38.4 28.67-62.45 37.31-24.07 8.64-51.5 12.98-82.32 12.98m-98.11-260.35h190.7c2.99 0 5.33-2.61 4.96-5.57-2.18-16.98-7.66-31.21-16.45-42.69-9.73-12.7-21.63-22.3-35.69-28.79-14.07-6.49-28.93-9.73-44.61-9.73s-30.42 3.12-45.83 9.33c-15.41 6.22-28.53 15.82-39.34 28.79-9.73 11.67-15.96 25.96-18.68 42.87a4.99 4.99 0 0 0 4.93 5.79ZM2834.96 600c-27.04 0-51.91-4.06-74.62-12.17s-42.18-19.59-58.4-34.47c-14.95-13.7-26.7-29.59-35.21-47.67a4.987 4.987 0 0 1 2.5-6.68l69.95-31.01c2.32-1.03 5.02-.14 6.31 2.03 8.03 13.45 19.33 24.88 33.91 34.26 15.94 10.28 33.66 15.41 53.12 15.41 21.09 0 37.98-3.65 50.69-10.95 12.7-7.3 19.06-17.44 19.06-30.42s-4.87-22.03-14.6-28.79-23.52-12.29-41.36-16.63l-38.12-10.54c-38.4-9.73-68.41-25.27-90.03-46.64-21.63-21.35-32.44-45.83-32.44-73.4 0-40.55 13.1-71.91 39.34-94.08C2751.28 186.08 2789 175 2838.2 175c24.86 0 47.71 3.65 68.53 10.95 20.81 7.3 38.79 17.58 53.94 30.82 13.87 12.15 23.88 26.21 30.04 42.22.95 2.46-.22 5.24-2.62 6.32l-66.69 30.12c-2.44 1.1-5.3.07-6.51-2.32-5.77-11.41-15.39-20.36-28.85-26.86-15.14-7.3-31.91-10.95-50.29-10.95s-32.72 3.93-42.99 11.76c-10.28 7.84-15.41 18.79-15.41 32.85 0 8.11 4.59 15.55 13.79 22.3 9.19 6.77 22.43 12.31 39.74 16.63l47.85 11.36q39.735 9.735 64.89 30.42c16.75 13.79 29.2 29.2 37.31 46.23s12.17 34.2 12.17 51.5c0 24.33-6.89 45.7-20.68 64.07-13.79 18.39-32.58 32.58-56.37 42.58-23.8 10-50.83 15-81.11 15ZM3196.81 600c-48.66 0-86.38-10.81-113.14-32.44-26.76-21.62-40.15-52.17-40.15-91.65 0-42.18 14.19-74.34 42.58-96.52 28.39-22.16 67.99-33.25 118.82-33.25h97.37c3.01 0 5.35-2.66 4.95-5.64-3.65-26.98-11.81-47.81-24.45-62.49-13.52-15.68-33.53-23.52-60.02-23.52-19.47 0-36.5 4.06-51.1 12.17-13.62 7.57-25.37 19.14-35.22 34.71a4.97 4.97 0 0 1-5.92 2.01l-70.4-25.86c-2.82-1.04-4.1-4.32-2.71-6.99 8.31-15.94 18.96-30.88 31.93-44.82 14.32-15.41 32.44-27.7 54.34-36.9 21.9-9.19 48.26-13.79 79.08-13.79 39.46 0 72.45 7.71 98.95 23.12 26.49 15.41 46.09 37.45 58.8 66.1 12.7 28.67 19.06 63.26 19.06 103.82l-2.38 217.3a4.985 4.985 0 0 1-4.99 4.93h-73.86c-2.66 0-4.85-2.09-4.98-4.74l-1.45-29.5c-.23-4.66-6.15-6.47-8.96-2.75-9.8 12.99-22.07 23.43-36.8 31.31-19.2 10.27-42.31 15.41-69.35 15.41Zm11.35-76.24q30 0 53.13-13.38c23.13-13.38 27.3-20.95 35.69-36.09 8.38-15.13 12.57-31.9 12.57-50.29v-3.12c0-2.75-2.23-4.99-4.99-4.99h-70.44c-36.23 0-61.64 5.01-76.24 15-14.6 10.01-21.9 24.21-21.9 42.58 0 15.69 6.35 27.98 19.06 36.9 12.7 8.92 30.42 13.38 53.12 13.38Z",className:"snext_dark_svg__cls-1"}))))}},68529:(a,b,c)=>{"use strict";c.d(b,{ToastContainer:()=>m});var d=c(60687),e=c(40611),f=c(49384),g=c(5336),h=c(35071),i=c(43649),j=c(96882),k=c(11860);let l=({toast:a})=>{let{removeToast:b}=(0,e.d)(),c=()=>{switch(a.type){case"success":return"text-green-800 dark:text-green-200";case"error":return"text-red-800 dark:text-red-200";case"warning":return"text-yellow-800 dark:text-yellow-200";case"info":return"text-blue-800 dark:text-blue-200";default:return"text-gray-800 dark:text-gray-200"}};return(0,d.jsxs)("div",{className:(0,f.$)("flex items-start gap-3 p-4 rounded-lg border shadow-lg max-w-md transition-all duration-300 ease-in-out transform",(()=>{switch(a.type){case"success":return"bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800";case"error":return"bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800";case"warning":return"bg-yellow-50 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800";case"info":return"bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800";default:return"bg-gray-50 border-gray-200 dark:bg-gray-950 dark:border-gray-800"}})(),"animate-in slide-in-from-right-full"),role:"alert",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(()=>{switch(a.type){case"success":return(0,d.jsx)(g.A,{className:"w-5 h-5 text-green-600"});case"error":return(0,d.jsx)(h.A,{className:"w-5 h-5 text-red-600"});case"warning":return(0,d.jsx)(i.A,{className:"w-5 h-5 text-yellow-600"});default:return(0,d.jsx)(j.A,{className:"w-5 h-5 text-blue-600"})}})()}),(0,d.jsx)("div",{className:(0,f.$)("flex-1 text-sm font-medium",c()),children:a.message}),(0,d.jsx)("button",{onClick:()=>b(a.id),className:(0,f.$)("flex-shrink-0 p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors",c()),"aria-label":"Close notification",children:(0,d.jsx)(k.A,{className:"w-4 h-4"})})]})},m=()=>{let{toasts:a}=(0,e.d)();return 0===a.length?null:(0,d.jsx)("div",{className:"fixed bottom-4 right-4 z-50 space-y-2",children:a.map(a=>(0,d.jsx)(l,{toast:a},a.id))})}},73895:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},78571:(a,b,c)=>{"use strict";c.d(b,{ToastContainer:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ToastContainer() from the server but ToastContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\components\\ui\\feedback\\ToastContainer.tsx","ToastContainer")},81142:(a,b,c)=>{Promise.resolve().then(c.bind(c,21741)),Promise.resolve().then(c.bind(c,51714)),Promise.resolve().then(c.bind(c,68529))},84107:(a,b,c)=>{Promise.resolve().then(c.bind(c,54413))},84819:(a,b,c)=>{"use strict";c.d(b,{E:()=>g,p:()=>h});var d=c(60687),e=c(43210);let f=(0,e.createContext)(void 0),g=()=>{let a=(0,e.useContext)(f);if(void 0===a)throw Error("useNotifications must be used within a NotificationProvider");return a},h=({children:a})=>{let[b,c]=(0,e.useState)([{id:1,header:"New Message",message:"New message from John",read:!1},{id:2,header:"Comment Activity",message:"New comment on your post",read:!1},{id:3,header:"System Update",message:"System update available",read:!0}]),g=b.filter(a=>!a.read).length;return(0,d.jsx)(f.Provider,{value:{notifications:b,unreadCount:g,markAsRead:a=>{c(b=>b.map(b=>b.id===a?{...b,read:!0}:b))},addNotification:a=>{let d=Math.max(...b.map(a=>a.id),0)+1;c(b=>[{...a,id:d},...b])}},children:a})}},94830:(a,b,c)=>{"use strict";c.r(b),c.d(b,{"7f53c4aead54f45b8422e5a6e0171b433cf54366e4":()=>h,"7f752043a54f8f73684b4ad1c89d25e1004858fbe7":()=>g,"7fc112607136060becbdc52ca516396f1a08f665e1":()=>i});var d=c(91199);c(42087);var e=c(74208),f=c(33331);let g=async(a,b)=>{let c=await (0,e.UL)();c.set("userAuth","myToken",{secure:!0,httpOnly:!1,sameSite:"lax"}),c.set(a,b,{secure:!0,httpOnly:!1,sameSite:"lax"})},h=async()=>{let a=await (0,e.UL)();a.delete("userAuth"),a.delete("token"),a.delete("nextToken")},i=async()=>{let a=await (0,e.UL)();return{userAuth:a.has("userAuth"),token:a.has("token"),accessToken:a.has("accessToken"),nextToken:a.has("nextToken")}};(0,f.D)([g,h,i]),(0,d.A)(g,"7f752043a54f8f73684b4ad1c89d25e1004858fbe7",null),(0,d.A)(h,"7f53c4aead54f45b8422e5a6e0171b433cf54366e4",null),(0,d.A)(i,"7fc112607136060becbdc52ca516396f1a08f665e1",null)}};