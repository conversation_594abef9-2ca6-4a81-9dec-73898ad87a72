-- Sintesa Backend Database Schema
-- MySQL 8.0+ compatible

-- Create database (run this separately if needed)
-- CREATE DATABASE sintesa_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- <PERSON><PERSON> sintesa_db;

-- Users table for authentication and user management
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user', 'viewer') NOT NULL DEFAULT 'user',
    department VARCHAR(100),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    preferences JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_department (department),
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User sessions table for session management
CREATE TABLE IF NOT EXISTS user_sessions (
    session_id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL,
    refresh_token VARCHAR(512) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_refresh_token (refresh_token),
    INDEX idx_is_active (is_active),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Query logs table for tracking SQL query execution
CREATE TABLE IF NOT EXISTS query_logs (
    query_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    sql_query TEXT NOT NULL,
    status ENUM('success', 'error', 'timeout') NOT NULL,
    execution_time DECIMAL(10,3),
    rows_affected INT,
    error_message TEXT,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_execution_time (execution_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Login attempts table for security monitoring
CREATE TABLE IF NOT EXISTS login_attempts (
    attempt_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50),
    ip_address VARCHAR(45) NOT NULL,
    success BOOLEAN NOT NULL,
    error_message VARCHAR(255),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_ip_address (ip_address),
    INDEX idx_success (success),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ADK (Anggaran Daerah Kabupaten) data table for budget management
CREATE TABLE IF NOT EXISTS adk_data (
    adk_id INT AUTO_INCREMENT PRIMARY KEY,
    tahun YEAR NOT NULL,
    kode_skpd VARCHAR(20) NOT NULL,
    nama_skpd VARCHAR(255) NOT NULL,
    kode_program VARCHAR(20) NOT NULL,
    nama_program VARCHAR(255) NOT NULL,
    kode_kegiatan VARCHAR(20) NOT NULL,
    nama_kegiatan VARCHAR(255) NOT NULL,
    kode_rekening VARCHAR(20),
    nama_rekening VARCHAR(255),
    pagu_anggaran DECIMAL(15,2) NOT NULL DEFAULT 0,
    realisasi_anggaran DECIMAL(15,2) NOT NULL DEFAULT 0,
    sisa_anggaran DECIMAL(15,2) GENERATED ALWAYS AS (pagu_anggaran - realisasi_anggaran) STORED,
    persentase_realisasi DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN pagu_anggaran > 0 THEN (realisasi_anggaran / pagu_anggaran * 100)
            ELSE 0 
        END
    ) STORED,
    tanggal_realisasi DATE,
    keterangan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_tahun (tahun),
    INDEX idx_kode_skpd (kode_skpd),
    INDEX idx_kode_program (kode_program),
    INDEX idx_kode_kegiatan (kode_kegiatan),
    INDEX idx_kode_rekening (kode_rekening),
    INDEX idx_pagu_anggaran (pagu_anggaran),
    INDEX idx_realisasi_anggaran (realisasi_anggaran),
    INDEX idx_persentase_realisasi (persentase_realisasi),
    INDEX idx_tanggal_realisasi (tanggal_realisasi),
    INDEX idx_created_at (created_at),
    INDEX idx_composite_skpd_tahun (kode_skpd, tahun),
    INDEX idx_composite_program_tahun (kode_program, tahun)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Reference data categories table
CREATE TABLE IF NOT EXISTS referensi_categories (
    category_id INT AUTO_INCREMENT PRIMARY KEY,
    category_code VARCHAR(50) NOT NULL UNIQUE,
    category_name VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category_code (category_code),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Reference data items table
CREATE TABLE IF NOT EXISTS referensi_data (
    item_id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL,
    item_code VARCHAR(50) NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    item_value VARCHAR(500),
    description TEXT,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (category_id) REFERENCES referensi_categories(category_id) ON DELETE CASCADE,
    UNIQUE KEY unique_category_code (category_id, item_code),
    INDEX idx_category_id (category_id),
    INDEX idx_item_code (item_code),
    INDEX idx_item_name (item_name),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- File uploads table for tracking uploaded files
CREATE TABLE IF NOT EXISTS file_uploads (
    file_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    stored_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64),
    upload_purpose VARCHAR(100),
    is_processed BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_stored_name (stored_name),
    INDEX idx_file_hash (file_hash),
    INDEX idx_upload_purpose (upload_purpose),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System settings table for application configuration
CREATE TABLE IF NOT EXISTS system_settings (
    setting_id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') NOT NULL DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_setting_key (setting_key),
    INDEX idx_is_public (is_public)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Notifications table for user notifications
CREATE TABLE IF NOT EXISTS notifications (
    notification_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') NOT NULL DEFAULT 'info',
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    action_url VARCHAR(500),
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default admin user (password: admin123)
-- Note: Change this password immediately after first login
INSERT IGNORE INTO users (
    username, 
    email, 
    password_hash, 
    full_name, 
    role, 
    department
) VALUES (
    'admin',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj6hsxq9w5KS', -- admin123
    'System Administrator',
    'admin',
    'IT Department'
);

-- Insert default reference categories
INSERT IGNORE INTO referensi_categories (category_code, category_name, description) VALUES
('skpd', 'Satuan Kerja Perangkat Daerah', 'Daftar SKPD di lingkungan pemerintah daerah'),
('program', 'Program Kegiatan', 'Daftar program dan kegiatan pemerintah daerah'),
('rekening', 'Kode Rekening', 'Daftar kode rekening anggaran'),
('status', 'Status Data', 'Status umum untuk berbagai keperluan'),
('jenis_belanja', 'Jenis Belanja', 'Klasifikasi jenis belanja anggaran');

-- Insert sample reference data
INSERT IGNORE INTO referensi_data (category_id, item_code, item_name, item_value, sort_order) VALUES
((SELECT category_id FROM referensi_categories WHERE category_code = 'status'), 'active', 'Aktif', '1', 1),
((SELECT category_id FROM referensi_categories WHERE category_code = 'status'), 'inactive', 'Tidak Aktif', '0', 2),
((SELECT category_id FROM referensi_categories WHERE category_code = 'status'), 'pending', 'Menunggu', 'pending', 3),
((SELECT category_id FROM referensi_categories WHERE category_code = 'jenis_belanja'), 'operasional', 'Belanja Operasional', 'operasional', 1),
((SELECT category_id FROM referensi_categories WHERE category_code = 'jenis_belanja'), 'modal', 'Belanja Modal', 'modal', 2),
((SELECT category_id FROM referensi_categories WHERE category_code = 'jenis_belanja'), 'transfer', 'Belanja Transfer', 'transfer', 3);

-- Insert default system settings
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('app_name', 'Sintesa Backend', 'string', 'Application name', true),
('app_version', '1.0.0', 'string', 'Application version', true),
('maintenance_mode', 'false', 'boolean', 'Maintenance mode status', false),
('max_query_timeout', '30000', 'number', 'Maximum query timeout in milliseconds', false),
('max_file_size', '10485760', 'number', 'Maximum file upload size in bytes', false),
('default_page_size', '20', 'number', 'Default pagination page size', true),
('session_timeout', '86400', 'number', 'Session timeout in seconds', false);

-- Create views for commonly used data

-- View for user summary (excluding sensitive data)
CREATE OR REPLACE VIEW user_summary AS
SELECT 
    user_id,
    username,
    email,
    full_name,
    role,
    department,
    is_active,
    last_login,
    created_at
FROM users;

-- View for ADK summary by SKPD
CREATE OR REPLACE VIEW adk_summary_skpd AS
SELECT 
    tahun,
    kode_skpd,
    nama_skpd,
    COUNT(*) as total_kegiatan,
    SUM(pagu_anggaran) as total_pagu,
    SUM(realisasi_anggaran) as total_realisasi,
    SUM(sisa_anggaran) as total_sisa,
    AVG(persentase_realisasi) as rata_rata_realisasi
FROM adk_data
GROUP BY tahun, kode_skpd, nama_skpd;

-- View for ADK summary by program
CREATE OR REPLACE VIEW adk_summary_program AS
SELECT 
    tahun,
    kode_program,
    nama_program,
    COUNT(*) as total_kegiatan,
    COUNT(DISTINCT kode_skpd) as total_skpd,
    SUM(pagu_anggaran) as total_pagu,
    SUM(realisasi_anggaran) as total_realisasi,
    SUM(sisa_anggaran) as total_sisa,
    AVG(persentase_realisasi) as rata_rata_realisasi
FROM adk_data
GROUP BY tahun, kode_program, nama_program;

-- Create stored procedures for common operations

DELIMITER //

-- Procedure to clean up expired sessions
CREATE PROCEDURE CleanupExpiredSessions()
BEGIN
    DELETE FROM user_sessions 
    WHERE expires_at < NOW() OR is_active = FALSE;
    
    SELECT ROW_COUNT() as deleted_sessions;
END //

-- Procedure to get user statistics
CREATE PROCEDURE GetUserStatistics()
BEGIN
    SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN is_active = TRUE THEN 1 END) as active_users,
        COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as active_24h,
        COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_7d,
        COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as active_30d,
        COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_users,
        COUNT(CASE WHEN role = 'user' THEN 1 END) as regular_users,
        COUNT(CASE WHEN role = 'viewer' THEN 1 END) as viewer_users
    FROM users;
END //

-- Procedure to get query statistics
CREATE PROCEDURE GetQueryStatistics(IN days INT)
BEGIN
    SELECT 
        COUNT(*) as total_queries,
        COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_queries,
        COUNT(CASE WHEN status = 'error' THEN 1 END) as failed_queries,
        COUNT(CASE WHEN status = 'timeout' THEN 1 END) as timeout_queries,
        AVG(execution_time) as avg_execution_time,
        MAX(execution_time) as max_execution_time,
        MIN(execution_time) as min_execution_time,
        COUNT(DISTINCT user_id) as unique_users
    FROM query_logs 
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL days DAY);
END //

DELIMITER ;

-- Create events for automatic cleanup (requires event scheduler to be enabled)
-- SET GLOBAL event_scheduler = ON;

/*
CREATE EVENT IF NOT EXISTS cleanup_expired_sessions
ON SCHEDULE EVERY 1 HOUR
DO
  CALL CleanupExpiredSessions();

CREATE EVENT IF NOT EXISTS cleanup_old_logs
ON SCHEDULE EVERY 1 DAY
DO
  DELETE FROM query_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

CREATE EVENT IF NOT EXISTS cleanup_old_login_attempts
ON SCHEDULE EVERY 1 DAY
DO
  DELETE FROM login_attempts WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
*/

-- Grant permissions (adjust as needed)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON sintesa_db.* TO 'sintesa_user'@'localhost';
-- GRANT EXECUTE ON sintesa_db.* TO 'sintesa_user'@'localhost';
-- FLUSH PRIVILEGES;

-- Show table information
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE()
ORDER BY TABLE_NAME;

-- Show indexes
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE()
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;