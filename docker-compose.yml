version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: sintesa-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-pdpsipa2025}
      MYSQL_DATABASE: ${DB_NAME:-sintesa}
      MYSQL_USER: ${DB_USER:-root}
      MYSQL_PASSWORD: ${DB_PASSWORD:-pdpsipa2025}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    ports:
      - "${DB_PORT:-3399}:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/seed.sql:/docker-entrypoint-initdb.d/02-seed.sql:ro
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=256M
      --max-connections=200
      --query-cache-type=1
      --query-cache-size=64M
    networks:
      - sintesa-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: sintesa-redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - sintesa-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5

  # Sintesa Backend Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: sintesa-backend
    restart: unless-stopped
    ports:
      - "${PORT:-3001}:3001"
    environment:
      NODE_ENV: ${NODE_ENV:-production}
      PORT: 3001
      HOST: 0.0.0.0
      
      # Database Configuration
      DB_HOST: mysql
      DB_PORT: 3306
      DB_USER: ${DB_USER:-sintesa_user}
      DB_PASSWORD: ${DB_PASSWORD:-sintesa_pass_2024}
      DB_NAME: ${DB_NAME:-sintesa}
      
      # Redis Configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      
      # JWT Configuration
      JWT_SECRET: ${JWT_SECRET:-sintesa-jwt-secret-key-2024-production}
      JWT_EXPIRES_IN: ${JWT_EXPIRES_IN:-24h}
      JWT_REFRESH_EXPIRES_IN: ${JWT_REFRESH_EXPIRES_IN:-7d}
      
      # Encryption
      ENCRYPTION_KEY: ${ENCRYPTION_KEY:-sintesa-encryption-key-2024-production}
      
      # CORS
      CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:3000,https://sintesa.example.com}
      
      # Rate Limiting
      RATE_LIMIT_MAX: ${RATE_LIMIT_MAX:-100}
      RATE_LIMIT_WINDOW: ${RATE_LIMIT_WINDOW:-60000}
      
      # File Upload
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-10485760}
      UPLOAD_PATH: ${UPLOAD_PATH:-./uploads}
      
      # Logging
      LOG_LEVEL: ${LOG_LEVEL:-info}
      
      # Session
      SESSION_TIMEOUT: ${SESSION_TIMEOUT:-86400}
      
      # Query Settings
      QUERY_TIMEOUT: ${QUERY_TIMEOUT:-30000}
      MAX_QUERY_RESULTS: ${MAX_QUERY_RESULTS:-10000}
      
      # Socket.IO
      SOCKET_CORS_ORIGINS: ${SOCKET_CORS_ORIGINS:-http://localhost:3000}
      
      # Security
      TRUST_PROXY: ${TRUST_PROXY:-true}
      COOKIE_SECRET: ${COOKIE_SECRET:-sintesa-cookie-secret-2024}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./temp:/app/temp
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - sintesa-network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: sintesa-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app
    networks:
      - sintesa-network
    profiles:
      - production

  # Adminer for Database Management (Development)
  adminer:
    image: adminer:latest
    container_name: sintesa-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: mysql
      ADMINER_DESIGN: pepa-linha
    depends_on:
      - mysql
    networks:
      - sintesa-network
    profiles:
      - development

  # Redis Commander for Redis Management (Development)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: sintesa-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: local:redis:6379
      HTTP_USER: admin
      HTTP_PASSWORD: ${REDIS_COMMANDER_PASSWORD:-admin123}
    depends_on:
      - redis
    networks:
      - sintesa-network
    profiles:
      - development

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  sintesa-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16