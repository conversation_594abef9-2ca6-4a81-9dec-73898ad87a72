import { v4 as uuidv4 } from 'uuid';
import <PERSON><PERSON> from 'joi';

// Inquiry routes for SQL query processing
export default async function inquiryRoutes(fastify, options) {
  // Main inquiry endpoint - handles encrypted SQL queries
  fastify.post('/', {
    schema: {
      body: {
        type: 'object',
        required: ['encryptedQuery'],
        properties: {
          encryptedQuery: { type: 'string' },
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 1000, default: 50 },
          timeout: { type: 'integer', minimum: 1000, maximum: 300000, default: 30000 },
          includeCount: { type: 'boolean', default: true },
          format: { type: 'string', enum: ['json', 'csv', 'excel'], default: 'json' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                results: { type: 'array' },
                pagination: {
                  type: 'object',
                  properties: {
                    page: { type: 'integer' },
                    limit: { type: 'integer' },
                    total: { type: 'integer' },
                    totalPages: { type: 'integer' },
                    hasNext: { type: 'boolean' },
                    hasPrev: { type: 'boolean' }
                  }
                },
                metadata: {
                  type: 'object',
                  properties: {
                    queryId: { type: 'string' },
                    executionTime: { type: 'number' },
                    rowCount: { type: 'integer' },
                    columns: { type: 'array' },
                    grandTotal: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    const queryId = uuidv4();
    const startTime = Date.now();
    
    try {
      const { encryptedQuery, page, limit, timeout, includeCount, format } = request.body;
      const userId = request.user.userId;
      const username = request.user.username;

      fastify.log.info(`Starting inquiry ${queryId} for user ${username}`);

      // Notify via Socket.IO that query is starting
      if (fastify.socketUtils) {
        fastify.socketUtils.sendToUser(userId, 'query_start', {
          queryId,
          timestamp: new Date().toISOString()
        });
      }

      // Decrypt the SQL query
      let sqlQuery;
      try {
        sqlQuery = fastify.encryption.decryptSqlQuery(encryptedQuery);
        fastify.log.info(`Decrypted SQL query for ${queryId}`);
      } catch (error) {
        fastify.log.error(`Failed to decrypt query ${queryId}:`, error);
        return reply.code(400).send({
          success: false,
          message: 'Invalid encrypted query format',
          queryId
        });
      }

      // Validate SQL query (basic security checks)
      const forbiddenKeywords = [
        'DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE',
        'GRANT', 'REVOKE', 'EXEC', 'EXECUTE', 'xp_', 'sp_'
      ];
      
      const upperQuery = sqlQuery.toUpperCase();
      const hasForbiddenKeyword = forbiddenKeywords.some(keyword => 
        upperQuery.includes(keyword)
      );
      
      if (hasForbiddenKeyword) {
        fastify.log.warn(`Forbidden SQL operation attempted in query ${queryId}`);
        return reply.code(403).send({
          success: false,
          message: 'Query contains forbidden operations',
          queryId
        });
      }

      // Notify query progress
      if (fastify.socketUtils) {
        fastify.socketUtils.sendToUser(userId, 'query_progress', {
          queryId,
          progress: 25,
          message: 'Query validated, executing...'
        });
      }

      let results = [];
      let totalCount = 0;
      let columns = [];
      let grandTotal = null;

      // Execute query with timeout
      const queryPromise = new Promise(async (resolve, reject) => {
        try {
          // Calculate offset for pagination
          const offset = (page - 1) * limit;
          
          // Modify query for pagination if it's a SELECT statement
          let paginatedQuery = sqlQuery;
          if (upperQuery.trim().startsWith('SELECT')) {
            paginatedQuery = `${sqlQuery} LIMIT ${limit} OFFSET ${offset}`;
          }

          // Execute main query
          const [queryResults] = await fastify.db.query(paginatedQuery);
          results = queryResults;

          // Get column information
          if (results.length > 0) {
            columns = Object.keys(results[0]).map(key => ({
              name: key,
              type: typeof results[0][key]
            }));
          }

          // Get total count if requested and it's a SELECT query
          if (includeCount && upperQuery.trim().startsWith('SELECT')) {
            // Create count query by wrapping original query
            const countQuery = `SELECT COUNT(*) as total FROM (${sqlQuery}) as count_subquery`;
            const [countResults] = await fastify.db.query(countQuery);
            totalCount = countResults[0]?.total || 0;
          }

          // Calculate grand total for numeric columns if applicable
          if (results.length > 0) {
            const numericColumns = columns.filter(col => 
              col.type === 'number' && 
              results.some(row => row[col.name] != null)
            );
            
            if (numericColumns.length > 0) {
              const firstNumericColumn = numericColumns[0].name;
              const grandTotalQuery = `SELECT SUM(${firstNumericColumn}) as grand_total FROM (${sqlQuery}) as sum_subquery`;
              try {
                const [grandTotalResults] = await fastify.db.query(grandTotalQuery);
                grandTotal = grandTotalResults[0]?.grand_total || 0;
              } catch (error) {
                fastify.log.warn(`Could not calculate grand total for query ${queryId}:`, error.message);
              }
            }
          }

          resolve();
        } catch (error) {
          reject(error);
        }
      });

      // Set timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Query timeout after ${timeout}ms`));
        }, timeout);
      });

      // Race between query execution and timeout
      await Promise.race([queryPromise, timeoutPromise]);

      const executionTime = (Date.now() - startTime) / 1000;

      // Notify query completion
      if (fastify.socketUtils) {
        fastify.socketUtils.sendToUser(userId, 'query_complete', {
          queryId,
          executionTime,
          resultCount: results.length
        });
      }

      // Prepare pagination metadata
      const totalPages = includeCount ? Math.ceil(totalCount / limit) : null;
      const pagination = {
        page,
        limit,
        total: totalCount,
        totalPages,
        hasNext: includeCount ? page < totalPages : results.length === limit,
        hasPrev: page > 1
      };

      // Prepare response metadata
      const metadata = {
        queryId,
        executionTime,
        rowCount: results.length,
        columns,
        ...(grandTotal !== null && { grandTotal })
      };

      // Log successful query
      await fastify.db.query(
        `INSERT INTO query_logs (id, user_id, sql_query, execution_time, row_count, status, created_at) 
         VALUES (?, ?, ?, ?, ?, 'success', NOW())`,
        [queryId, userId, sqlQuery, executionTime, results.length]
      );

      fastify.log.info(`Query ${queryId} completed successfully in ${executionTime}s, returned ${results.length} rows`);

      // Handle different response formats
      if (format === 'csv') {
        const csv = convertToCSV(results, columns);
        reply.type('text/csv');
        reply.header('Content-Disposition', `attachment; filename="query_${queryId}.csv"`);
        return reply.send(csv);
      } else if (format === 'excel') {
        // For Excel format, you might want to use a library like 'xlsx'
        // For now, return JSON with a note
        return reply.send({
          success: true,
          message: 'Excel format not implemented yet, returning JSON',
          data: { results, pagination, metadata }
        });
      }

      // Default JSON response
      return reply.send({
        success: true,
        message: 'Query executed successfully',
        data: {
          results,
          pagination,
          metadata
        }
      });

    } catch (error) {
      const executionTime = (Date.now() - startTime) / 1000;
      
      fastify.log.error(`Query ${queryId} failed:`, error);

      // Notify query error
      if (fastify.socketUtils) {
        fastify.socketUtils.sendToUser(request.user?.userId, 'query_error', {
          queryId,
          error: error.message
        });
      }

      // Log failed query
      try {
        await fastify.db.query(
          `INSERT INTO query_logs (id, user_id, sql_query, execution_time, error_message, status, created_at) 
           VALUES (?, ?, ?, ?, ?, 'error', NOW())`,
          [queryId, request.user?.userId, 'encrypted', executionTime, error.message]
        );
      } catch (logError) {
        fastify.log.error('Failed to log query error:', logError);
      }

      return reply.code(500).send({
        success: false,
        message: error.message || 'Query execution failed',
        queryId,
        executionTime
      });
    }
  });

  // Get query history
  fastify.get('/history', {
    schema: {
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          status: { type: 'string', enum: ['success', 'error', 'all'], default: 'all' },
          dateFrom: { type: 'string', format: 'date' },
          dateTo: { type: 'string', format: 'date' }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const { page, limit, status, dateFrom, dateTo } = request.query;
      const userId = request.user.userId;
      const offset = (page - 1) * limit;

      let whereConditions = ['user_id = ?'];
      let queryParams = [userId];

      if (status !== 'all') {
        whereConditions.push('status = ?');
        queryParams.push(status);
      }

      if (dateFrom) {
        whereConditions.push('DATE(created_at) >= ?');
        queryParams.push(dateFrom);
      }

      if (dateTo) {
        whereConditions.push('DATE(created_at) <= ?');
        queryParams.push(dateTo);
      }

      const whereClause = whereConditions.join(' AND ');

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM query_logs WHERE ${whereClause}`;
      const [countResults] = await fastify.db.query(countQuery, queryParams);
      const totalCount = countResults[0].total;

      // Get paginated results
      const historyQuery = `
        SELECT id, sql_query, execution_time, row_count, status, error_message, created_at
        FROM query_logs 
        WHERE ${whereClause}
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `;
      
      const [historyResults] = await fastify.db.query(historyQuery, [...queryParams, limit, offset]);

      const totalPages = Math.ceil(totalCount / limit);

      return reply.send({
        success: true,
        message: 'Query history retrieved successfully',
        data: {
          history: historyResults,
          pagination: {
            page,
            limit,
            total: totalCount,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        }
      });

    } catch (error) {
      fastify.log.error('Get query history error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve query history'
      });
    }
  });

  // Get query statistics
  fastify.get('/stats', {
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const userId = request.user.userId;

      const statsQuery = `
        SELECT 
          COUNT(*) as total_queries,
          COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_queries,
          COUNT(CASE WHEN status = 'error' THEN 1 END) as failed_queries,
          AVG(CASE WHEN status = 'success' THEN execution_time END) as avg_execution_time,
          MAX(execution_time) as max_execution_time,
          SUM(CASE WHEN status = 'success' THEN row_count ELSE 0 END) as total_rows_processed,
          DATE(created_at) as query_date,
          COUNT(*) as daily_count
        FROM query_logs 
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
        ORDER BY query_date DESC
      `;

      const [statsResults] = await fastify.db.query(statsQuery, [userId]);

      // Overall statistics
      const overallStats = {
        totalQueries: statsResults.reduce((sum, row) => sum + row.daily_count, 0),
        successfulQueries: statsResults.reduce((sum, row) => sum + (row.successful_queries || 0), 0),
        failedQueries: statsResults.reduce((sum, row) => sum + (row.failed_queries || 0), 0),
        avgExecutionTime: statsResults.length > 0 ? 
          statsResults.reduce((sum, row) => sum + (row.avg_execution_time || 0), 0) / statsResults.length : 0,
        maxExecutionTime: Math.max(...statsResults.map(row => row.max_execution_time || 0)),
        totalRowsProcessed: statsResults.reduce((sum, row) => sum + (row.total_rows_processed || 0), 0)
      };

      return reply.send({
        success: true,
        message: 'Query statistics retrieved successfully',
        data: {
          overall: overallStats,
          daily: statsResults
        }
      });

    } catch (error) {
      fastify.log.error('Get query stats error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to retrieve query statistics'
      });
    }
  });

  // Cancel running query (placeholder - would need query execution tracking)
  fastify.post('/cancel/:queryId', {
    schema: {
      params: {
        type: 'object',
        required: ['queryId'],
        properties: {
          queryId: { type: 'string' }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const { queryId } = request.params;
      const userId = request.user.userId;

      // In a real implementation, you would track running queries and cancel them
      // For now, just return a placeholder response
      
      fastify.log.info(`Query cancellation requested for ${queryId} by user ${userId}`);

      return reply.send({
        success: true,
        message: 'Query cancellation requested',
        data: { queryId }
      });

    } catch (error) {
      fastify.log.error('Cancel query error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to cancel query'
      });
    }
  });
}

// Helper function to convert results to CSV
function convertToCSV(results, columns) {
  if (!results || results.length === 0) {
    return '';
  }

  const headers = columns.map(col => col.name).join(',');
  const rows = results.map(row => 
    columns.map(col => {
      const value = row[col.name];
      if (value === null || value === undefined) {
        return '';
      }
      // Escape quotes and wrap in quotes if contains comma or quote
      const stringValue = String(value);
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    }).join(',')
  );

  return [headers, ...rows].join('\n');
}