# Redis Setup Guide for Sintesa Backend

Your backend is already configured to work with Redis! The Redis plugin is properly set up in `src/plugins/redis.js` and all the necessary dependencies are installed.

## Current Redis Configuration

The backend is configured to connect to Red<PERSON> with these settings (from `.env`):
```
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_KEY_PREFIX=sintesa:
```

## Redis Installation Options

### Option 1: Install Redis on Windows (Recommended)

1. **Download Redis for Windows:**
   - Go to: https://github.com/microsoftarchive/redis/releases
   - Download the latest `.msi` file (e.g., `Redis-x64-3.0.504.msi`)
   - Run the installer and follow the setup wizard

2. **Start Redis Service:**
   ```powershell
   # Start Redis service
   net start redis
   
   # Or start manually
   redis-server
   ```

3. **Test Redis Connection:**
   ```powershell
   redis-cli ping
   # Should return: PONG
   ```

### Option 2: Use Docker (Alternative)

1. **Run Redis in Docker:**
   ```bash
   docker run -d --name redis-sintesa -p 6379:6379 redis:latest
   ```

2. **Test connection:**
   ```bash
   docker exec -it redis-sintesa redis-cli ping
   ```

### Option 3: Use WSL2 with Ubuntu

1. **Install Redis in WSL:**
   ```bash
   sudo apt update
   sudo apt install redis-server
   ```

2. **Start Redis:**
   ```bash
   sudo service redis-server start
   ```

3. **Test connection:**
   ```bash
   redis-cli ping
   ```

## Testing Your Setup

### 1. Check if Redis is Running
```powershell
# Check if port 6379 is listening
netstat -an | findstr :6379

# Should show something like:
# TCP    0.0.0.0:6379           0.0.0.0:0              LISTENING
```

### 2. Test Backend Connection

1. **Start your backend:**
   ```bash
   cd d:\sintesa-backend
   npm run dev
   ```

2. **Check logs for Redis connection:**
   - Look for: `✅ Redis connected successfully`
   - If Redis is not available, you'll see: `⚠️ Redis connection failed, continuing without cache`

### 3. Test Redis Functionality

Create a test file to verify Redis is working:

```javascript
// test-redis.js
import Redis from 'ioredis';

const redis = new Redis({
  host: 'localhost',
  port: 6379,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3
});

async function testRedis() {
  try {
    // Test basic operations
    await redis.set('test:key', 'Hello Redis!');
    const value = await redis.get('test:key');
    console.log('✅ Redis test successful:', value);
    
    // Clean up
    await redis.del('test:key');
    await redis.quit();
  } catch (error) {
    console.error('❌ Redis test failed:', error.message);
  }
}

testRedis();
```

Run the test:
```bash
node test-redis.js
```

## What Redis Does in Your Backend

Your backend uses Redis for:

1. **Session Management:**
   - User sessions with 8-hour expiry
   - JWT token caching
   - Session extension and cleanup

2. **Data Caching:**
   - Database query results
   - Reference data (Kdsatker, Kdkanwil, etc.)
   - API response caching

3. **Rate Limiting:**
   - Request counting per user/IP
   - Temporary blocks for excessive requests

4. **Real-time Features:**
   - Socket.IO session storage
   - User presence tracking
   - Notification queues

## Redis Operations Available

Your backend provides these Redis utilities:

```javascript
// Basic operations
fastify.cache.set(key, value, ttl)
fastify.cache.get(key)
fastify.cache.del(key)

// Session management
fastify.session.create(sessionId, data, ttl)
fastify.session.get(sessionId)
fastify.session.destroy(sessionId)

// Advanced operations
fastify.cache.hset(key, field, value)  // Hash operations
fastify.cache.lpush(key, value)        // List operations
fastify.cache.sadd(key, member)        // Set operations
```

## Troubleshooting

### Redis Connection Issues

1. **Check if Redis is running:**
   ```powershell
   netstat -an | findstr :6379
   ```

2. **Check Windows Services:**
   ```powershell
   Get-Service -Name *redis*
   ```

3. **Check firewall settings:**
   - Ensure port 6379 is not blocked
   - Add Redis to Windows Firewall exceptions

### Backend Issues

1. **Check environment variables:**
   - Verify `.env` file has correct Redis settings
   - Ensure no typos in REDIS_HOST or REDIS_PORT

2. **Check logs:**
   - Backend will log Redis connection status
   - Look for error messages in console

### Performance Tips

1. **Memory Management:**
   - Set appropriate TTL for cached data
   - Monitor Redis memory usage
   - Use `FLUSHDB` to clear cache if needed

2. **Connection Pooling:**
   - Backend uses ioredis with connection pooling
   - Adjust `maxRetriesPerRequest` if needed

## Next Steps

1. **Install Redis** using one of the options above
2. **Start Redis service**
3. **Start your backend** with `npm run dev`
4. **Check logs** for successful Redis connection
5. **Test your application** - caching should now work!

Your backend will work without Redis (it gracefully degrades), but with Redis you'll get:
- ⚡ Faster response times
- 🔄 Better session management  
- 📊 Improved performance
- 🚀 Real-time features

## Need Help?

If you encounter issues:
1. Check the backend logs for Redis connection messages
2. Verify Redis is running on port 6379
3. Test Redis connection manually with `redis-cli ping`
4. Ensure no firewall is blocking the connection