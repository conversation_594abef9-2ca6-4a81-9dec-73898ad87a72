import data from "@/data/reference/thematic/KdMP.json";
import { Select, SelectItem } from "@heroui/react";

const JenisMP = (props) => {
  // Ensure we have a valid selectedKeys Set
  const selectedKeys =
    props.value && props.value !== "" && props.value !== "XX"
      ? [props.value]
      : ["00"];

  return (
    <Select
      aria-label="Pilih Major Project"
      selectedKeys={new Set(selectedKeys)}
      onSelectionChange={(keys) => {
        const value = Array.from(keys)[0];
        props.onChange(value);
      }}
      size="sm"
      placeholder="Pilih Major Project"
      className="max-w-full"
    >
      <SelectItem key="00" value="00" textValue="Semua Major Project">
        Semua Major Project
      </SelectItem>
      {data.map((mp, index) => (
        <SelectItem
          key={mp.kdmp}
          value={mp.kdmp}
          textValue={`${mp.kdmp} - ${mp.nmmp}`}
        >
          {mp.kdmp} - {mp.nmmp}
        </SelectItem>
      ))}
    </Select>
  );
};

export default JenisMP;
