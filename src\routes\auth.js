import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

// Authentication routes
export default async function authRoutes(fastify, options) {
  // Login endpoint
  fastify.post('/login', {
    schema: {
      body: {
        type: 'object',
        required: ['username', 'password'],
        properties: {
          username: { type: 'string', minLength: 3, maxLength: 50 },
          password: { type: 'string', minLength: 6, maxLength: 100 },
          rememberMe: { type: 'boolean', default: false }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: {
              type: 'object',
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    token: { type: 'string' }
                  }
                },
                token: { type: 'string' },
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    username: { type: 'string' },
                    email: { type: 'string' },
                    role: { type: 'string' },
                    permissions: { type: 'array' },
                    lastLogin: { type: 'string' }
                  }
                },
                tokens: {
                  type: 'object',
                  properties: {
                    accessToken: { type: 'string' },
                    refreshToken: { type: 'string' },
                    expiresIn: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      }
    },
    preHandler: [fastify.rateLimit({
      max: 5,
      timeWindow: '1 minute',
      keyGenerator: (request) => request.ip
    })]
  }, async (request, reply) => {
    try {
      const { username, password, rememberMe } = request.body;
      const clientIp = request.ip;
      const userAgent = request.headers['user-agent'];

      // Check rate limiting for this username
      const loginAttempts = await fastify.auth.checkLoginAttempts(username, clientIp);
      if (loginAttempts >= 5) {
        return reply.code(429).send({
          success: false,
          message: 'Too many login attempts. Please try again later.',
          retryAfter: 900 // 15 minutes
        });
      }

      // Find user in database
      const userQuery = `
        SELECT * 
        FROM users 
        WHERE username = ? AND (active = '1' OR active IS NULL)
      `;
      
      console.log('Executing query:', userQuery, 'with params:', [username]);
      const { rows: users } = await fastify.db.query(userQuery, [username]);
      console.log('Query result:', users);
      
      if (users.length === 0) {
        await fastify.auth.recordFailedLogin(username);
        return reply.code(401).send({
          success: false,
          message: 'Invalid username or password'
        });
      }

      const user = users[0];

      // Verify password
      console.log('Comparing password:', password, 'with hash:', user.password);
      const isValidPassword = await fastify.auth.comparePassword(password, user.password);
      console.log('Password valid:', isValidPassword);
      
      if (!isValidPassword) {
        await fastify.auth.recordFailedLogin(username);
        return reply.code(401).send({
          success: false,
          message: 'Invalid username or password'
        });
      }

      // Generate tokens
      const tokenExpiry = rememberMe ? '7d' : '1h';
      const refreshTokenExpiry = rememberMe ? '30d' : '7d';
      
      console.log('Generating access token for user:', { userId: user.id, username: user.username, role: user.role });
      const accessToken = await fastify.auth.generateAccessToken({
        userId: user.id,
        username: user.username,
        role: user.role || 'user',
        permissions: []
      }, tokenExpiry);
      console.log('Access token generated successfully');

      console.log('Generating refresh token for user:', { userId: user.id, username: user.username });
      const refreshToken = await fastify.auth.generateRefreshToken({
        userId: user.id,
        username: user.username
      }, refreshTokenExpiry);
      console.log('Refresh token generated successfully');

      // Create session
      console.log('Creating session for user:', user.id);
      const sessionId = uuidv4();
      const sessionData = {
        userId: user.id,
        username: user.username,
        role: user.role || 'user',
        permissions: [],
        ip: clientIp,
        userAgent,
        refreshToken,
        lastActivity: new Date().toISOString()
      };

      console.log('Session data prepared, storing session...');
      await fastify.auth.createSession(sessionId, sessionData, rememberMe ? 30 * 24 * 60 * 60 : 7 * 24 * 60 * 60);
      console.log('Session stored successfully');

      // Update user last login
      console.log('Updating last login for user:', user.id);
      await fastify.db.query(
        'UPDATE users SET last_login = NOW() WHERE id = ?',
        [user.id]
      );
      console.log('Last login updated successfully');

      // Clear login attempts on successful login
      console.log('Clearing login attempts for user:', username);
      await fastify.auth.clearLoginAttempts(username);
      console.log('Login attempts cleared successfully');

      // Set secure cookies
      reply.setCookie('sessionId', sessionId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: rememberMe ? 30 * 24 * 60 * 60 * 1000 : 7 * 24 * 60 * 60 * 1000
      });

      // Prepare response data
      const responseData = {
        user: {
          id: user.id,
          username: user.username,
          email: user.email || null,
          fullName: user.name,
          role: user.role || 'user',
          permissions: [],
          lastLogin: new Date().toISOString(),
          avatar: user.avatar || null
        },
        tokens: {
          accessToken,
          refreshToken,
          expiresIn: rememberMe ? 7 * 24 * 60 * 60 : 60 * 60 // seconds
        }
      };

      fastify.log.info(`User ${username} logged in successfully from ${clientIp}`);

      // Encrypt the token before sending to frontend
      fastify.log.info('Encryption object available: ' + (!!fastify.encryption));
      fastify.log.info('Access token to encrypt: ' + (accessToken ? accessToken.substring(0, 20) + '...' : 'undefined'));
      let encryptedToken;
      try {
        if (!fastify.encryption) {
          throw new Error('Encryption object not available');
        }
        if (!accessToken) {
          throw new Error('Access token is undefined');
        }
        encryptedToken = fastify.encryption.encrypt(accessToken);
        fastify.log.info('Encrypted token: ' + (encryptedToken ? encryptedToken.substring(0, 20) + '...' : 'undefined'));
        fastify.log.info('Encrypted token length: ' + (encryptedToken ? encryptedToken.length : 0));
      } catch (encryptError) {
        fastify.log.error('Encryption failed: ' + encryptError.message);
        encryptedToken = accessToken; // fallback to unencrypted
      }

      fastify.log.info('About to send response with encryptedToken: ' + (encryptedToken ? encryptedToken.substring(0, 20) + '...' : 'undefined'));
      
      const responsePayload = {
        success: true,
        message: 'Login successful',
        data: {
          data: {
            token: encryptedToken
          },
          token: encryptedToken,
          user: responseData.user,
          tokens: responseData.tokens
        }
      };
      
      fastify.log.info('Response data.data.token: ' + (responsePayload.data.data.token ? responsePayload.data.data.token.substring(0, 20) + '...' : 'undefined'));
      fastify.log.info('Response data.token: ' + (responsePayload.data.token ? responsePayload.data.token.substring(0, 20) + '...' : 'undefined'));
      
      return reply.send(responsePayload);

    } catch (error) {
      fastify.log.error('Login error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Internal server error during login'
      });
    }
  });

  // Token refresh endpoint
  fastify.post('/refresh-token', {
    schema: {
      body: {
        type: 'object',
        required: ['refreshToken'],
        properties: {
          refreshToken: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { refreshToken } = request.body;
      const sessionId = request.cookies.sessionId;

      if (!refreshToken || !sessionId) {
        return reply.code(401).send({
          success: false,
          message: 'Refresh token and session required'
        });
      }

      // Verify refresh token
      let decoded;
      try {
        decoded = await fastify.jwt.verify(refreshToken);
      } catch (error) {
        return reply.code(401).send({
          success: false,
          message: 'Invalid refresh token'
        });
      }

      // Get session data
      const sessionData = await fastify.auth.getSession(sessionId);
      if (!sessionData || sessionData.refreshToken !== refreshToken) {
        return reply.code(401).send({
          success: false,
          message: 'Invalid session or refresh token'
        });
      }

      // Get updated user data
      const userQuery = `
        SELECT * 
        FROM users 
        WHERE id = ? AND (active = '1' OR active IS NULL)
      `;
      
      const { rows: users } = await fastify.db.query(userQuery, [decoded.userId]);
      
      if (users.length === 0) {
        return reply.code(401).send({
          success: false,
          message: 'User not found or inactive'
        });
      }

      const user = users[0];

      // Generate new access token
      const newAccessToken = await fastify.auth.generateAccessToken({
        userId: user.id,
        username: user.username,
        role: user.role || 'user',
        permissions: []
      });

      // Update session activity
      await fastify.auth.updateSession(sessionId, {
        lastActivity: new Date().toISOString()
      });

      // Encrypt the new token before sending to frontend
      const encryptedNewToken = fastify.encryption.encrypt(newAccessToken);

      return reply.send({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          token: encryptedNewToken,
          accessToken: encryptedNewToken,
          expiresIn: 60 * 60 // 1 hour
        }
      });

    } catch (error) {
      fastify.log.error('Token refresh error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Internal server error during token refresh'
      });
    }
  });

  // Logout endpoint
  fastify.post('/logout', {
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const sessionId = request.cookies.sessionId;
      const userId = request.user.userId;

      // Destroy session
      if (sessionId) {
        await fastify.auth.destroySession(sessionId);
      }

      // Clear cookies
      reply.clearCookie('sessionId');

      // Log logout
      await fastify.db.query(
        'UPDATE users SET last_logout = NOW() WHERE id = ?',
        [userId]
      );

      fastify.log.info(`User ${request.user.username} logged out`);

      return reply.send({
        success: true,
        message: 'Logout successful'
      });

    } catch (error) {
      fastify.log.error('Logout error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Internal server error during logout'
      });
    }
  });

  // Check login status
  fastify.get('/status', {
    preHandler: [fastify.optionalAuth]
  }, async (request, reply) => {
    try {
      if (!request.user) {
        return reply.send({
          success: false,
          message: 'Not authenticated',
          data: { authenticated: false }
        });
      }

      const sessionId = request.cookies.sessionId;
      if (sessionId) {
        // Update session activity
        await fastify.auth.updateSession(sessionId, {
          lastActivity: new Date().toISOString()
        });
      }

      return reply.send({
        success: true,
        message: 'User is authenticated',
        data: {
          authenticated: true,
          user: {
            id: request.user.userId,
            username: request.user.username,
            role: request.user.role,
            permissions: request.user.permissions
          }
        }
      });

    } catch (error) {
      fastify.log.error('Auth status check error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Change password
  fastify.post('/change-password', {
    schema: {
      body: {
        type: 'object',
        required: ['currentPassword', 'newPassword'],
        properties: {
          currentPassword: { type: 'string', minLength: 6 },
          newPassword: { type: 'string', minLength: 6, maxLength: 100 }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const { currentPassword, newPassword } = request.body;
      const userId = request.user.userId;

      // Get current user data
      const [users] = await fastify.db.query(
        'SELECT password FROM users WHERE id = ?',
        [userId]
      );

      if (users.length === 0) {
        return reply.code(404).send({
          success: false,
          message: 'User not found'
        });
      }

      // Verify current password
      const isValidPassword = await fastify.auth.comparePassword(currentPassword, users[0].password);
      if (!isValidPassword) {
        return reply.code(401).send({
          success: false,
          message: 'Current password is incorrect'
        });
      }

      // Hash new password
      const hashedNewPassword = await fastify.auth.hashPassword(newPassword);

      // Update password
      await fastify.db.query(
        'UPDATE users SET password = ?, password_changed_at = NOW() WHERE id = ?',
        [hashedNewPassword, userId]
      );

      fastify.log.info(`User ${request.user.username} changed password`);

      return reply.send({
        success: true,
        message: 'Password changed successfully'
      });

    } catch (error) {
      fastify.log.error('Change password error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Internal server error during password change'
      });
    }
  });

  // Get user profile
  fastify.get('/profile', {
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const userId = request.user.userId;

      const userQuery = `
        SELECT u.id, u.username, u.email, u.full_name, u.avatar, u.created_at, 
               u.last_login, u.login_count, r.name as role_name, r.permissions
        FROM users u 
        LEFT JOIN roles r ON u.role_id = r.id 
        WHERE u.id = ?
      `;
      
      const [users] = await fastify.db.query(userQuery, [userId]);
      
      if (users.length === 0) {
        return reply.code(404).send({
          success: false,
          message: 'User not found'
        });
      }

      const user = users[0];

      return reply.send({
        success: true,
        message: 'Profile retrieved successfully',
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          fullName: user.full_name,
          avatar: user.avatar,
          role: user.role_name || 'user',
          permissions: user.permissions ? JSON.parse(user.permissions) : [],
          createdAt: user.created_at,
          lastLogin: user.last_login,
          loginCount: user.login_count
        }
      });

    } catch (error) {
      fastify.log.error('Get profile error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Internal server error'
      });
    }
  });

  // Update user profile
  fastify.put('/profile', {
    schema: {
      body: {
        type: 'object',
        properties: {
          email: { type: 'string', format: 'email' },
          fullName: { type: 'string', maxLength: 100 },
          avatar: { type: 'string', maxLength: 255 }
        }
      }
    },
    preHandler: [fastify.authenticate]
  }, async (request, reply) => {
    try {
      const { email, fullName, avatar } = request.body;
      const userId = request.user.userId;

      const updateFields = [];
      const updateValues = [];

      if (email !== undefined) {
        updateFields.push('email = ?');
        updateValues.push(email);
      }
      if (fullName !== undefined) {
        updateFields.push('full_name = ?');
        updateValues.push(fullName);
      }
      if (avatar !== undefined) {
        updateFields.push('avatar = ?');
        updateValues.push(avatar);
      }

      if (updateFields.length === 0) {
        return reply.code(400).send({
          success: false,
          message: 'No fields to update'
        });
      }

      updateFields.push('updated_at = NOW()');
      updateValues.push(userId);

      const updateQuery = `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`;
      await fastify.db.query(updateQuery, updateValues);

      fastify.log.info(`User ${request.user.username} updated profile`);

      return reply.send({
        success: true,
        message: 'Profile updated successfully'
      });

    } catch (error) {
      fastify.log.error('Update profile error:', error);
      return reply.code(500).send({
        success: false,
        message: 'Internal server error during profile update'
      });
    }
  });
}