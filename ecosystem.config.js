module.exports = {
  apps: [
    {
      name: 'sintesa-backend',
      script: 'src/server.js',
      cwd: '/app',
      instances: process.env.PM2_INSTANCES || 'max',
      exec_mode: 'cluster',
      
      // Environment variables
      env: {
        NODE_ENV: 'development',
        PORT: 3001,
        HOST: '0.0.0.0'
      },
      
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001,
        HOST: '0.0.0.0'
      },
      
      // Logging
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process management
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      restart_delay: 4000,
      
      // Health monitoring
      min_uptime: '10s',
      max_restarts: 10,
      
      // Advanced settings
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Source map support
      source_map_support: true,
      
      // Instance variables
      instance_var: 'INSTANCE_ID',
      
      // Graceful shutdown
      shutdown_with_message: true,
      
      // Health check
      health_check_grace_period: 3000,
      
      // Monitoring
      pmx: true,
      
      // Ignore watch
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        'temp',
        '.git'
      ],
      
      // Environment specific configurations
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 3001,
        HOST: '0.0.0.0',
        LOG_LEVEL: 'debug'
      },
      
      env_testing: {
        NODE_ENV: 'testing',
        PORT: 3002,
        HOST: '127.0.0.1',
        LOG_LEVEL: 'warn'
      }
    }
  ],
  
  // Deployment configuration
  deploy: {
    production: {
      user: 'deploy',
      host: ['sintesa-prod-1', 'sintesa-prod-2'],
      ref: 'origin/main',
      repo: '**************:your-org/sintesa-backend.git',
      path: '/var/www/sintesa-backend',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'apt update && apt install git -y'
    },
    
    staging: {
      user: 'deploy',
      host: 'sintesa-staging',
      ref: 'origin/develop',
      repo: '**************:your-org/sintesa-backend.git',
      path: '/var/www/sintesa-backend-staging',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env staging'
    }
  }
};