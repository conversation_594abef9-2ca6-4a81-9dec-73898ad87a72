(()=>{var a={};a.id=425,a.ids=[425],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8086:a=>{"use strict";a.exports=require("module")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29860:(a,b,c)=>{Promise.resolve().then(c.bind(c,30885))},30885:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\sintesaNEXT2\\\\src\\\\app\\\\(dashboard)\\\\inquiry-data\\\\up-tup\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\inquiry-data\\up-tup\\page.jsx","default")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},39606:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["inquiry-data",{children:["up-tup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,30885)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\inquiry-data\\up-tup\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,8413)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\layout.jsx"],loading:[()=>Promise.resolve().then(c.bind(c,48221)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\loading.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,53361)),"D:\\sintesaNEXT2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\sintesaNEXT2\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["D:\\sintesaNEXT2\\src\\app\\(dashboard)\\inquiry-data\\up-tup\\page.jsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/inquiry-data/up-tup/page",pathname:"/inquiry-data/up-tup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/inquiry-data/up-tup/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},40228:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},43364:(a,b,c)=>{Promise.resolve().then(c.bind(c,68090))},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},59e3:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(4765),e=c.n(d);let f="mebe23",g=(a,b=f)=>{let c=e().AES.encrypt(JSON.stringify(a),b).toString();return e().enc.Base64.stringify(e().enc.Utf8.parse(c))}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68090:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>aW});var d=c(60687),e=c(67401),f=c(43210),g=c.n(f),h=c(14221),i=c(21875),j=c(56093),k=c(55110),l=c(49995),m=c(75378),n=c(27580),o=c(11860),p=c(13964),q=c(70615);let r=({isOpen:a,onClose:b,query:c,title:e})=>{let[g,h]=(0,f.useState)(!1),r=async()=>{if(c)try{await navigator.clipboard.writeText(c),h(!0),setTimeout(()=>h(!1),1500)}catch(a){h(!1)}};return(0,d.jsx)(i.Y,{isOpen:a,onClose:b,size:"3xl",scrollBehavior:"inside",backdrop:"blur",hideCloseButton:!0,classNames:{header:"bg-gradient-to-r from-gray-200 to-zinc-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(j.g,{children:[(0,d.jsx)(k.c,{className:"flex justify-between items-center m-6",children:(0,d.jsx)("div",{className:"text-lg font-semibold",children:e||"SQL Preview"})}),(0,d.jsx)(l.h,{children:(0,d.jsx)("div",{className:"bg-gray-100 p-8 rounded-xl overflow-auto max-h-[60vh]",children:(0,d.jsx)("pre",{className:"whitespace-pre-wrap text-sm font-mono text-gray-800",style:{textAlign:"center"},children:c&&c.replace(/\s+/g," ").trim()})})}),(0,d.jsxs)(m.q,{className:"flex justify-between",children:[(0,d.jsx)(n.T,{color:"danger",variant:"light",onPress:b,startContent:(0,d.jsx)(o.A,{size:16}),children:"Tutup"}),(0,d.jsx)(n.T,{color:"default",variant:"ghost",onPress:r,startContent:g?(0,d.jsx)(p.A,{size:16}):(0,d.jsx)(q.A,{size:16}),children:g?"Tersalin!":"Salin ke Clipboard"})]})]})})};var s=c(41871),t=c(44301),u=c(21988),v=c(69087),w=c(61611),x=c(8819),y=c(40611),z=c(30485),A=c(62085);let B=({isOpen:a,onClose:b,query:c,thang:e,queryType:g="INQUIRY"})=>{let[p,q]=(0,f.useState)(!1),{axiosJWT:r,token:B,name:C}=(0,f.useContext)(h.A),{showToast:D}=(0,y.d)(),E=z.Ik().shape({queryName:z.Yj().required("Nama Query harus diisi"),queryType:z.Yj().required("Tipe Query harus dipilih")}),F={queryName:"",queryType:g,thang:e||new Date().getFullYear().toString()},G=async(a,{resetForm:d})=>{q(!0);try{let e={tipe:a.queryType,nama:a.queryName,name:C,query:c,thang:a.thang};await r.post("http://localhost:88/user/simpanquery",e,{headers:{Authorization:`Bearer ${B}`,"Content-Type":"application/json"}}),D("Query berhasil disimpan","success"),d(),b()}catch(a){D(a.response?.data?.error||"Gagal menyimpan query","error")}finally{q(!1)}};return(0,d.jsx)(i.Y,{isOpen:a,onClose:b,size:"3xl",scrollBehavior:"inside",backdrop:"blur",hideCloseButton:!0,classNames:{header:"bg-gradient-to-r from-yellow-200 to-amber-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(j.g,{children:[(0,d.jsx)(k.c,{className:"flex justify-between items-center m-6",children:(0,d.jsxs)("div",{className:"text-lg font-semibold flex items-center",children:[(0,d.jsx)(w.A,{className:"mr-2 text-blue-600",size:20}),"Simpan Query"]})}),(0,d.jsx)(A.l1,{initialValues:F,validationSchema:E,onSubmit:G,children:({values:a,errors:c,touched:e,handleChange:f,isSubmitting:g})=>(0,d.jsxs)(A.lV,{children:[(0,d.jsx)(l.h,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tahun Anggaran"}),(0,d.jsx)(s.r,{name:"thang",value:a.thang,onChange:f,disabled:!0,className:"bg-gray-100"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tipe Query"}),(0,d.jsxs)(t.d,{name:"queryType",value:a.queryType,onChange:f,disabled:p,children:[(0,d.jsx)(u.y,{value:"INQUIRY",children:"Inquiry"},"INQUIRY"),(0,d.jsx)(u.y,{value:"BELANJA",children:"Belanja"},"BELANJA"),(0,d.jsx)(u.y,{value:"PENERIMAAN",children:"Penerimaan"},"PENERIMAAN"),(0,d.jsx)(u.y,{value:"BLOKIR",children:"Blokir"},"BLOKIR")]}),c.queryType&&e.queryType&&(0,d.jsx)("div",{className:"text-red-500 text-xs mt-1",children:c.queryType})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nama Query"}),(0,d.jsx)(s.r,{name:"queryName",value:a.queryName,onChange:f,placeholder:"Masukkan nama untuk query ini...",disabled:p}),c.queryName&&e.queryName&&(0,d.jsx)("div",{className:"text-red-500 text-xs mt-1",children:c.queryName})]}),(0,d.jsx)("div",{className:"text-xs text-gray-500 italic",children:"*) Query yang tersimpan dapat diakses di menu Profile, tab Query Data"})]})}),(0,d.jsxs)(m.q,{className:"flex justify-between",children:[(0,d.jsx)(n.T,{color:"danger",variant:"light",onPress:b,disabled:p,startContent:(0,d.jsx)(o.A,{size:16}),children:"Tutup"}),(0,d.jsx)(n.T,{color:"warning",variant:"ghost",type:"submit",disabled:p,className:"w-[160px]",startContent:p?(0,d.jsx)(v.o,{size:"sm"}):(0,d.jsx)(x.A,{size:16}),children:p?"Menyimpan...":"Simpan Query"})]})]})})]})})};var C=c(17985),D=c(84292),E=c(53823),F=c(88977),G=c(14229),H=c(37911),I=c(10022),J=c(5066),K=c(16023);async function L(a,b="data.xlsx"){if(!a||!a.length)return;let d=await c.e(3103).then(c.bind(c,33103)),e=d.utils.json_to_sheet(a),f=d.utils.book_new();d.utils.book_append_sheet(f,e,"Sheet1");let g=new Blob([d.write(f,{bookType:"xlsx",type:"array"})],{type:"application/octet-stream"}),h=URL.createObjectURL(g),i=document.createElement("a");i.setAttribute("href",h),i.setAttribute("download",b),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(h)}async function M(a,b="data.pdf"){if(!a||!a.length)return;let d=(await c.e(4403).then(c.bind(c,4403))).default,e=(await c.e(8848).then(c.bind(c,88848))).default,f=new d,g=Object.keys(a[0]),h=a.map(a=>g.map(b=>a[b]));e(f,{head:[g],body:h,styles:{fontSize:8},headStyles:{fillColor:[41,128,185]}}),f.save(b)}let N=({showModalPDF:a,setShowModalPDF:b,selectedFormat:c,setSelectedFormat:e,fetchExportData:f,filename:g="data_export",loading:h})=>{let p=async()=>{try{let a=await f();if(!a||0===a.length)return;switch(c){case"pdf":await M(a,`${g}.pdf`);break;case"excel":await L(a,`${g}.xlsx`);break;case"json":!function(a,b="data.json"){if(!a||!a.length)return;let c=new Blob([JSON.stringify(a,null,2)],{type:"application/json"}),d=URL.createObjectURL(c),e=document.createElement("a");e.setAttribute("href",d),e.setAttribute("download",b),e.style.visibility="hidden",document.body.appendChild(e),e.click(),document.body.removeChild(e),URL.revokeObjectURL(d)}(a,`${g}.json`);break;case"text":!function(a,b="data.txt"){if(!a||!a.length)return;let c=new Blob([JSON.stringify(a,null,2)],{type:"text/plain"}),d=URL.createObjectURL(c),e=document.createElement("a");e.setAttribute("href",d),e.setAttribute("download",b),e.style.visibility="hidden",document.body.appendChild(e),e.click(),document.body.removeChild(e),URL.revokeObjectURL(d)}(a,`${g}.txt`)}b(!1)}catch(a){console.error("Export failed",a)}};return(0,d.jsx)(i.Y,{isOpen:a,onClose:()=>b(!1),size:"3xl",scrollBehavior:"inside",backdrop:"blur",hideCloseButton:!0,classNames:{header:"bg-gradient-to-r from-green-200 to-emerald-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(j.g,{children:[(0,d.jsx)(k.c,{className:"flex justify-between items-center m-6",children:(0,d.jsxs)("div",{className:"text-lg font-semibold flex items-center",children:[(0,d.jsx)(F.A,{className:"mr-2 text-success",size:20}),"Kirim Data ke WhatsApp"]})}),(0,d.jsx)(l.h,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Pilih format file untuk dikirim:"}),(0,d.jsxs)(C.U,{value:c,onValueChange:e,orientation:"horizontal",className:"flex flex-row gap-8 justify-center h-16 items-center",classNames:{wrapper:"gap-8 justify-center h-16 items-center"},children:[(0,d.jsx)(D.O,{value:"pdf",color:"danger",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(G.A,{className:"mr-2 text-red-600",size:18}),(0,d.jsx)("span",{children:"PDF"})]})}),(0,d.jsx)(D.O,{value:"excel",color:"success",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(H.A,{className:"mr-2 text-green-600",size:18}),(0,d.jsx)("span",{children:"Excel (.xlsx)"})]})}),(0,d.jsx)(D.O,{value:"json",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(I.A,{className:"mr-2 text-blue-600",size:18}),(0,d.jsx)("span",{children:"JSON"})]})}),(0,d.jsx)(D.O,{value:"text",color:"default",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(J.A,{className:"mr-2 text-gray-600",size:18}),(0,d.jsx)("span",{children:"Text (.txt)"})]})})]}),(0,d.jsx)(E.y,{className:"my-2"}),(0,d.jsx)("div",{className:"text-xs text-gray-500",children:(0,d.jsxs)("p",{children:["Nama file: ",g,".",c]})})]})}),(0,d.jsxs)(m.q,{className:"flex justify-between",children:[(0,d.jsx)(n.T,{color:"danger",variant:"light",onPress:()=>b(!1),disabled:h,startContent:(0,d.jsx)(o.A,{size:16}),children:"Tutup"}),(0,d.jsx)(n.T,{color:"success",variant:"ghost",onPress:p,disabled:h,className:"w-[160px]",startContent:h?(0,d.jsx)(v.o,{size:"sm"}):(0,d.jsx)(K.A,{size:16}),children:h?"Mengirim...":"Kirim"})]})]})})};var O=c(7192),P=c(51034),Q=c.n(P),R=c(99270),S=c(98564),T=c(85015),U=c(55327),V=c(80273),W=c(92241),X=c(98e3),Y=c(76142),Z=c(18445),$=c(42817),_=c(59e3);let aa=({isOpen:a,onClose:b,sql:c,from:e,thang:p,pembulatan:q})=>{let{axiosJWT:r,token:t,statusLogin:u}=(0,f.useContext)(h.A);(0,f.useEffect)(()=>{},[q]);let[w,x]=(0,f.useState)(!1),[y,z]=(0,f.useState)(""),[A,B]=(0,f.useState)(null),[C,D]=(0,f.useState)(!1),[E,F]=(0,f.useState)(0),[G,H]=(0,f.useState)(null),[I,J]=(0,f.useState)(null),[K,L]=(0,f.useState)(!1),[M,N]=(0,f.useState)(!0),[P,aa]=(0,f.useState)(!1),[ab,ac]=(0,f.useState)(null),ad=(0,f.useRef)(""),ae=(0,f.useRef)({column:null,direction:null}),[af,ag]=(0,f.useState)({column:null,direction:null}),[ah,ai]=(0,f.useState)([]),[aj,ak]=(0,f.useState)(null),[al,am]=(0,f.useState)(!1),an=(0,f.useRef)(1),ao=async(a=1,b=!1)=>{if(!u||!c)return;let d=1===a;d&&!b?(x(!0),N(!0),ai([]),an.current=1):b&&(N(!1),aa(!0)),am(!0),J(null);let e=performance.now();try{let f=c;if(ae.current.column&&ae.current.direction){let a=ae.current.column,b="ascending"===ae.current.direction?"ASC":"DESC";if(/\bORDER\s+BY\b/i.test(c))f=c.replace(/ORDER\s+BY\s+[^;]*/i,`ORDER BY ${a} ${b}`);else{let d=c.match(/(\s+LIMIT\s+)/i);f=d?c.replace(d[0],` ORDER BY ${a} ${b}${d[0]}`):`${c} ORDER BY ${a} ${b}`}}if(ad.current&&ad.current.trim()){let a=ad.current.trim().replace(/'/g,"''"),b=/\bWHERE\b/i.test(c),d=c.match(/SELECT\s+(.*?)\s+FROM/i);if(d){let e=d[1],g=[];if("*"===e.trim());else if((g=e.split(",").map(a=>{let b=a.trim().split(/\s+AS\s+/i)[0].trim();return b=b.replace(/["`\[\]]/g,"")}).filter(a=>{let b=a.trim();return!(b.includes("(")||b.includes("*")||b.match(/^(COUNT|SUM|AVG|MAX|MIN|DISTINCT|CASE|IF|CONCAT|SUBSTRING|DATE|YEAR|MONTH|DAY)/i)||b.match(/^[0-9]+$/)||b.match(/^['"`].*['"`]$/)||b.match(/^NULL$/i)||0===b.length||b.includes("+")||b.includes("-")||b.includes("*")||b.includes("/")||b.includes("=")||b.includes("<")||b.includes(">"))&&b.match(/^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$/)})).length>0){let d=g.filter(a=>{let b=a.toUpperCase();return"PAGU"!==b&&"PAGU_APBN"!==b&&"PAGU_DIPA"!==b&&"REALISASI"!==b&&"BLOKIR"!==b});if(d.length>0){let e=d.map(b=>`(LOWER(CAST(${b} AS CHAR)) LIKE LOWER('%${a}%'))`).join(" OR "),g=`(${e})`;if(b){let a=c.match(/(\s+(GROUP\s+BY|ORDER\s+BY|HAVING|LIMIT)\s+)/i);f=a?c.replace(a[0],` AND ${g}${a[0]}`):`${c} AND ${g}`}else{let a=c.match(/(\s+(GROUP\s+BY|ORDER\s+BY|HAVING|LIMIT)\s+)/i);f=a?c.replace(a[0],` WHERE ${g}${a[0]}`):`${c} WHERE ${g}`}}}}}let g=encodeURIComponent(f),h=(0,_.A)(g),i=await r.post("http://localhost:88/next/inquiry",{sql:h,page:a},{timeout:3e4}),j=performance.now();if(B((j-e)/1e3),i.data){let c=i.data.data||[],e=i.data.total||0,f=i.data.totalPages||0,g=i.data.grandTotals||null;F(e),d&&g&&H(g);let h=!1;if(f>0)h=a<f;else if(e>0){let b=Math.ceil(e/100);h=a<b}else h=c.length>=100;ak(h?(a+1).toString():null),an.current=a,b?ai(a=>[...a,...c]):ai(c)}else F(0),ai([]),ak(null)}catch(e){let{status:a,data:c}=e.response||{},d=c&&c.error||e.message||"Terjadi Permasalahan Koneksi atau Server Backend";J(d),(0,O.t)(a,d),F(0),b||(ai([]),ak(null))}finally{am(!1),d&&!b?x(!1):b&&aa(!1)}},[ap,aq]=(0,$.X)({hasMore:!!aj,isEnabled:a&&u,shouldUseLoader:!0,onLoadMore:()=>{aj&&!al&&ao(parseInt(aj),!0)}});(0,f.useEffect)(()=>{if(a&&u&&c){let a=setTimeout(()=>{z(""),ad.current="",ag({column:null,direction:null}),ae.current={column:null,direction:null},J(null),N(!0),ao(1,!1)},100);return()=>{clearTimeout(a)}}},[a,u,c]),(0,f.useEffect)(()=>{!a&&(J(null),z(""),ad.current="",F(0),B(null),N(!0),aa(!1),ag({column:null,direction:null}),ae.current={column:null,direction:null},ak(null),ab&&(clearTimeout(ab),ac(null)))},[a,ab]),(0,f.useEffect)(()=>{w||al||N(!1)},[w,al]);let ar=a=>{let b=Number(a);return isNaN(b)?"0":"1000000000000"===q?new Intl.NumberFormat("de-DE",{minimumFractionDigits:0,maximumFractionDigits:2}).format(b):new Intl.NumberFormat("de-DE",{minimumFractionDigits:0,maximumFractionDigits:0}).format(b)},as={kddept:a=>String(a),kdsatker:a=>String(a)},at=(0,f.useMemo)(()=>0===ah.length?[]:Object.keys(ah[0]),[ah]),au=(0,f.useMemo)(()=>0===ah.length?{}:at.reduce((a,b)=>(["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(b.toUpperCase())&&ah.reduce((a,c)=>{let d=c[b];return isNaN(Number(d))||""===d||"boolean"==typeof d?a:a+1},0)/ah.length>.7&&(a[b]=!0),a),{}),[ah,at]);g().useEffect(()=>{},[]);let av=at.length>0;return(0,d.jsx)(i.Y,{backdrop:"blur",isOpen:a,onClose:b,size:C?"full":"6xl",scrollBehavior:"inside",hideCloseButton:!0,className:C?"max-h-full":"h-[80vh] w-[80vw]",classNames:{header:"bg-gradient-to-r from-sky-200 to-cyan-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(j.g,{children:[(0,d.jsxs)(k.c,{className:"flex justify-between items-center m-6",children:[(0,d.jsx)("div",{className:"text-lg font-semibold",children:"Hasil Inquiry"}),(0,d.jsx)("div",{className:"flex items-center space-x-2",children:(0,d.jsx)(S.A,{isSelected:C,onValueChange:D,onChange:a=>{D(a.target.checked)},size:"sm",children:(0,d.jsx)("span",{className:"text-sm",children:"Layar Penuh"})})})]}),(0,d.jsxs)(l.h,{className:"flex flex-col h-full min-h-0 p-0",children:[(0,d.jsx)("div",{className:"flex justify-end items-center px-6",children:(0,d.jsx)("div",{className:"flex space-x-2",children:(0,d.jsx)(s.r,{placeholder:"Ketik untuk mencari Kode atau Nama",value:y,onChange:a=>{let b=a.target.value;if(z(b),ad.current=b,J(null),ab&&clearTimeout(ab),""===b){ao(1,!1);let a=aq.current;a&&a.scrollTo({top:0,behavior:"smooth"}),ac(null);return}let c=setTimeout(()=>{ao(1,!1);let a=aq.current;a&&a.scrollTo({top:0,behavior:"smooth"}),ac(null)},300);ac(c)},startContent:(0,d.jsx)(R.A,{size:16}),size:"md",className:"w-96"})})}),I?(0,d.jsxs)("div",{className:"text-center p-8 text-red-500",children:[(0,d.jsxs)("p",{children:["Error loading data: ",I]}),(0,d.jsxs)("div",{className:"mt-2 space-x-2",children:[(0,d.jsx)(n.T,{color:"primary",size:"sm",onClick:()=>{J(null),L(!0),setTimeout(()=>{ao(1,!1),L(!1)},100)},isLoading:K||w,children:"Retry"}),(0,d.jsx)(n.T,{color:"default",size:"sm",variant:"bordered",onClick:b,children:"Close"})]})]}):0!==ah.length||w||al?0===at.length?(0,d.jsx)("div",{className:"flex items-center justify-center h-full py-8",children:w||al?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(v.o,{color:"primary",size:"lg",variant:"simple"}),(0,d.jsx)("span",{className:"text-lg text-gray-600 ml-6 flex gap-0.5",children:"Memproses query data...".split("").map((a,b)=>(0,d.jsx)("span",{style:{display:"inline-block",animation:"wave 1.2s infinite",animationDelay:`${.08*b}s`},children:" "===a?"\xa0":a},b))}),(0,d.jsx)("style",{children:`
                    @keyframes wave {
                      0%, 60%, 100% { transform: translateY(0); }
                      30% { transform: translateY(-8px); }
                    }
                  `})]}):(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"No data available"})}):(0,d.jsx)("div",{className:"h-full overflow-auto px-6 py-1",ref:aq,children:(0,d.jsx)(T.Z,{className:"h-full p-4 shadow-none border-2",children:(0,d.jsxs)(U.j,{"aria-label":"Inquiry results table",removeWrapper:!0,sortDescriptor:af,onSortChange:a=>{ag(a),ae.current=a,ao(1,!1);let b=aq.current;b&&b.scrollTo({top:0,behavior:"smooth"})},classNames:{base:"h-full overflow-auto",table:"h-full",th:"position: sticky top-0 z-20",wrapper:"h-full w-full "},children:[(0,d.jsxs)(V.X,{children:[av&&(0,d.jsx)(W.e,{className:"text-center w-12 uppercase",children:"No"},"index"),at.map(a=>{au[a];let b=["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(a.toUpperCase()),c={};return["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(a.toUpperCase())&&(c={width:"160px",minWidth:"160px",maxWidth:"260px"}),(0,d.jsx)(W.e,{allowsSorting:b,className:"text-center uppercase",style:c,children:a},a)})]}),(0,d.jsxs)(X.E,{isLoading:!1,emptyContent:"No data to display",children:[0===ah.length?(0,d.jsx)(Y.s,{children:(0,d.jsx)(Z.w,{colSpan:at.length+ +!!av,className:"text-center",children:y?`Tidak ada hasil untuk pencarian: "${y}"`:"No data available"})}):ah.map((a,b)=>(0,d.jsxs)(Y.s,{children:[av&&(0,d.jsx)(Z.w,{className:"text-center",children:b+1}),at.map(b=>(0,d.jsx)(Z.w,{className:au[b]?"text-right":"text-center",children:as[b]?as[b](a[b]):au[b]&&!isNaN(Number(a[b]))?ar(a[b]):a[b]},b))]},`${a.id||b}`)),ah.length>0&&(0,d.jsx)(Y.s,{children:(0,d.jsx)(Z.w,{colSpan:at.length+ +!!av,className:`text-center ${P?"py-4":"py-2"}`,style:{minHeight:"40px"},children:(0,d.jsx)("div",{ref:ap,className:"w-full",children:P?(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,d.jsx)(v.o,{color:"primary",size:"md",variant:"simple"}),(0,d.jsx)("span",{className:"text-sm text-default-600",children:"Memuat data selanjutnya..."})]}):(0,d.jsx)("div",{className:"h-1 w-full flex items-center justify-center",children:!1})})})}),ah.length>0&&(0,d.jsxs)(Y.s,{className:"sticky bottom-0 bg-default-100 z-20 rounded-lg",children:[av&&(0,d.jsx)(Z.w,{className:"text-center font-medium text-foreground-600 bg-default-100 first:rounded-l-lg"}),at.map((a,b)=>{let c=au[a],e=a.toUpperCase(),f=0;c&&["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(e)&&(f=ah.reduce((b,c)=>{let d=Number(c[a]);return isNaN(d)?b:b+d},0));let g=at.findLastIndex(a=>!au[a]);return(0,d.jsx)(Z.w,{className:`${c?"text-right":"text-center"} font-medium text-foreground-600 bg-default-100 uppercase ${0===b&&!av?"first:rounded-l-lg":""} ${b===at.length-1?"last:rounded-r-lg":""}`,children:c&&["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(e)?ar(f):b===g?"GRAND TOTAL":""},a)})]})]})]})})}):(0,d.jsx)("div",{className:"text-center p-8 text-gray-500",children:y?(0,d.jsxs)("div",{children:[(0,d.jsxs)("p",{children:['Tidak ada hasil ditemukan untuk pencarian: "',y,'"']}),(0,d.jsx)("p",{className:"text-sm mt-2",children:"Coba gunakan kata kunci yang berbeda"})]}):(0,d.jsxs)("div",{children:["No data available",!1]})})]}),(0,d.jsx)(m.q,{children:(0,d.jsxs)("div",{className:"flex justify-between items-center gap-8 w-full",children:[(0,d.jsx)("div",{className:"flex text-sm",children:E>0?(0,d.jsxs)(d.Fragment,{children:["Total Baris: ",Q()(E).format("0,0"),", Ditampilkan:"," ",ah.length," item",y&&` (hasil pencarian: "${y}")`]}):y?`Tidak ada hasil untuk pencarian: "${y}"`:"No data"}),(0,d.jsx)(n.T,{color:"danger",variant:"ghost",className:"w-[120px]",onPress:b,startContent:(0,d.jsx)(o.A,{size:16}),children:"Tutup"})]})})]})})};var ab=c(80505);let ac=({id:a,checked:b,onChange:c,label:e,size:f="sm",disabled:g=!1})=>(0,d.jsxs)("div",{className:`flex items-center gap-3 px-2 py-1 rounded-2xl shadow-sm transition-all duration-300 group ${g?"opacity-50":""}`,children:[(0,d.jsx)(ab.Z,{id:a,isSelected:b,onValueChange:g?void 0:c,size:f,isDisabled:g,"aria-label":e,"aria-labelledby":`${a}-label`,classNames:{wrapper:"group-data-[selected=true]:bg-gradient-to-r group-data-[selected=true]:from-purple-400 group-data-[selected=true]:to-blue-400",thumb:"group-data-[selected=true]:bg-white shadow-lg"}}),(0,d.jsx)("label",{id:`${a}-label`,htmlFor:a,className:`text-sm font-medium transition-colors duration-200 flex-1 ${g?"text-gray-400 cursor-not-allowed":"text-gray-700 group-hover:text-purple-600 cursor-pointer"}`,children:e})]});var ad=c(77611),ae=c(54861),af=c(79410),ag=c(96882);let ah=({inquiryState:a,status:b})=>{let{dept:c,setDept:e,deptradio:f,setDeptradio:g,deptkondisi:h,setDeptkondisi:i,katadept:j,setKatadept:k}=a||{},l=j&&""!==j.trim(),m=h&&""!==h.trim(),o=c&&"XXX"!==c&&"000"!==c&&"XX"!==c,p=l||m,q=l||o,r=m||o;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-green-100 to-emerald-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(af.A,{size:20,className:"ml-4 text-secondary"}),"Kementerian"]})," ",(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[" ",(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${p?"text-gray-400":"text-gray-700"}`,children:"Pilih Kementerian"}),o&&!p&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>e&&e("000"),children:"Clear"})]}),(0,d.jsx)(ae.A,{value:c,onChange:e,className:"w-full min-w-0 max-w-full",size:"sm",status:b,isDisabled:p})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),m&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>i&&i(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:h||"",isDisabled:q,onChange:a=>{let b=a.target.value;i&&i(b)}})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),l&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>k&&k(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: keuangan",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:r,onChange:a=>{let b=a.target.value;k&&k(b)}})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"})," ",(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:[f||"1"],onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),!b){g&&g("1");return}g&&g(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var ai=c(79300),aj=c(45115);let ak=({inquiryState:a})=>{let{dept:b,kdunit:c,setKdunit:e,unitkondisi:f,setUnitkondisi:h,kataunit:i,setKataunit:j,unitradio:k,setUnitradio:l}=a||{},m=i&&""!==i.trim(),o=f&&""!==f.trim(),p=c&&"XXX"!==c&&"XX"!==c,q=m||o,r=m||p,v=o||p;return g().useEffect(()=>{e&&e("XX")},[b,e]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-green-100 to-emerald-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(ai.A,{size:20,className:"ml-4 text-secondary"}),"Eselon I"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Pilih Eselon I"}),p&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>e&&e("XX"),children:"Clear"})]}),(0,d.jsx)(aj.A,{value:c,onChange:e,kddept:b,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihunit",isDisabled:q})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"ml-1 cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),o&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 01,02,03, dst",className:"w-full min-w-0",size:"sm",value:f||"",isDisabled:r,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),m&&!v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: sekretariat",className:"w-full min-w-0",size:"sm",value:i||"",isDisabled:v,onChange:a=>j&&j(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([k||"1"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&l&&l(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var al=c(71850),am=c(8521);let an=({inquiryState:a})=>{let{dekon:b,setDekon:c,dekonkondisi:e,setDekonkondisi:f,katadekon:g,setKatadekon:h,dekonradio:i,setDekonradio:j}=a,k=g&&""!==g.trim(),l=e&&""!==e.trim(),m=b&&"XXX"!==b&&"XX"!==b&&"000"!==b&&""!==b.trim(),o=k||l,p=k||m,q=l||m;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-green-100 to-emerald-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(al.A,{size:20,className:"ml-4 text-secondary"}),"Kewenangan"]})," ",(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${o?"text-gray-400":"text-gray-700"}`,children:"Pilih Kewenangan"}),m&&!o&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(am.A,{value:b,onChange:c,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihdekon",isDisabled:o})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${p?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),l&&!p&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>f&&f(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: DK,TP,UB, dst",className:"w-full min-w-0",size:"sm",value:e||"",isDisabled:p,onChange:a=>f&&f(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),k&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: dekonsentrasi",className:"w-full min-w-0",size:"sm",value:g||"",isDisabled:q,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"})," ",(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([i]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&j(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var ao=c(776);let ap=({inquiryState:a,status:b})=>{let{kanwil:c,setKanwil:e,prov:f,kanwilradio:h,setKanwilradio:i,kanwilkondisi:j,setKanwilkondisi:k,katakanwil:l,setKatakanwil:m}=a,o=l&&""!==l.trim(),p=j&&""!==j.trim(),q=c&&"XXX"!==c&&"XX"!==c&&"XX"!==c,r=o||p,v=o||q,w=p||q;return g().useEffect(()=>{e&&e("XX")},[f,e]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-green-100 to-emerald-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(al.A,{size:20,className:"ml-4 text-secondary"}),"Kanwil"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Pilih Kanwil"}),q&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>e&&e("XX"),children:"Clear"})]}),(0,d.jsx)(ao.A,{value:c,onChange:e,kdlokasi:f,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihkanwil",isDisabled:r})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),p&&!v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>k&&k(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:v,onChange:a=>k&&k(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${w?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),o&&!w&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>m&&m(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: jakarta",className:"w-full min-w-0",size:"sm",value:l||"",isDisabled:w,onChange:a=>m&&m(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set(h?[h]:["1"]),onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),!b){i&&i("1");return}i&&i(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aq=c(62100),ar=c(24286);let as=({inquiryState:a})=>{let{kppn:b,setKppn:c,kanwil:e,kppnkondisi:f,setKppnkondisi:h,katakppn:i,setKatakppn:j,kppnradio:k,setKppnradio:l}=a,m=i&&""!==i.trim(),o=f&&""!==f.trim(),p=b&&"XXX"!==b&&"XX"!==b&&"XX"!==b,q=m||o,r=m||p,v=o||p;return g().useEffect(()=>{c&&c("XX")},[e,c]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-green-100 to-emerald-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aq.A,{size:20,className:"ml-4 text-secondary"}),"KPPN"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${q?"text-gray-400":"text-gray-700"}`,children:"Pilih KPPN"}),p&&!q&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(ar.A,{value:b,onChange:c||(()=>console.warn("setKppn is undefined")),kdkanwil:e,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih KPPN",status:"pilihkppn",isDisabled:q})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${r?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),o&&!r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:f||"",isDisabled:r,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),m&&!v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: medan",className:"w-full min-w-0",size:"sm",value:i||"",isDisabled:v,onChange:a=>j&&j(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:[k||"1"],onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),"string"==typeof b&&b.startsWith("$.")&&(b=b.replace("$.","")),!b){l&&l("1");return}l&&l(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var at=c(17313),au=c(72625);let av=({inquiryState:a})=>{let{satker:b,setSatker:c,dept:e,kdunit:f,prov:h,kppn:i,satkerkondisi:j,setSatkerkondisi:k,katasatker:l,setKatasatker:m,satkerradio:o,setSatkerradio:p}=a,q=l&&""!==l.trim(),r=j&&""!==j.trim(),v=b&&"XXX"!==b&&"XX"!==b,w=q||r,x=q||v,y=r||v;return g().useEffect(()=>{c&&c("XX")},[e,f,h,i,c]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-green-100 to-emerald-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(at.A,{size:20,className:"text-secondary ml-4"}),"Satker"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${w?"text-gray-400":"text-gray-700"}`,children:"Pilih Satker"}),v&&!w&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(au.A,{value:b,onChange:c||(()=>console.warn("setSatker is undefined")),kddept:e,kdunit:f,kdlokasi:h,kdkppn:i,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Satker",status:"pilihsatker",isDisabled:w})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${x?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),r&&!x&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>k&&k(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 647321,647322, dst",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:x,onChange:a=>k&&k(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${y?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),q&&!y&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>m&&m(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: universitas",className:"w-full min-w-0",size:"sm",value:l||"",isDisabled:y,onChange:a=>m&&m(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:[o||"1"],onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),"string"==typeof b&&b.startsWith("$.")&&(b=b.replace("$.","")),!b){p&&p("1");return}p&&p(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var aw=c(84027),ax=c(26238);let ay=({inquiryState:a})=>{let{akun:b,setAkun:c,akunkondisi:e,setAkunkondisi:f,kataakun:g,setKataakun:h,akunradio:i,setAkunradio:j,jenlap:k,jenis:l,kdakun:m,setAkunType:o,setAkunValue:p,setAkunSql:q}=a,r=e&&""!==e.trim(),v=g&&""!==g.trim();return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-green-100 to-emerald-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(aw.A,{size:20,className:"ml-4 text-secondary"}),"Akun"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsx)("div",{className:"flex items-center justify-between",children:(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Pilih Akun"})}),(0,d.jsx)(ax.A,{value:b&&b.type?b.type:b,onChange:a=>{c(a),o&&o(a.type),p&&p(a.value),q&&q(a.sql)},jenlap:k,jenis:l,kdakun:m,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Akun",status:"pilihakun",isDisabled:!1})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(ad.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(ag.A,{size:15})})})]}),r&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>f&&f(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:e||"",isDisabled:v,onChange:a=>f&&f(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Mengandung Kata"}),v&&(0,d.jsx)(n.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(s.r,{placeholder:"misalkan: gaji",className:"w-full min-w-0",size:"sm",value:g||"",isDisabled:r,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(t.d,{selectedKeys:i?[i]:["1"],onSelectionChange:a=>{let b=Array.from(a)[0];j&&j(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var az=c(40228);let aA=[{value:"1",label:"Januari"},{value:"2",label:"Februari"},{value:"3",label:"Maret"},{value:"4",label:"April"},{value:"5",label:"Mei"},{value:"6",label:"Juni"},{value:"7",label:"Juli"},{value:"8",label:"Agustus"},{value:"9",label:"September"},{value:"10",label:"Oktober"},{value:"11",label:"November"},{value:"12",label:"Desember"}],aB=({inquiryState:a})=>{let{cutoff:b,setCutoff:c}=a;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-green-100 to-emerald-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(az.A,{size:20,className:"ml-4 text-secondary"}),"Cut-Off"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsx)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Pilih Bulan Cutoff"}),(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(t.d,{"aria-label":"Select cutoff month",className:"w-full min-w-0 max-w-xs",size:"sm",selectedKeys:new Set([b]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&c(b)},isDisabled:!1,disallowEmptySelection:!0,placeholder:"Choose month",children:aA.map(a=>(0,d.jsx)(u.y,{value:a.value,children:a.label},a.value))}),(0,d.jsx)("p",{className:"text-xs text-gray-500 ml-3",children:"Wajib dipilih - Filter bulan selalu aktif untuk UP/TUP"})]})]})})})]})})},aC=({inquiryState:a})=>{let{cutoffFilter:b,setCutoffFilter:c,kddept:e,setKddept:f,unit:g,setUnit:h,kddekon:i,setKddekon:j,kdkanwil:k,setKdkanwil:l,kdkppn:m,setKdkppn:n,kdsatker:o,setKdsatker:p,kdakun:q,setKdakun:r}=a;return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-full p-3 mb-4 sm:p-4 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl",children:(0,d.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-6 2xl:grid-cols-8 gap-2 sm:gap-3",children:[(0,d.jsx)(ac,{id:"cutoff-filter",checked:!0,onChange:()=>{},label:"Bulan",disabled:!0}),(0,d.jsx)(ac,{id:"kddept-filter",checked:e,onChange:f,label:"Kementerian",disabled:!1}),(0,d.jsx)(ac,{id:"unit-filter",checked:g,onChange:h,label:"Eselon I",disabled:!1}),(0,d.jsx)(ac,{id:"dekon-filter",checked:i,onChange:j,label:"Kewenangan",disabled:!1}),(0,d.jsx)(ac,{id:"kanwil-filter",checked:k,onChange:l,label:"Kanwil",disabled:!1}),(0,d.jsx)(ac,{id:"kdkppn-filter",checked:m,onChange:n,label:"KPPN",disabled:!1}),(0,d.jsx)(ac,{id:"kdsatker-filter",checked:o,onChange:p,label:"Satker",disabled:!1}),(0,d.jsx)(ac,{id:"kdakun-filter",checked:q,onChange:r,label:"Akun",disabled:!1})]})}),(0,d.jsxs)("div",{className:"space-y-4 mb-4",children:[(0,d.jsx)(aB,{inquiryState:a}),e&&(0,d.jsx)(ah,{inquiryState:a}),g&&(0,d.jsx)(ak,{inquiryState:a}),i&&(0,d.jsx)(an,{inquiryState:a}),k&&(0,d.jsx)(ap,{inquiryState:a}),m&&(0,d.jsx)(as,{inquiryState:a}),o&&(0,d.jsx)(av,{inquiryState:a}),q&&(0,d.jsx)(ay,{inquiryState:a})]})]})};var aD=c(36220),aE=c(2840),aF=c(97840),aG=c(78122),aH=c(31158);let aI=({onExecuteQuery:a,onExportExcel:b,onExportCSV:c,onExportPDF:e,onReset:f,onSaveQuery:g,onShowSQL:h,isLoading:i})=>(0,d.jsx)(T.Z,{className:"mb-4 shadow-none bg-transparent",children:(0,d.jsx)(aD.U,{children:(0,d.jsxs)("div",{className:"flex flex-wrap gap-6 justify-center md:justify-center",children:[(0,d.jsx)(n.T,{color:"primary",startContent:(0,d.jsx)(aF.A,{size:16}),onClick:a,isLoading:i,className:"w-[160px] h-[50px]",children:"Tayang Data"}),(0,d.jsx)(n.T,{color:"danger",variant:"ghost",startContent:(0,d.jsx)(aG.A,{size:16}),onClick:f,isDisabled:i,className:"w-[160px] h-[50px]",children:"Reset Filter"}),(0,d.jsxs)(aE.x,{children:[(0,d.jsx)(n.T,{color:"success",variant:"flat",startContent:(0,d.jsx)(aH.A,{size:16}),onClick:b,isDisabled:i,className:"w-[120px] h-[50px]",children:"Excel"}),(0,d.jsx)(n.T,{color:"secondary",variant:"flat",startContent:(0,d.jsx)(aH.A,{size:16}),onClick:c,isDisabled:i,className:"w-[120px] h-[50px]",children:"CSV"})]}),(0,d.jsx)(n.T,{color:"success",variant:"flat",startContent:(0,d.jsx)(F.A,{size:16}),onClick:e,isDisabled:i,className:"w-[160px] h-[50px]",children:"Kirim WA"}),(0,d.jsx)(n.T,{color:"warning",variant:"flat",startContent:(0,d.jsx)(x.A,{size:16}),onClick:g,isDisabled:i,className:"w-[160px] h-[50px]",children:"Simpan Query"}),(0,d.jsx)(n.T,{color:"default",variant:"flat",startContent:(0,d.jsx)(I.A,{size:16}),onClick:h,isDisabled:i,className:"w-[160px] h-[50px]",children:"Tayang SQL"})]})})}),aJ=({inquiryState:a,onFilterChange:b})=>{let{thang:c,setThang:e,jenlap:f,setJenlap:h,pembulatan:i,setPembulatan:j}=a||{},[k,l]=g().useState("2025"),[m,n]=g().useState("1"),[o,p]=g().useState("1"),q=null!=c?c:k,r=null!=f?f:m,s=null!=i?i:o;g().useEffect(()=>{b&&b({thang:q,jenlap:r,pembulatan:s})},[q,r,s,b]);let v=a=>b=>{let c=Array.from(b)[0];a&&void 0!==c&&a(c)};return(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)("div",{className:"w-full p-3 mb-4 sm:p-4 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row gap-6 w-full",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("label",{id:"thang-label",className:"block text-sm font-medium mb-2",children:"Tahun Anggaran"}),(0,d.jsx)(t.d,{selectedKeys:[q],onSelectionChange:v(e||l),className:"w-full",placeholder:"Pilih Tahun",disallowEmptySelection:!0,"aria-labelledby":"thang-label","aria-label":"Pilih Tahun Anggaran",children:["2025","2024","2023","2022","2021","2020","2019"].map(a=>(0,d.jsx)(u.y,{textValue:a,children:a},a))})]}),(0,d.jsxs)("div",{className:"flex-[1.5]",children:[(0,d.jsx)("label",{id:"jenlap-label",className:"block text-sm font-medium mb-2",children:"Jenis Laporan"}),(0,d.jsx)(t.d,{selectedKeys:[r],onSelectionChange:v(h||n),className:"w-full",placeholder:"Pilih Jenis Laporan",disallowEmptySelection:!0,"aria-labelledby":"jenlap-label","aria-label":"Pilih Jenis Laporan",children:[{value:"1",label:"Outstanding UP/TUP"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("label",{id:"pembulatan-label",className:"block text-sm font-medium mb-2",children:"Pembulatan"}),(0,d.jsx)(t.d,{selectedKeys:[s],onSelectionChange:v(j||p),className:"w-full",placeholder:"Pilih Pembulatan",disallowEmptySelection:!0,"aria-labelledby":"pembulatan-label","aria-label":"Pilih Pembulatan",children:[{value:"1",label:"Rupiah"},{value:"1000",label:"Ribuan"},{value:"1000000",label:"Jutaan"},{value:"1000000000",label:"Miliar"},{value:"1000000000000",label:"Triliun"}].map(a=>(0,d.jsx)(u.y,{textValue:a.label,children:a.label},a.value))})]})]})})})};class aK{constructor(a,b,c=null){this.fieldName=a,this.tableName=b,this.referenceTable=c}buildColumns(a,b="",c={}){let d={columns:[],joinClause:"",groupBy:[]};if(!a)return d;let e=`a.${this.fieldName}`,f=this.referenceTable?`${this.referenceTable.alias}.${this.referenceTable.nameField}`:null,g=c&&"1"===c.jenlap?"a.pagu_apbn":"a.pagu";switch(a){case"1":d.columns.push(e),d.groupBy.push(e);break;case"2":d.columns.push(e),f&&(d.columns.push(f),d.joinClause=this.buildJoinClause(b)),d.groupBy.push(e);break;case"3":f&&(d.columns.push(f),d.joinClause=this.buildJoinClause(b)),d.groupBy.push(e)}return d.paguField=g,d}buildJoinClause(a=""){if(!this.referenceTable)return"";let b=this.referenceTable.hasYear?`_${a}`:"",c=`${this.referenceTable.schema}.${this.referenceTable.table}${b}`;return` LEFT JOIN ${c} ${this.referenceTable.alias} ON ${this.referenceTable.joinCondition}`}buildWhereConditions(a){let b=[],{pilihValue:c,kondisiValue:d,kataValue:e,opsiType:f,defaultValues:g=["XXX","000","XX","00","XXXX","0000","XXXXXX","000000"]}=a;if(e&&""!==e.trim()){let a=this.referenceTable?`${this.referenceTable.alias}.${this.referenceTable.nameField}`:`a.${this.fieldName}`;b.push(`${a} LIKE '%${e}%'`)}else d&&""!==d.trim()?b.push(this.parseKondisiConditions(d)):c&&!g.includes(c)&&b.push(`a.${this.fieldName} = '${c}'`);return b.filter(a=>a&&""!==a.trim())}parseKondisiConditions(a){if(!a||""===a.trim())return"";let b=`a.${this.fieldName}`;if("!"===a.substring(0,1)){let c=a.substring(1).split(",").map(a=>a.trim()).filter(a=>""!==a);if(c.length>0){let a=c.map(a=>`'${a}'`).join(",");return`${b} NOT IN (${a})`}}else if(a.includes("%"))return`${b} LIKE '${a}'`;else if(a.includes("-")&&!a.includes(",")){let[c,d]=a.split("-").map(a=>a.trim());if(c&&d)return`${b} BETWEEN '${c}' AND '${d}'`}else{let c=a.split(",").map(a=>a.trim()).filter(a=>""!==a);if(c.length>0){let a=c.map(a=>`'${a}'`).join(",");return`${b} IN (${a})`}}return""}build(a,b=""){let{isEnabled:c,radio:d,pilihValue:e,kondisiValue:f,kataValue:g,opsiType:h}=a,i={columns:[],joinClause:"",groupBy:[],whereConditions:[]};if(!c)return i;let j=this.buildColumns(d,b);if(i.columns=j.columns,i.joinClause=j.joinClause,i.groupBy=j.groupBy,g&&""!==g.trim()&&this.referenceTable){let a=`${this.referenceTable.alias}.${this.referenceTable.nameField}`;i.joinClause||(i.joinClause=this.buildJoinClause(b)),i.columns.includes(a)||i.columns.push(a)}return i.whereConditions=this.buildWhereConditions({pilihValue:e,kondisiValue:f,kataValue:g,opsiType:h}),i}getEmptyResult(){return{columns:[],joinClause:"",whereConditions:[],groupBy:[]}}}let aL=aK;class aM extends aL{constructor(){super("kddept","department",{schema:"dbref",table:"t_dept",alias:"b",nameField:"nmdept",hasYear:!0,joinCondition:"a.kddept=b.kddept"})}buildFromState(a){let{kddept:b,dept:c,deptkondisi:d,katadept:e,deptradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aN extends aL{constructor(){super("kdunit","unit",{schema:"dbref",table:"t_unit",alias:"c",nameField:"nmunit",hasYear:!0,joinCondition:"a.kddept=c.kddept AND a.kdunit=c.kdunit"})}buildFromState(a){let{unit:b,kdunit:c,unitkondisi:d,kataunit:e,unitradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aO extends aL{constructor(){super("kddekon","dekonsentrasi",{schema:"dbref",table:"t_dekon",alias:"d",nameField:"nmdekon",hasYear:!0,joinCondition:"a.kddekon=d.kddekon"})}buildFromState(a){let{kddekon:b,dekon:c,dekonkondisi:d,katadekon:e,dekonradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aP extends aL{constructor(){super("kdsatker","satker",{schema:"dbref",table:"t_satker",alias:"s",nameField:"nmsatker",hasYear:!0,joinCondition:"a.kddept=s.kddept AND a.kdunit=s.kdunit AND a.kdsatker=s.kdsatker"})}buildFromState(a){let{kdsatker:b,satker:c,satkerkondisi:d,katasatker:e,satkerradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aQ extends aL{constructor(){super("kdkanwil","kanwil",{schema:"dbref",table:"t_kanwil",alias:"kw",nameField:"nmkanwil",hasYear:!0,joinCondition:"a.kdkanwil=kw.kdkanwil"})}buildFromState(a){let{kdkanwil:b,kanwil:c,kanwilkondisi:d,katakanwil:e,kanwilradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aR extends aL{constructor(){super("kdkppn","kppn",{schema:"dbref",table:"t_kppn",alias:"kp",nameField:"nmkppn",hasYear:!0,joinCondition:"a.kdkppn=kp.kdkppn"})}buildFromState(a){let{kdkppn:b,kppn:c,kppnkondisi:d,katakppn:e,kppnradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aS extends aL{constructor(){super("kdakun","akun",{schema:"dbref",table:"t_akun",alias:"ak",nameField:"nmakun",hasYear:!0,joinCondition:"a.kdakun=ak.kdakun"})}buildFromState(a){let{kdakun:b,akun:c,akunkondisi:d,kataakun:e,akunradio:f,thang:g}=a;if(b&&"4"===f)return{columns:[],groupBy:[],joinClause:"",whereConditions:[]};if(b&&("BKPK"===c||"JENBEL"===c)){let a="BKPK"===c?4:2,b=this.build({isEnabled:!0,radio:f,pilihValue:"",kondisiValue:"",kataValue:""},g);if("BKPK"===c){let c=`dbref.t_bkpk_${g}`;if("3"===f?(b.columns=["bk.nmbkpk"],b.joinClause=` LEFT JOIN ${c} bk ON LEFT(a.kdakun,${a}) = bk.kdbkpk`,b.groupBy=[`LEFT(a.kdakun,${a})`]):"2"===f?(b.columns=[`LEFT(a.kdakun,${a}) AS kdbkpk`,"bk.nmbkpk"],b.joinClause=` LEFT JOIN ${c} bk ON LEFT(a.kdakun,${a}) = bk.kdbkpk`,b.groupBy=[`LEFT(a.kdakun,${a})`]):(b.columns=[`LEFT(a.kdakun,${a}) AS kdbkpk`],b.groupBy=[`LEFT(a.kdakun,${a})`],b.joinClause=""),d&&/^[0-9]+$/.test(d)){let a=d.length;b.whereConditions=[`LEFT(a.kdakun,${a}) IN ('${d}')`]}e&&""!==e.trim()&&(b.whereConditions=[`bk.nmbkpk LIKE '%${e.trim()}%'`])}else if("JENBEL"===c){let c=`dbref.t_gbkpk_${g}`;if("3"===f?(b.columns=["gb.nmgbkpk"],b.joinClause=` LEFT JOIN ${c} gb ON LEFT(a.kdakun,${a}) = gb.kdgbkpk`,b.groupBy=[`LEFT(a.kdakun,${a})`]):"2"===f?(b.columns=[`LEFT(a.kdakun,${a}) AS kdgbkpk`,"gb.nmgbkpk"],b.joinClause=` LEFT JOIN ${c} gb ON LEFT(a.kdakun,${a}) = gb.kdgbkpk`,b.groupBy=[`LEFT(a.kdakun,${a})`]):(b.columns=[`LEFT(a.kdakun,${a}) AS kdgbkpk`],b.groupBy=[`LEFT(a.kdakun,${a})`],b.joinClause=""),d&&/^[0-9]+$/.test(d)){let a=d.length;b.whereConditions=[`LEFT(a.kdakun,${a}) IN ('${d}')`]}e&&""!==e.trim()&&(b.whereConditions=[`gb.nmgbkpk LIKE '%${e.trim()}%'`])}return b}if(b&&("AKUN"===c||!c)&&!d&&!e)return this.build({isEnabled:!0,radio:f,pilihValue:"",kondisiValue:"",kataValue:""},g);if(b&&d&&/^[0-9]+$/.test(d)){let a=d.length,c=`LEFT(a.kdakun,${a}) IN ('${d}')`;return{...this.build({isEnabled:b,radio:f,pilihValue:"",kondisiValue:"",kataValue:e},g),whereConditions:[c]}}if(b&&e&&""!==e.trim()){let a=`ak.nmakun LIKE '%${e.trim()}%'`;return{...this.build({isEnabled:b,radio:f,pilihValue:"",kondisiValue:"",kataValue:e},g),whereConditions:[a]}}return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aT{constructor(){this.filters={department:new aM,unit:new aN,dekon:new aO,satker:new aP,kanwil:new aQ,kppn:new aR,akun:new aS},this.filterStateMap={department:"kddept",unit:"unit",dekon:"kddekon",kanwil:"kdkanwil",kppn:"kdkppn",satker:"kdsatker",akun:"kdakun"}}isFilterEnabled(a,b){let c=this.filterStateMap[a],d=!!c&&!0===b[c];return console.log(`🔍 isFilterEnabled ${a}:`,{stateKey:c,stateValue:b[c],isEnabled:d}),d}buildFilter(a,b){let c=this.filters[a];if(!c)throw Error(`UPTUP Filter ${a} not found`);return c.buildFromState?c.buildFromState(b):c.build(b)}buildAllFilters(a){let b={columns:[],joinClauses:[],groupBy:[],whereConditions:[]};return Object.entries(this.filters).forEach(([c,d])=>{let e=this.isFilterEnabled(c,a);if(console.log(`🔍 UPTUP Filter ${c}:`,{enabled:e,stateKey:this.filterStateMap[c]}),e)try{let e=d.buildFromState?d.buildFromState(a):d.build(a);console.log(`✅ UPTUP Filter ${c} result:`,{columns:e.columns,joinClause:e.joinClause,whereConditions:e.whereConditions,groupBy:e.groupBy}),e.columns&&b.columns.push(...e.columns),e.joinClause&&b.joinClauses.push(e.joinClause),e.groupBy&&b.groupBy.push(...e.groupBy),e.whereConditions&&e.whereConditions.length>0&&b.whereConditions.push(...e.whereConditions)}catch(a){console.warn(`❌ Error building UPTUP filter ${c}:`,a)}}),b.columns=[...new Set(b.columns)],b.joinClauses=[...new Set(b.joinClauses)],b.groupBy=[...new Set(b.groupBy)],console.log("\uD83D\uDD0D UPTUP buildAllFilters final result:",b),b}optimizeJoins(a){return[...new Set(a.filter(a=>a&&a.trim()))].sort((a,b)=>{let c=a.includes("LEFT JOIN"),d=b.includes("LEFT JOIN");return c&&!d?-1:!c&&d?1:a.localeCompare(b)})}getFilterStats(a){let b=Object.keys(this.filters).filter(b=>this.isFilterEnabled(b,a));return{totalFilters:Object.keys(this.filters).length+1,enabledFilters:b.length+1,enabledFilterNames:["bulan",...b],disabledFilters:Object.keys(this.filters).length-b.length}}getAvailableFilters(){return["bulan",...Object.keys(this.filters)]}validateConfiguration(a){let b=this.getFilterStats(a),c=[],d=[];return 1===b.enabledFilters&&d.push("Only Bulan filter is active. Consider adding more filters for specific analysis."),b.enabledFilters>6&&d.push("Consider reducing the number of active filters for better performance."),a.kddept&&a.unit&&a.kdsatker&&d.push("Using Kementerian, Eselon I, and Satker together provides very specific UP/TUP filtering."),a.kdkanwil&&a.kdkppn&&d.push("Using both Kanwil and KPPN filters provides location-specific UP/TUP analysis."),{isValid:0===c.length,warnings:c,recommendations:d,stats:b}}}class aU{constructor(){this.filterBuilder=new aT}buildQuery(a){let b=performance.now();try{let c=this.getBaseTable(a),d=this.buildSelectClause(a),e=this.buildFromClause(a,c),f=this.buildWhereClause(a),g=this.buildGroupByClause(a),h=this.buildOrderByClause(a),i=`${d} ${e} ${f} ${g} ${h}`,j=performance.now();return console.log(`🚀 UPTUP Query built in ${(j-b).toFixed(2)}ms`),i.trim()}catch(a){return console.error("Error building UPTUP query:",a),""}}getBaseTable(a){let{thang:b}=a;return`monev${b}.pa_up_${b}_outstanding`}buildSelectClause(a){let{pembulatan:b}=a,c=this.filterBuilder.buildAllFilters(a),d=c.columns;console.log("\uD83D\uDD0D buildSelectClause - filterResult:",c),console.log("\uD83D\uDD0D buildSelectClause - filterColumns:",d);let e=[],f=[...new Set([...d,...e])];console.log("\uD83D\uDD0D buildSelectClause - baseColumns:",e),console.log("\uD83D\uDD0D buildSelectClause - allColumns:",f);let g=[`ROUND(SUM(a.rupiah)/${b},0) AS rupiah`],h=`SELECT ${f.length>0?f.join(", ")+", ":""}${g.join(", ")}`;return console.log("\uD83D\uDD0D buildSelectClause - final SELECT:",h),h}buildFromClause(a,b){let c=this.filterBuilder.buildAllFilters(a),d=this.filterBuilder.optimizeJoins(c.joinClauses),e=`FROM ${b} a`;return d.length>0&&(e+=` ${d.join(" ")}`),e}buildWhereClause(a){let{cutoff:b}=a,c=[];if(b&&"0"!==b){let a=`a.bulan <= ${b}`;c.push(a),console.log("\uD83D\uDD0D UPTUP Query - Adding cutoff condition:",a)}let d=[...c,...this.filterBuilder.buildAllFilters(a).whereConditions];return console.log("\uD83D\uDD0D UPTUP Query - All WHERE conditions:",d),d.length>0?`WHERE ${d.join(" AND ")}`:""}buildGroupByClause(a){let b=this.filterBuilder.buildAllFilters(a);console.log("\uD83D\uDD0D buildGroupByClause - filterResult.groupBy:",b.groupBy);let c=b.groupBy||[];console.log("\uD83D\uDD0D buildGroupByClause - filterGroupBy:",c);let d=c.length>0?`GROUP BY ${c.join(", ")}`:"";return console.log("\uD83D\uDD0D buildGroupByClause - final GROUP BY:",d),d}buildOrderByClause(a){return""}generateSqlPreview(a){let b=this.getBaseTable(a),c=this.buildSelectClause(a),d=this.buildFromClause(a,b),e=this.buildWhereClause(a),f=this.buildGroupByClause(a),g=this.buildOrderByClause(a);return{selectClause:c,fromClause:d,whereClause:e,groupByClause:f,orderByClause:g,fullQuery:`${c} ${d} ${e} ${f} ${g}`.trim()}}validateQuery(a){let b=[],c=[];if(!a||"string"!=typeof a)return c.push("Query is empty or invalid"),{isValid:!1,errors:c,warnings:b};a.includes("SELECT")||c.push("Query missing SELECT clause"),a.includes("FROM")||c.push("Query missing FROM clause"),a.length>1e4&&b.push("Query is very long, may impact performance");let d=(a.match(/JOIN/g)||[]).length;return d>10&&b.push(`Query has ${d} joins, may impact performance`),{isValid:0===c.length,errors:c,warnings:b,stats:{queryLength:a.length,joinCount:d,hasGroupBy:a.includes("GROUP BY"),hasOrderBy:a.includes("ORDER BY")}}}getQueryPerformanceMetrics(a){let b=performance.now();try{let c=this.filterBuilder.getFilterStats(a),d=this.filterBuilder.validateConfiguration(a),e=this.buildQuery(a),f=this.validateQuery(e);return{buildTime:performance.now()-b,filterStats:c,validation:d,queryValidation:f,recommendations:[...d.recommendations,...f.warnings]}}catch(a){return{buildTime:performance.now()-b,error:a.message,filterStats:{enabledFilters:0},validation:{isValid:!1,warnings:[a.message]},recommendations:["Fix query building errors before proceeding"]}}}}let aV=()=>{let a=function(){let{role:a,telp:b,verified:c,loadingExcell:d,setloadingExcell:e,kdkppn:g,kdkanwil:i,settampilAI:j}=(0,f.useContext)(h.A),[k,l]=(0,f.useState)(!1),[m,n]=(0,f.useState)(!1),[o,p]=(0,f.useState)(!1),[q,r]=(0,f.useState)(!1),[s,t]=(0,f.useState)(!1),[u,v]=(0,f.useState)(!1),[w,x]=(0,f.useState)(!1),[y,z]=(0,f.useState)(!1),[A,B]=(0,f.useState)(!1),[C,D]=(0,f.useState)(!1),[E,F]=(0,f.useState)(!1),[G,H]=(0,f.useState)(!1),[I,J]=(0,f.useState)("1"),[K,L]=(0,f.useState)(new Date().getFullYear().toString()),[M,N]=(0,f.useState)("1"),[O,P]=(0,f.useState)("pdf"),[Q,R]=(0,f.useState)(!1),[S,T]=(0,f.useState)(!1),[U,V]=(0,f.useState)(!1),[W,X]=(0,f.useState)((new Date().getMonth()+1).toString()),[Y,Z]=(0,f.useState)(!0),[$,_]=(0,f.useState)(!0),[aa,ab]=(0,f.useState)(!1),[ac,ad]=(0,f.useState)(!1),[ae,af]=(0,f.useState)(!1),[ag,ah]=(0,f.useState)(!1),[ai,aj]=(0,f.useState)(!1),[ak,al]=(0,f.useState)(!1),[am,an]=(0,f.useState)("000"),[ao,ap]=(0,f.useState)(""),[aq,ar]=(0,f.useState)(""),[as,at]=(0,f.useState)("XX"),[au,av]=(0,f.useState)(""),[aw,ax]=(0,f.useState)(""),[ay,az]=(0,f.useState)("XX"),[aA,aB]=(0,f.useState)(""),[aC,aD]=(0,f.useState)(""),[aE,aF]=(0,f.useState)("XX"),[aG,aH]=(0,f.useState)(""),[aI,aJ]=(0,f.useState)(""),[aK,aL]=(0,f.useState)("XX"),[aM,aN]=(0,f.useState)(""),[aO,aP]=(0,f.useState)(""),[aQ,aR]=(0,f.useState)("XX"),[aS,aT]=(0,f.useState)(""),[aU,aV]=(0,f.useState)(""),[aW,aX]=(0,f.useState)("AKUN"),[aY,aZ]=(0,f.useState)(""),[a$,a_]=(0,f.useState)(""),[a0,a1]=(0,f.useState)("1"),[a2,a3]=(0,f.useState)("1"),[a4,a5]=(0,f.useState)("1"),[a6,a7]=(0,f.useState)("1"),[a8,a9]=(0,f.useState)("1"),[ba,bb]=(0,f.useState)("1"),[bc,bd]=(0,f.useState)("1"),[be,bf]=(0,f.useState)("pilihdept"),[bg,bh]=(0,f.useState)(""),[bi,bj]=(0,f.useState)(""),[bk,bl]=(0,f.useState)(", round(sum(rupiah)/1,0) as rupiah");return{role:a,telp:b,verified:c,loadingExcell:d,setloadingExcell:e,kodekppn:g,kodekanwil:i,settampilAI:j,showModal:k,setShowModal:l,showModalKedua:m,setShowModalKedua:n,showModalsql:o,setShowModalsql:p,showModalApbn:q,setShowModalApbn:r,showModalAkumulasi:s,setShowModalAkumulasi:t,showModalBulanan:u,setShowModalBulanan:v,showModalBlokir:w,setShowModalBlokir:x,showModalPN:y,setShowModalPN:z,showModalPN2:A,setShowModalPN2:B,showModalJnsblokir:C,setShowModalJnsblokir:D,showModalPDF:E,setShowModalPDF:F,showModalsimpan:G,setShowModalsimpan:H,jenlap:I,setJenlap:J,thang:K,setThang:L,pembulatan:M,setPembulatan:N,selectedFormat:O,setSelectedFormat:P,export2:Q,setExport2:R,loadingStatus:S,setLoadingStatus:T,showFormatDropdown:U,setShowFormatDropdown:V,cutoff:W,setCutoff:X,cutoffFilter:Y,setCutoffFilter:Z,kddept:$,setKddept:_,unit:aa,setUnit:ab,kddekon:ac,setKddekon:ad,kdkanwil:ae,setKdkanwil:af,kdkppn:ag,setKdkppn:ah,kdsatker:ai,setKdsatker:aj,kdakun:ak,setKdakun:al,dept:am,setDept:an,deptkondisi:ao,setDeptkondisi:ap,katadept:aq,setKatadept:ar,kdunit:as,setKdunit:at,unitkondisi:au,setUnitkondisi:av,kataunit:aw,setKataunit:ax,dekon:ay,setDekon:az,dekonkondisi:aA,setDekonkondisi:aB,katadekon:aC,setKatadekon:aD,kanwil:aE,setKanwil:aF,kanwilkondisi:aG,setKanwilkondisi:aH,katakanwil:aI,setKatakanwil:aJ,kppn:aK,setKppn:aL,kppnkondisi:aM,setKppnkondisi:aN,katakppn:aO,setKatakppn:aP,satker:aQ,setSatker:aR,satkerkondisi:aS,setSatkerkondisi:aT,katasatker:aU,setKatasatker:aV,akun:aW,setAkun:aX,akunkondisi:aY,setAkunkondisi:aZ,kataakun:a$,setKataakun:a_,deptradio:a0,setDeptradio:a1,unitradio:a2,setUnitradio:a3,dekonradio:a4,setDekonradio:a5,kanwilradio:a6,setKanwilradio:a7,kppnradio:a8,setKppnradio:a9,satkerradio:ba,setSatkerradio:bb,akunradio:bc,setAkunradio:bd,opsidept:be,setOpsidept:bf,sql:bg,setSql:bh,from:bi,setFrom:bj,select:bk,setSelect:bl}}(),{statusLogin:b,token:c,axiosJWT:i}=(0,f.useContext)(h.A),{buildQuery:j}=function(a){let[b,c]=(0,f.useState)({}),d=(0,f.useMemo)(()=>new aU,[]),{thang:e,jenlap:g,pembulatan:h,cutoff:i,cutoffFilter:j,kddept:k,unit:l,kddekon:m,kdkanwil:n,kdkppn:o,kdsatker:p,kdakun:q,dept:r,kdunit:s,dekon:t,kanwil:u,kppn:v,satker:w,akun:x,deptkondisi:y,unitkondisi:z,dekonkondisi:A,kanwilkondisi:B,kppnkondisi:C,satkerkondisi:D,akunkondisi:E,deptradio:F,unitradio:G,dekonradio:H,kanwilradio:I,kppnradio:J,satkerradio:K,akunradio:L,setFrom:M,setSelect:N,setSql:O}=a,P=()=>{try{let b=d.buildQuery(a),c=d.generateSqlPreview(a);return M&&M(c.fromClause),N&&N(c.selectClause),O&&O(b),b}catch(a){return console.error("Error building query:",a),""}},Q=()=>d.getQueryPerformanceMetrics(a),R=()=>d.generateSqlPreview(a),S=(a=P)=>d.validateQuery(a),T=()=>d.filterBuilder.getFilterStats(a),U=b=>d.filterBuilder.isFilterEnabled(b,a),V=b=>d.filterBuilder.buildFilter(b,a);return{buildQuery:P,getBuildQuery:()=>P,generateSqlPreview:R,validateQuery:S,getQueryPerformanceMetrics:Q,getFilterStats:T,analyzeQueryComplexity:()=>{let a=Q(),b=T();return{complexity:{low:b.enabledFilters<=3&&a.validation.stats.joinCount<=3,medium:b.enabledFilters<=6&&a.validation.stats.joinCount<=6,high:b.enabledFilters>6||a.validation.stats.joinCount>6},metrics:a,stats:b,recommendations:a.recommendations}},isFilterEnabled:U,getAvailableFilters:()=>d.filterBuilder.getAvailableFilters(),buildFilter:V,debugFilter:a=>{let b=V(a),c=U(a);return console.log(`🔍 Debug Filter: ${a}`,{isEnabled:c,columns:b.columns,joinClause:b.joinClause,whereConditions:b.whereConditions,groupBy:b.groupBy}),{filterName:a,isEnabled:c,...b}},getCachedQuery:a=>b[a],setCachedQuery:(a,b)=>{c(c=>({...c,[a]:{query:b,timestamp:Date.now()}}))},clearQueryCache:()=>{c({})},generateSqlPreview:R,generateOptimizedSql:()=>P,parseAdvancedConditions:(a,b)=>{try{return d.filterBuilder.filters.department.parseKondisiConditions(a)}catch(a){return console.warn("Error parsing advanced conditions:",a),[]}},optimizeGroupBy:(a,b)=>[...new Set(b)].filter(b=>a.some(a=>a.includes(b)||b.includes("a."))),optimizeJoins:a=>d.filterBuilder.optimizeJoins(Array.isArray(a)?a:[a]),validateQuery:S,getQueryPerformanceMetrics:Q,getQueryStats:T}}(a),{role:k,telp:l,verified:m,loadingExcell:n,setloadingExcell:o,kodekppn:p,kodekanwil:q,settampilAI:s,showModal:t,setShowModal:u,showModalKedua:v,setShowModalKedua:w,showModalsql:x,setShowModalsql:y,showModalPDF:z,setShowModalPDF:A,showModalsimpan:C,setShowModalsimpan:D,jenlap:E,setJenlap:F,thang:G,setThang:H,pembulatan:I,setPembulatan:J,selectedFormat:K,setSelectedFormat:M,export2:O,setExport2:P,loadingStatus:Q,setLoadingStatus:R,showFormatDropdown:S,setShowFormatDropdown:T,cutoff:U,setCutoff:V,cutoffFilter:W,setCutoffFilter:X,kddept:Y,setKddept:Z,unit:$,setUnit:_,kddekon:ab,setKddekon:ac,kdkanwil:ad,setKdkanwil:ae,kdkppn:af,setKdkppn:ag,kdsatker:ah,setKdsatker:ai,kdakun:aj,setKdakun:ak,dept:al,setDept:am,deptkondisi:an,setDeptkondisi:ao,katadept:ap,setKatadept:aq,kdunit:ar,setKdunit:as,unitkondisi:at,setUnitkondisi:au,kataunit:av,setKataunit:aw,dekon:ax,setDekon:ay,dekonkondisi:az,setDekonkondisi:aA,katadekon:aB,setKatadekon:aD,kanwil:aE,setKanwil:aF,kanwilkondisi:aG,setKanwilkondisi:aH,katakanwil:aK,setKatakanwil:aL,kppn:aM,setKppn:aN,kppnkondisi:aO,setKppnkondisi:aP,katakppn:aQ,setKatakppn:aR,satker:aS,setSatker:aT,satkerkondisi:aV,setSatkerkondisi:aW,katasatker:aX,setKatasatker:aY,akun:aZ,setAkun:a$,akunkondisi:a_,setAkunkondisi:a0,kataakun:a1,setKataakun:a2,deptradio:a3,setDeptradio:a4,unitradio:a5,setUnitradio:a6,dekonradio:a7,setDekonradio:a8,kanwilradio:a9,setKanwilradio:ba,kppnradio:bb,setKppnradio:bc,satkerradio:bd,setSatkerradio:be,akunradio:bf,setAkunradio:bg,sql:bh,setSql:bi,from:bj,setFrom:bk,select:bl,setSelect:bm}=a;console.log("=== FormInquiryUptup Debug ==="),console.log("cutoffFilter state:",W),console.log("cutoff state:",U),console.log("kddept state:",Y),console.log("unit state:",$),console.log("kddekon state:",ab),console.log("=== End FormInquiryUptup Debug ===");let bn=()=>{console.log("\uD83D\uDD0D generateUnifiedQuery - Debug inquiry state:",{jenlap:a.jenlap,dept:a.dept,kddept:a.kddept,unit:a.unit,kddekon:a.kddekon,dekon:a.dekon,thang:a.thang,cutoff:a.cutoff,cutoffFilter:a.cutoffFilter,timestamp:new Date().toISOString()});let b=j();return"string"==typeof b&&b.length>0?console.log("\uD83D\uDD04 Query Generated:",b.substring(0,600)):console.log("\uD83D\uDD04 Query Generated: (empty or invalid)"),b},bo=async()=>{let b=bn();a.setSql(b),u(!0)};g().useEffect(()=>{j()},[G,I]),g().useRef(!1);let[bp,bq]=g().useState(!1);async function br(){console.log("fetchExportData called");let a=bn();if(!a||"string"!=typeof a||""===a.trim())return(0,e.qs)("Query tidak valid, silakan cek filter dan parameter."),console.error("Export aborted: SQL query is empty or invalid.",{sql:a}),[];if(!b)return console.log("Not logged in, cannot export data."),[];try{let b=await i.post("http://localhost:88/next/inquiry",{sql:a,page:1},{headers:{Authorization:`Bearer ${c}`}});if(console.log("[Export Debug] Backend response:",b.data),b.data&&Array.isArray(b.data.data))return b.data.data;return[]}catch(a){return console.error("Export API error:",a),a&&a.response&&console.error("[Export Debug] Backend error response:",a.response.data),[]}}let bs=async()=>{o(!0);try{let a=await br();if(!a||!a.length){(0,e.qs)("Tidak ada data untuk diexport"),o(!1);return}await L(a,"inquiry_data.xlsx"),(0,e.qs)("Data berhasil diexport ke Excel")}catch(a){(0,e.qs)("Gagal export Excel")}o(!1)},bt=async()=>{o(!0);try{let a=await br();if(!a||!a.length){(0,e.qs)("Tidak ada data untuk diexport"),o(!1);return}!function(a,b="data.csv"){if(!a||!a.length)return;let c=(a,b)=>null==b?"":b,d=Object.keys(a[0]),e=new Blob([[d.join(","),...a.map(a=>d.map(b=>JSON.stringify(a[b],c)).join(","))].join("\r\n")],{type:"text/csv"}),f=URL.createObjectURL(e),g=document.createElement("a");g.setAttribute("href",f),g.setAttribute("download",b),g.style.visibility="hidden",document.body.appendChild(g),g.click(),document.body.removeChild(g),URL.revokeObjectURL(f)}(a,"inquiry_data.csv"),(0,e.qs)("Data berhasil diexport ke CSV")}catch(a){(0,e.qs)("Gagal export CSV")}o(!1)};return(0,d.jsxs)("div",{className:"w-full",children:[(0,d.jsxs)("div",{className:"xl:px-8 p-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Inquiry Outstanding UP/TUP"}),(0,d.jsx)(aJ,{inquiryState:{jenlap:E,setJenlap:F,pembulatan:I,setPembulatan:J,thang:G,setThang:H}}),(0,d.jsx)(aC,{inquiryState:{jenlap:E,cutoff:U,setCutoff:V,cutoffFilter:W,setCutoffFilter:X,kddept:Y,setKddept:Z,unit:$,setUnit:_,kddekon:ab,setKddekon:ac,kdkanwil:ad,setKdkanwil:ae,kdkppn:af,setKdkppn:ag,kdsatker:ah,setKdsatker:ai,kdakun:aj,setKdakun:ak,dept:al,setDept:am,deptkondisi:an,setDeptkondisi:ao,katadept:ap,setKatadept:aq,deptradio:a3,setDeptradio:a4,kdunit:ar,setKdunit:as,unitkondisi:at,setUnitkondisi:au,kataunit:av,setKataunit:aw,unitradio:a5,setUnitradio:a6,dekon:ax,setDekon:ay,dekonkondisi:az,setDekonkondisi:aA,katadekon:aB,setKatadekon:aD,dekonradio:a7,setDekonradio:a8,kanwil:aE,setKanwil:aF,kanwilkondisi:aG,setKanwilkondisi:aH,katakanwil:aK,setKatakanwil:aL,kanwilradio:a9,setKanwilradio:ba,kppn:aM,setKppn:aN,kppnkondisi:aO,setKppnkondisi:aP,katakppn:aQ,setKatakppn:aR,kppnradio:bb,setKppnradio:bc,satker:aS,setSatker:aT,satkerkondisi:aV,setSatkerkondisi:aW,katasatker:aX,setKatasatker:aY,satkerradio:bd,setSatkerradio:be,akun:aZ,setAkun:a$,akunkondisi:a_,setAkunkondisi:a0,kataakun:a1,setKataakun:a2,akunradio:bf,setAkunradio:bg}}),(0,d.jsx)("div",{className:"my-3 sm:px-16",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row md:flex-wrap lg:flex-nowrap gap-2 border-2 dark:border-zinc-600 rounded-xl shadow-sm py-2 px-4 font-mono tracking-wide bg-zinc-100 dark:bg-black",children:[(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-blue-600 ml-4",children:"Tahun Anggaran:"}),(0,d.jsx)("span",{className:"ml-2",children:G})]}),(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-green-600 ml-4",children:"Jenis Laporan:"}),(0,d.jsx)("span",{className:"ml-2",children:"1"===E?"Outstanding UP/TUP":""})]}),(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-purple-600 ml-4",children:"Pembulatan:"}),(0,d.jsx)("span",{className:"ml-2",children:"1"===I?"Rupiah":"1000"===I?"Ribuan":"1000000"===I?"Jutaan":"1000000000"===I?"Miliaran":"Triliunan"})]}),(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-orange-600 ml-4",children:"Filter Aktif:"}),(0,d.jsxs)("span",{className:"ml-2",children:[[!0,Y,$,ab,ad,af,ah,aj].filter(Boolean).length," ","dari"," ",8," ","(Bulan: Wajib)"]})]})]})}),(0,d.jsx)(aI,{onExecuteQuery:bo,onExportExcel:bs,onExportCSV:bt,onExportPDF:()=>{A(!0)},onReset:()=>{F("1"),H(new Date().getFullYear().toString()),V((new Date().getMonth()+1).toString()),Z(!0),_(!1),ac(!1),ae(!1),ag(!1),ai(!1),ak(!1),am("000"),as("XX"),ay("XX"),aF("XX"),aN("XX"),aP(""),aR(""),aT("XX"),aW(""),aY(""),a$("AKUN"),J("1"),a4("1"),a6("1"),a8("1"),ba("1"),bc("1"),be("1"),bg("1"),bi(""),bk(""),bm(", round(sum(rupiah)/1,0) as rupiah")},isLoading:n,onSaveQuery:()=>bq(!0),onShowSQL:()=>{console.log("\uD83D\uDD0D handlegetQuerySQL - Debug inquiry state:",{jenlap:a.jenlap,cutoff:a.cutoff,cutoffFilter:a.cutoffFilter,type:typeof a.jenlap,timestamp:new Date().toISOString()});let b=bn();a.setSql(b),y(!0)}})]}),x&&(0,d.jsx)(r,{isOpen:x,onClose:()=>{y(!1),window.scrollTo({top:0,behavior:"smooth"})},query:bh}),t&&(0,d.jsx)(aa,{isOpen:t,onClose:()=>{u(!1),D(!1),window.scrollTo({top:0,behavior:"smooth"})},sql:bh,from:bj,thang:G,pembulatan:I}),C&&(0,d.jsx)(B,{isOpen:C,onClose:()=>{D(!1),window.scrollTo({top:0,behavior:"smooth"})},sql:bh}),z&&(0,d.jsx)(N,{showModalPDF:z,setShowModalPDF:A,selectedFormat:K,setSelectedFormat:M,fetchExportData:br,filename:"inquiry_data",loading:n}),bp&&(0,d.jsx)(B,{isOpen:bp,onClose:()=>bq(!1),query:bh,thang:G,queryType:"INQUIRY"})]})},aW=()=>(0,d.jsx)(aV,{})},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,3598,9161,6159,6942,9697,901,3723,3864,3752],()=>b(b.s=39606));module.exports=c})();