import Fastify from "fastify";
import { fileURLToPath } from "url";
import { dirname, join } from "path";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create Fastify instance with logging
const fastify = Fastify({
  logger: {
    level: process.env.LOG_LEVEL || "info",
  },
  trustProxy: true,
  bodyLimit: 10485760, // 10MB
  keepAliveTimeout: 30000,
  connectionTimeout: 30000,
});

// Register plugins
async function registerPlugins() {
  // Security plugins
  await fastify.register(import("@fastify/helmet"), {
    contentSecurityPolicy: false,
  });

  // CORS configuration for frontend
  await fastify.register(import("@fastify/cors"), {
    origin: [
      "http://localhost:3000",
      "http://localhost:3001",
      process.env.FRONTEND_URL || "http://localhost:3000",
    ],
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
  });

  // JWT authentication
  const jwtSecret = process.env.JWT_SECRET || "sintesa-jwt-secret-key-2024";
  fastify.log.info("Server JWT secret: " + jwtSecret);
  await fastify.register(import("@fastify/jwt"), {
    secret: jwtSecret,
    cookie: {
      cookieName: "token",
      signed: false,
    },
  });

  // Cookie support
  await fastify.register(import("@fastify/cookie"), {
    secret: process.env.COOKIE_SECRET || "sintesa-cookie-secret-2024",
    parseOptions: {},
  });

  // Rate limiting
  await fastify.register(import("@fastify/rate-limit"), {
    max: 100,
    timeWindow: "1 minute",
    skipOnError: true,
  });

  // File upload support
  await fastify.register(import("@fastify/multipart"), {
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB
    },
  });

  // WebSocket support for Socket.IO
  await fastify.register(import("@fastify/websocket"));

  // Static file serving
  await fastify.register(import("@fastify/static"), {
    root: join(__dirname, "../public"),
    prefix: "/public/",
  });
}

// Register database connection
async function registerDatabase() {
  await fastify.register(import("./plugins/database.js"));
}

// Register Redis connection
async function registerRedis() {
  await fastify.register(import("./plugins/redis.js"));
}

// Register Socket.IO
async function registerSocketIO() {
  await fastify.register(import("./plugins/socket.js"));
}

// Register authentication middleware
async function registerAuth() {
  await fastify.register(import("./plugins/auth.js"));
}

// Register encryption utilities
async function registerEncryption() {
  await fastify.register(import("./plugins/encryption.js"));
}

// Register routes
async function registerRoutes() {
  // Root endpoint - API information
  fastify.get("/", async (request, reply) => {
    return {
      success: true,
      message: "Sintesa Backend API",
      version: process.env.npm_package_version || "1.0.0",
      timestamp: new Date().toISOString(),
      endpoints: {
        health: "/health",
        auth: "/next/auth/*",
        users: "/next/users/*",
        inquiry: "/next/inquiry/*",
        referensi: "/next/referensi/*",
        adk: "/next/adk/*",
        dashboard: "/next/dashboard/*",
        status: "/next/status/*",
      },
      documentation: "https://github.com/sintesa-backend/docs",
    };
  });

  // Health check
  fastify.get("/health", async (request, reply) => {
    return {
      status: "ok",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || "1.0.0",
    };
  });

  // API routes
  await fastify.register(import("./routes/auth.js"), { prefix: "/next/auth" });
  await fastify.register(import("./routes/users.js"), {
    prefix: "/next/users",
  });
  await fastify.register(import("./routes/inquiry.js"), {
    prefix: "/next/inquiry",
  });
  await fastify.register(import("./routes/referensi_simple.js"), {
    prefix: "/next/referensi",
  });
  // await fastify.register(import('./routes/adk.js'), { prefix: '/next/adk' });
  await fastify.register(import("./routes/dashboard_simple.js"), {
    prefix: "/next/dashboard",
  });
  await fastify.register(import("./routes/status.js"), {
    prefix: "/next/status",
  });
}

// Error handler
fastify.setErrorHandler(async (error, request, reply) => {
  fastify.log.error(error);

  // JWT errors
  if (
    error.code === "FST_JWT_NO_AUTHORIZATION_IN_HEADER" ||
    error.code === "FST_JWT_AUTHORIZATION_TOKEN_EXPIRED"
  ) {
    return reply.status(401).send({
      success: false,
      message: "Token tidak valid atau telah kedaluwarsa",
      code: "UNAUTHORIZED",
    });
  }

  // Validation errors
  if (error.validation) {
    return reply.status(400).send({
      success: false,
      message: "Data tidak valid",
      errors: error.validation,
      code: "VALIDATION_ERROR",
    });
  }

  // Database errors
  if (error.code && error.code.startsWith("ER_")) {
    return reply.status(500).send({
      success: false,
      message: "Terjadi kesalahan database",
      code: "DATABASE_ERROR",
    });
  }

  // Default error
  const statusCode = error.statusCode || 500;
  return reply.status(statusCode).send({
    success: false,
    message: error.message || "Terjadi kesalahan server",
    code: "INTERNAL_ERROR",
  });
});

// Not found handler
fastify.setNotFoundHandler(async (request, reply) => {
  return reply.status(404).send({
    success: false,
    message: "Endpoint tidak ditemukan",
    code: "NOT_FOUND",
  });
});

// Graceful shutdown
process.on("SIGINT", async () => {
  fastify.log.info("Received SIGINT, shutting down gracefully...");
  try {
    await fastify.close();
    process.exit(0);
  } catch (err) {
    fastify.log.error("Error during shutdown:", err);
    process.exit(1);
  }
});

// Start server
async function start() {
  try {
    // Register all plugins and routes
    await registerPlugins();
    await registerDatabase();
    // await registerRedis();
    await registerAuth();
    await registerEncryption();
    // await registerSocketIO();
    await registerRoutes();

    // Start listening
    const port = process.env.PORT || 88;
    const host = process.env.HOST || "0.0.0.0";

    await fastify.listen({ port, host });

    fastify.log.info(
      `🚀 Sintesa Backend Server running on http://${host}:${port}`
    );
    fastify.log.info(`📊 Dashboard: http://localhost:3000`);
    fastify.log.info(`🔍 Health Check: http://${host}:${port}/health`);
  } catch (err) {
    console.error("Error starting server:", err);
    fastify.log.error("Error starting server:", err);
    process.exit(1);
  }
}

start();
