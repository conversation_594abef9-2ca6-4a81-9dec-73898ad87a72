#!/usr/bin/env node

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';
import readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (query) => new Promise((resolve) => rl.question(query, resolve));

class SintesaSetup {
  constructor() {
    this.config = {
      database: {},
      redis: {},
      jwt: {},
      encryption: {},
      server: {}
    };
  }

  async run() {
    console.log('🚀 Welcome to Sintesa Backend Setup!');
    console.log('This script will help you configure your backend environment.\n');

    try {
      await this.checkPrerequisites();
      await this.createDirectories();
      await this.collectConfiguration();
      await this.generateEnvFile();
      await this.installDependencies();
      await this.setupDatabase();
      await this.finalizeSetup();
      
      console.log('\n✅ Setup completed successfully!');
      console.log('\n📋 Next steps:');
      console.log('1. Review the generated .env file');
      console.log('2. Start the development server: npm run dev');
      console.log('3. Visit http://localhost:3001/api/health to verify');
      
    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      process.exit(1);
    } finally {
      rl.close();
    }
  }

  async checkPrerequisites() {
    console.log('🔍 Checking prerequisites...');
    
    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 18) {
      throw new Error(`Node.js 18+ required, found ${nodeVersion}`);
    }
    
    console.log(`✅ Node.js ${nodeVersion} detected`);
    
    // Check npm
    try {
      const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
      console.log(`✅ npm ${npmVersion} detected`);
    } catch (error) {
      throw new Error('npm not found. Please install Node.js with npm.');
    }
    
    // Check if package.json exists
    try {
      await fs.access('package.json');
      console.log('✅ package.json found');
    } catch (error) {
      throw new Error('package.json not found. Run this script from the project root.');
    }
  }

  async createDirectories() {
    console.log('\n📁 Creating necessary directories...');
    
    const directories = [
      'logs',
      'uploads',
      'temp',
      'config',
      'database',
      'scripts'
    ];
    
    for (const dir of directories) {
      try {
        await fs.mkdir(dir, { recursive: true });
        
        // Create .gitkeep files for empty directories
        const gitkeepPath = path.join(dir, '.gitkeep');
        try {
          await fs.access(gitkeepPath);
        } catch {
          await fs.writeFile(gitkeepPath, '');
        }
        
        console.log(`✅ Created ${dir}/`);
      } catch (error) {
        console.log(`⚠️  Directory ${dir}/ already exists`);
      }
    }
  }

  async collectConfiguration() {
    console.log('\n⚙️  Configuration Setup');
    console.log('Press Enter to use default values shown in [brackets]\n');
    
    // Server configuration
    console.log('🖥️  Server Configuration:');
    this.config.server.port = await this.askWithDefault('Port', '3001');
    this.config.server.host = await this.askWithDefault('Host', '0.0.0.0');
    this.config.server.nodeEnv = await this.askWithDefault('Environment (development/production)', 'development');
    
    // Database configuration
    console.log('\n🗄️  Database Configuration:');
    this.config.database.host = await this.askWithDefault('MySQL Host', 'localhost');
    this.config.database.port = await this.askWithDefault('MySQL Port', '3306');
    this.config.database.user = await this.askWithDefault('MySQL User', 'sintesa_user');
    this.config.database.password = await this.askWithDefault('MySQL Password', 'sintesa_pass_2024');
    this.config.database.name = await this.askWithDefault('Database Name', 'sintesa');
    
    // Redis configuration
    console.log('\n🔴 Redis Configuration:');
    this.config.redis.host = await this.askWithDefault('Redis Host', 'localhost');
    this.config.redis.port = await this.askWithDefault('Redis Port', '6379');
    this.config.redis.password = await this.askWithDefault('Redis Password (optional)', '');
    
    // Security configuration
    console.log('\n🔐 Security Configuration:');
    this.config.jwt.secret = await this.askWithDefault('JWT Secret', this.generateSecret(64));
    this.config.encryption.key = await this.askWithDefault('Encryption Key', this.generateSecret(32));
    
    // CORS configuration
    console.log('\n🌐 CORS Configuration:');
    this.config.server.corsOrigins = await this.askWithDefault('Allowed Origins (comma-separated)', 'http://localhost:3000,http://localhost:5173');
  }

  async askWithDefault(prompt, defaultValue) {
    const answer = await question(`${prompt} [${defaultValue}]: `);
    return answer.trim() || defaultValue;
  }

  generateSecret(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  async generateEnvFile() {
    console.log('\n📝 Generating .env file...');
    
    const envContent = `# Sintesa Backend Environment Configuration
# Generated on ${new Date().toISOString()}

# Server Configuration
NODE_ENV=${this.config.server.nodeEnv}
PORT=${this.config.server.port}
HOST=${this.config.server.host}
TRUST_PROXY=true

# Database Configuration
DB_HOST=${this.config.database.host}
DB_PORT=${this.config.database.port}
DB_USER=${this.config.database.user}
DB_PASSWORD=${this.config.database.password}
DB_NAME=${this.config.database.name}
DB_CHARSET=utf8mb4
DB_TIMEZONE=+07:00

# Redis Configuration
REDIS_HOST=${this.config.redis.host}
REDIS_PORT=${this.config.redis.port}
REDIS_PASSWORD=${this.config.redis.password}
REDIS_DB=0

# JWT Configuration
JWT_SECRET=${this.config.jwt.secret}
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Encryption Configuration
ENCRYPTION_KEY=${this.config.encryption.key}
ENCRYPTION_ALGORITHM=aes-256-gcm

# CORS Configuration
CORS_ORIGINS=${this.config.server.corsOrigins}

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=60000

# File Upload
MAX_FILE_SIZE=10485760
MAX_FILES=5
UPLOAD_PATH=./uploads

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Session Management
SESSION_TIMEOUT=86400
COOKIE_SECRET=${this.generateSecret(32)}

# Query Settings
QUERY_TIMEOUT=30000
MAX_QUERY_RESULTS=10000
QUERY_CACHE_TTL=300

# Socket.IO
SOCKET_CORS_ORIGINS=${this.config.server.corsOrigins}
SOCKET_PING_TIMEOUT=60000
SOCKET_PING_INTERVAL=25000

# Security
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# Cache Settings
CACHE_TTL=3600
CACHE_PREFIX=sintesa:

# API Documentation
ENABLE_DOCS=true
API_HOST=localhost:${this.config.server.port}
API_SCHEME=http

# Feature Flags
ENABLE_RATE_LIMITING=true
ENABLE_QUERY_LOGGING=true
ENABLE_ENCRYPTION=true
ENABLE_WEBSOCKETS=true

# Application Metadata
APP_NAME=Sintesa Backend
APP_VERSION=1.0.0
APP_DESCRIPTION=Modern and fast backend API for Sintesa applications

# Timezone
TZ=Asia/Jakarta
`;
    
    await fs.writeFile('.env', envContent);
    console.log('✅ .env file created');
  }

  async installDependencies() {
    console.log('\n📦 Installing dependencies...');
    
    try {
      console.log('Installing production dependencies...');
      execSync('npm install', { stdio: 'inherit' });
      console.log('✅ Dependencies installed successfully');
    } catch (error) {
      throw new Error('Failed to install dependencies: ' + error.message);
    }
  }

  async setupDatabase() {
    console.log('\n🗄️  Database Setup');
    
    const setupDb = await question('Do you want to set up the database now? (y/N): ');
    
    if (setupDb.toLowerCase() === 'y' || setupDb.toLowerCase() === 'yes') {
      try {
        console.log('Creating database schema...');
        
        // Check if schema.sql exists
        try {
          await fs.access('database/schema.sql');
          console.log('✅ Database schema file found');
          console.log('📋 To set up the database, run:');
          console.log(`   mysql -h ${this.config.database.host} -u ${this.config.database.user} -p${this.config.database.password} ${this.config.database.name} < database/schema.sql`);
        } catch {
          console.log('⚠️  Database schema file not found at database/schema.sql');
          console.log('   You\'ll need to create the database schema manually.');
        }
        
      } catch (error) {
        console.log('⚠️  Database setup skipped:', error.message);
      }
    } else {
      console.log('⏭️  Database setup skipped');
      console.log('   Remember to set up your database before starting the server');
    }
  }

  async finalizeSetup() {
    console.log('\n🔧 Finalizing setup...');
    
    // Create a simple start script
    const startScript = `#!/bin/bash
# Sintesa Backend Start Script

echo "🚀 Starting Sintesa Backend..."

# Check if .env exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Run setup script first."
    exit 1
fi

# Start the server
npm run dev
`;
    
    await fs.writeFile('start.sh', startScript);
    
    // Make it executable on Unix systems
    try {
      execSync('chmod +x start.sh');
    } catch {
      // Ignore on Windows
    }
    
    console.log('✅ Start script created (start.sh)');
    
    // Create a simple health check script
    const healthCheckScript = `#!/usr/bin/env node

import http from 'http';

const options = {
  hostname: 'localhost',
  port: ${this.config.server.port},
  path: '/api/health',
  method: 'GET'
};

const req = http.request(options, (res) => {
  console.log(\`Status: \${res.statusCode}\`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const result = JSON.parse(data);
      console.log('Health check result:', result);
    } catch (error) {
      console.log('Raw response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('Health check failed:', error.message);
});

req.end();
`;
    
    await fs.writeFile('scripts/health-check.js', healthCheckScript);
    console.log('✅ Health check script created');
  }
}

// Run setup if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const setup = new SintesaSetup();
  setup.run().catch(console.error);
}

export default SintesaSetup;