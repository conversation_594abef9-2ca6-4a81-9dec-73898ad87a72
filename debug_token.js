import jwt from 'jsonwebtoken';
import CryptoJS from 'crypto-js';

// JWT configuration (matching the backend)
const JWT_SECRET = 'sintesa-jwt-secret-key-2024';
const ENCRYPTION_SECRET = 'mebe23';

// Create a test JWT token
const payload = {
  id: 1,
  username: 'testuser',
  role: 'admin',
  kdkanwil: '01',
  kdkppn: '001',
  kdlokasi: '001',
  active: 1,
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (8 * 60 * 60), // 8 hours
  iss: 'sintesa-backend',
  aud: 'sintesa-frontend'
};

console.log('=== JWT Token Generation ===');
const accessToken = jwt.sign(payload, JWT_SECRET);
console.log('Generated JWT token:', accessToken);

// Verify the token works
try {
  const decoded = jwt.verify(accessToken, JWT_SECRET);
  console.log('JWT verification successful:', decoded);
} catch (error) {
  console.log('JWT verification failed:', error.message);
}

console.log('\n=== Encryption Test ===');
// Encrypt the token (like the frontend does - matching Encrypt.jsx)
const encJson = CryptoJS.AES.encrypt(accessToken, ENCRYPTION_SECRET).toString();
const encryptedToken = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(encJson));
console.log('Encrypted token:', encryptedToken);

console.log('\n=== Decryption Test ===');
// Decrypt the token (like the backend should do - matching Decrypt.jsx)
try {
  const decData = CryptoJS.enc.Base64.parse(encryptedToken).toString(CryptoJS.enc.Utf8);
  const bytes = CryptoJS.AES.decrypt(decData, ENCRYPTION_SECRET).toString(CryptoJS.enc.Utf8);
  console.log('Decrypted token:', bytes);
  
  // Verify the decrypted token
  const decodedDecrypted = jwt.verify(bytes, JWT_SECRET);
  console.log('Decrypted JWT verification successful:', decodedDecrypted);
} catch (error) {
  console.log('Decryption or verification failed:', error.message);
  console.log('Error details:', error);
}
