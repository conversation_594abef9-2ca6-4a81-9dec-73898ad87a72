(()=>{var a={};a.id=4045,a.ids=[4045],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2466:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["satker",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,9229)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\satker\\[id]\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,8413)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\layout.jsx"],loading:[()=>Promise.resolve().then(c.bind(c,48221)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\loading.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,53361)),"D:\\sintesaNEXT2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\sintesaNEXT2\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["D:\\sintesaNEXT2\\src\\app\\(dashboard)\\satker\\[id]\\page.jsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/satker/[id]/page",pathname:"/satker/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/satker/[id]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7762:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>S});var d=c(60687),e=c(40611);c(37624);var f=c(14221),g=c(43210);let h=g.forwardRef(function({title:a,titleId:b,...c},d){return g.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?g.createElement("title",{id:b},a):null,g.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"}))});var i=c(85015),j=c(69087),k=c(77611),l=c(27580);function m({kdsatker:a,tahun:b}){let{token:c,axiosJWT:m}=(0,g.useContext)(f.A)||{},[n,o]=(0,g.useState)([]),[p,q]=(0,g.useState)(!1),[r,s]=(0,g.useState)(""),[t,u]=(0,g.useState)(""),{showToast:v}=(0,e.d)(),w=async(a,b,d)=>{let e=`${process.env.NEXT_PUBLIC_SOCKET}/file/download_adk?nmfile=${a}&folder=${b}&tahun=${d}`;try{let b=await m.get(e,{responseType:"blob",headers:{Authorization:`Bearer ${c}`}}),d=window.URL.createObjectURL(new Blob([b.data])),f=document.createElement("a");f.href=d,f.setAttribute("target","_blank"),f.setAttribute("rel","noopener noreferrer"),f.setAttribute("download",a),document.body.appendChild(f),f.click(),f.remove()}catch(a){u("Terjadi kesalahan saat mendownload file. Silakan coba lagi.")}};return r?(0,d.jsx)(i.Z,{className:"w-full",children:(0,d.jsx)("div",{className:"p-4 text-center text-red-600 dark:text-red-400",children:r})}):p?(0,d.jsx)(i.Z,{className:"w-full flex items-center justify-center min-h-[200px]",children:(0,d.jsx)(j.o,{size:"lg",color:"primary"})}):(0,d.jsx)(i.Z,{className:"w-full",children:(0,d.jsxs)("div",{className:"p-4",children:[(0,d.jsxs)("h5",{className:"text-lg font-bold mb-4 text-blue-700 dark:text-blue-200",children:["DIPA TA. ",b||"2025"]}),(0,d.jsx)("div",{className:"overflow-auto rounded-lg border border-blue-100 dark:border-gray-700 bg-white/60 dark:bg-gray-900/60 shadow-inner min-h-[360px] max-h-[360px]",children:(0,d.jsxs)("table",{className:"min-w-full text-sm",children:[(0,d.jsx)("thead",{className:"bg-blue-100 dark:bg-gray-800/80 text-blue-800 dark:text-blue-200 font-semibold",children:(0,d.jsxs)("tr",{className:"text-center",children:[(0,d.jsx)("th",{className:"px-3 py-2",children:"#"}),(0,d.jsx)("th",{className:"px-3 py-2",children:" File"}),(0,d.jsx)("th",{className:"px-3 py-2",children:"Revisi "}),(0,d.jsx)("th",{className:"px-3 py-2",children:"Tgl"}),(0,d.jsx)("th",{className:"px-3 py-2",children:"Download"})]})}),(0,d.jsx)("tbody",{children:n.length>0?n.map((a,c)=>(0,d.jsxs)("tr",{className:"border-b border-blue-50 dark:border-gray-800 text-center",children:[(0,d.jsx)("td",{className:"px-3 py-2",children:c+1}),(0,d.jsx)("td",{className:"px-3 py-2",children:(0,d.jsx)(k.I,{content:(0,d.jsx)("span",{className:"block max-w-xs whitespace-pre-line break-words text-sm font-mono text-white px-2 py-1 transition-colors duration-300",children:a.nmfile}),placement:"top",className:"bg-orange-400 dark:bg-orange-500 border border-white shadow-lg rounded-lg transition-all duration-300",children:(0,d.jsx)("span",{className:"cursor-pointer text-blue-700 dark:text-blue-200 transition-colors duration-300 hover:text-orange-500 focus:text-orange-500",children:a.nmfile.length>10?a.nmfile.slice(0,10)+"...":a.nmfile})})}),(0,d.jsx)("td",{className:"px-3 py-2",children:a.norev}),(0,d.jsx)("td",{className:"px-3 py-2",children:function(a){if(!a)return"";let[b,c,d]=a.split("-");return b&&c&&d?`${d}/${c}/${b.slice(-2)}`:a}(a.tg)}),(0,d.jsx)("td",{className:"px-3 py-2",children:(0,d.jsx)(k.I,{content:"Download",placement:"top",className:"bg-orange-400 text-white dark:bg-orange-500 border border-white shadow-lg rounded-lg transition-all duration-300",children:(0,d.jsx)(l.T,{isIconOnly:!0,size:"sm",color:"primary",variant:"flat",onClick:()=>w(a.nmfile,a.folder,b||"2025"),children:(0,d.jsx)(h,{className:"w-5 h-5"})})})})]},c)):(0,d.jsx)("tr",{children:(0,d.jsx)("td",{colSpan:6,className:"text-center py-8 text-gray-400 dark:text-gray-500",children:"Tidak ada data file ADK untuk satker ini."})})})]})}),t&&(0,d.jsx)("div",{className:"mt-4 text-red-600 dark:text-red-400 text-center text-sm",children:t})]})})}var n=c(24793);let o=g.forwardRef(function({title:a,titleId:b,...c},d){return g.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?g.createElement("title",{id:b},a):null,g.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}),p=g.forwardRef(function({title:a,titleId:b,...c},d){return g.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?g.createElement("title",{id:b},a):null,g.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}),q=g.forwardRef(function({title:a,titleId:b,...c},d){return g.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:d,"aria-labelledby":b},c),a?g.createElement("title",{id:b},a):null,g.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m19.5 8.25-7.5 7.5-7.5-7.5"}))});var r=c(36220),s=c(62948),t=c(6409),u=c(98462),v=c(81317),w=(0,u.tv)({slots:{base:"flex items-center",item:["flex gap-1 items-center","cursor-pointer","whitespace-nowrap","outline-solid outline-transparent","tap-highlight-transparent",...v.zb],separator:"text-default-400 px-1 rtl:rotate-180"},variants:{color:{foreground:{item:"text-foreground/50",separator:"text-foreground/50"},primary:{item:"text-primary/80",separator:"text-primary/80"},secondary:{item:"text-secondary/80",separator:"text-secondary/80"},success:{item:"text-success/80",separator:"text-success/80"},warning:{item:"text-warning/80",separator:"text-warning/80"},danger:{item:"text-danger/80",separator:"text-danger/80"}},size:{sm:{item:"text-tiny"},md:{item:"text-small"},lg:{item:"text-medium"}},underline:{none:{item:"no-underline"},hover:{item:"hover:underline"},always:{item:"underline"},active:{item:"active:underline"},focus:{item:"focus:underline"}},isCurrent:{true:{item:"cursor-default"},false:{item:["hover:opacity-hover","active:opacity-disabled"]}},isDisabled:{true:{item:"opacity-disabled pointer-events-none",separator:"opacity-disabled"}},disableAnimation:{false:{item:"transition-opacity"},true:{item:"transition-none"}}},defaultVariants:{size:"md",color:"foreground",underline:"hover",isDisabled:!1},compoundVariants:[{isCurrent:!0,color:"foreground",class:{item:"text-foreground"}},{isCurrent:!0,color:"primary",class:{item:"text-primary"}},{isCurrent:!0,color:"secondary",class:{item:"text-secondary"}},{isCurrent:!0,color:"success",class:{item:"text-success"}},{isCurrent:!0,color:"warning",class:{item:"text-warning"}},{isCurrent:!0,color:"danger",class:{item:"text-danger"}},{isCurrent:!1,underline:"none",class:{item:"no-underline"}},{underline:["hover","always","active","focus"],class:"underline-offset-4"}]}),x=(0,u.tv)({slots:{base:"",list:"flex flex-wrap list-none",ellipsis:"text-medium",separator:"text-default-400 px-1"},variants:{size:{sm:{},md:{},lg:{}},radius:{none:{list:"rounded-none"},sm:{list:"rounded-small"},md:{list:"rounded-medium"},lg:{list:"rounded-large"},full:{list:"rounded-full"}},variant:{solid:{list:"bg-default-100"},bordered:{list:"border-medium border-default-200 shadow-xs"},light:{}}},defaultVariants:{size:"md",radius:"sm",variant:"light"},compoundVariants:[{variant:["solid","bordered"],class:{list:"max-w-fit"}},{variant:["solid","bordered"],size:"sm",class:{list:"px-2 py-1"}},{variant:["solid","bordered"],size:"md",class:{list:"px-2.5 py-1.5"}},{variant:["solid","bordered"],size:"lg",class:{list:"px-3 py-2"}}]}),y=c(87223),z=c(62104),A=c(45427),B=c(25381),C=c(66775),D=c(15320),E=c(58285),F=c(79910),G=(0,s.Rf)((a,b)=>{let{Component:c,WrapperComponent:e,children:f,isLast:h,separator:i,startContent:j,endContent:k,hideSeparator:l,getBaseProps:m,getItemProps:n,getSeparatorProps:o}=function(a){let[b,c]=(0,s.rE)(a,w.variantKeys),{ref:d,as:e,className:f,children:h,isLast:i,separator:j,startContent:k,endContent:l,classNames:m,hideSeparator:n=!1,...o}=b,p=!!(null==a?void 0:a.isCurrent),q=null==a?void 0:a.isDisabled,r=(null==a?void 0:a.href)&&!p?"a":"span",u="string"==typeof r,v=(0,y.zD)(d),{itemProps:x}=function(a,b){let{isCurrent:c,isDisabled:d,"aria-current":e,elementType:f="a",...g}=a,{linkProps:h}=function(a,b){let{elementType:c="a",onPress:d,onPressStart:e,onPressEnd:f,onClick:g,isDisabled:h,...i}=a,j={};"a"!==c&&(j={role:"link",tabIndex:h?void 0:0});let{focusableProps:k}=(0,D.Wc)(a,b),{pressProps:l,isPressed:m}=(0,E.d)({onPress:d,onPressStart:e,onPressEnd:f,onClick:g,isDisabled:h,ref:b}),n=(0,A.$)(i,{labelable:!0}),o=(0,B.v)(k,l),p=(0,C.rd)(),q=(0,C._h)(a);return{isPressed:m,linkProps:(0,B.v)(n,q,{...o,...j,"aria-disabled":h||void 0,"aria-current":a["aria-current"],onClick:b=>{var c;null==(c=l.onClick)||c.call(l,b),(0,C.PJ)(b,p,a.href,a.routerOptions)}})}}({isDisabled:d||c,elementType:f,...g},b),i=/^h[1-6]$/.test(f),j={};return i||(j=h),c&&(j["aria-current"]=e||"page",j.tabIndex=a.autoFocus?-1:void 0),{itemProps:{"aria-disabled":d,...j}}}({...a,isCurrent:p,elementType:r},v),{isFocusVisible:G,isFocused:H,focusProps:I}=(0,t.o)(),J=(0,g.useMemo)(()=>w({...c,isCurrent:p,underline:(null==a?void 0:a.underline)===void 0||p?"none":null==a?void 0:a.underline,className:f}),[(0,F.t6)(c),p,f]),K=(0,F.$z)(null==m?void 0:m.base,f);return{Component:r,WrapperComponent:e||"li",children:h,separator:j,startContent:k,endContent:l,isDisabled:q,isCurrent:p,isLast:i,hideSeparator:n,getBaseProps:()=>({ref:v,"data-slot":"base",className:J.base({class:K}),...(0,z.$)(o,{enabled:u})}),getItemProps:()=>({href:p||null==a?void 0:a.href,"data-slot":"item","data-focus":(0,F.sE)(H),"data-focus-visible":(0,F.sE)(G),"data-disabled":null==a?void 0:a.isDisabled,"data-current":null==a?void 0:a.isCurrent,className:J.item({class:null==m?void 0:m.item}),...(0,F.v6)(x,q?{}:I)}),getSeparatorProps:()=>({"data-slot":"separator","aria-hidden":(0,F.sE)(!0),className:J.separator({class:null==m?void 0:m.separator})})}}({...a,ref:b});return(0,d.jsxs)(e,{...m(),children:[(0,d.jsxs)(c,{...n(),children:[j,f,k]}),!h&&!l&&(0,d.jsx)("span",{...o(),children:i})]})});G.displayName="HeroUI.Breadcrumbs";var H=c(58445),I=c(38881),J={};J={"ar-AE":{breadcrumbs:`\u{639}\u{646}\u{627}\u{635}\u{631} \u{627}\u{644}\u{648}\u{627}\u{62C}\u{647}\u{629}`},"bg-BG":{breadcrumbs:`\u{422}\u{440}\u{43E}\u{445}\u{438} \u{445}\u{43B}\u{44F}\u{431}`},"cs-CZ":{breadcrumbs:"Popis cesty"},"da-DK":{breadcrumbs:`Br\xf8dkrummer`},"de-DE":{breadcrumbs:"Breadcrumbs"},"el-GR":{breadcrumbs:`\u{3A0}\u{3BB}\u{3BF}\u{3B7}\u{3B3}\u{3AE}\u{3C3}\u{3B5}\u{3B9}\u{3C2} breadcrumb`},"en-US":{breadcrumbs:"Breadcrumbs"},"es-ES":{breadcrumbs:"Migas de pan"},"et-EE":{breadcrumbs:"Lingiread"},"fi-FI":{breadcrumbs:"Navigointilinkit"},"fr-FR":{breadcrumbs:"Chemin de navigation"},"he-IL":{breadcrumbs:`\u{5E9}\u{5D1}\u{5D9}\u{5DC}\u{5D9} \u{5E0}\u{5D9}\u{5D5}\u{5D5}\u{5D8}`},"hr-HR":{breadcrumbs:"Navigacijski putovi"},"hu-HU":{breadcrumbs:`Morzsamen\xfc`},"it-IT":{breadcrumbs:"Breadcrumb"},"ja-JP":{breadcrumbs:`\u{30D1}\u{30F3}\u{304F}\u{305A}\u{30EA}\u{30B9}\u{30C8}`},"ko-KR":{breadcrumbs:`\u{D0D0}\u{C0C9} \u{D45C}\u{C2DC}`},"lt-LT":{breadcrumbs:`Nar\u{161}ymo kelias`},"lv-LV":{breadcrumbs:`Atpaka\u{13C}ce\u{13C}i`},"nb-NO":{breadcrumbs:"Navigasjonsstier"},"nl-NL":{breadcrumbs:"Broodkruimels"},"pl-PL":{breadcrumbs:"Struktura nawigacyjna"},"pt-BR":{breadcrumbs:"Caminho detalhado"},"pt-PT":{breadcrumbs:"Categorias"},"ro-RO":{breadcrumbs:`Miez de p\xe2ine`},"ru-RU":{breadcrumbs:`\u{41D}\u{430}\u{432}\u{438}\u{433}\u{430}\u{446}\u{438}\u{44F}`},"sk-SK":{breadcrumbs:`Naviga\u{10D}n\xe9 prvky Breadcrumbs`},"sl-SI":{breadcrumbs:"Drobtine"},"sr-SP":{breadcrumbs:"Putanje navigacije"},"sv-SE":{breadcrumbs:`S\xf6kv\xe4gar`},"tr-TR":{breadcrumbs:`\u{130}\xe7erik haritalar\u{131}`},"uk-UA":{breadcrumbs:`\u{41D}\u{430}\u{432}\u{456}\u{433}\u{430}\u{446}\u{456}\u{439}\u{43D}\u{430} \u{441}\u{442}\u{435}\u{436}\u{43A}\u{430}`},"zh-CN":{breadcrumbs:`\u{5BFC}\u{822A}\u{680F}`},"zh-TW":{breadcrumbs:`\u{5C0E}\u{89BD}\u{5217}`}};var K=c(55904),L=a=>(0,d.jsx)("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",viewBox:"0 0 24 24",width:"1em",...a,children:(0,d.jsx)("path",{d:"m9 18 6-6-6-6"})}),M=a=>(0,d.jsxs)("svg",{"aria-hidden":"true",fill:"none",height:"1em",shapeRendering:"geometricPrecision",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",viewBox:"0 0 24 24",width:"1em",...a,children:[(0,d.jsx)("circle",{cx:"12",cy:"12",fill:"currentColor",r:"1"}),(0,d.jsx)("circle",{cx:"19",cy:"12",fill:"currentColor",r:"1"}),(0,d.jsx)("circle",{cx:"5",cy:"12",fill:"currentColor",r:"1"})]}),N=(0,s.Rf)((a,b)=>{let{Component:c,children:e,childCount:f,itemProps:h,separator:i=(0,d.jsx)(L,{}),maxItems:j,itemsBeforeCollapse:k,itemsAfterCollapse:l,isDisabled:m,renderEllipsis:n,getBaseProps:o,getListProps:p,getEllipsisProps:q,getSeparatorProps:r,onAction:t}=function(a){var b,c;let d=(0,H.o)(),e=null!=(c=null!=(b=null==a?void 0:a.disableAnimation)?b:null==d?void 0:d.disableAnimation)&&c,[f,h]=(0,s.rE)(a,x.variantKeys),{ref:i,as:j,color:k,underline:l,isDisabled:m,separator:n,children:o,itemsBeforeCollapse:p=1,itemsAfterCollapse:q=2,maxItems:r=8,hideSeparator:t,renderEllipsis:u,className:v,classNames:w,itemClasses:B,onAction:C,...D}=f,E=j||"nav",L="string"==typeof E,{navProps:M}=function(a){var b;let{"aria-label":c,...d}=a,e=(0,K.o)((b=J)&&b.__esModule?b.default:b,"@react-aria/breadcrumbs");return{navProps:{...(0,A.$)(d,{labelable:!0}),"aria-label":c||e.format("breadcrumbs")}}}(a),[,N]=(0,I.t)(o,G),O=g.Children.count(N),P=(0,y.zD)(i),Q=(0,g.useMemo)(()=>x({...h}),[(0,F.t6)(h)]),R=(0,F.$z)(null==w?void 0:w.base,v);return{Component:E,children:N,slots:Q,separator:n,childCount:O,itemsAfterCollapse:q,itemsBeforeCollapse:p,maxItems:r,classNames:w,isDisabled:m,itemProps:{color:k,underline:l,disableAnimation:e,hideSeparator:t,size:null==a?void 0:a.size,classNames:B},renderEllipsis:u,getBaseProps:()=>({ref:P,"data-slot":"base",className:Q.base({class:R}),...(0,F.v6)(M,(0,z.$)(D,{enabled:L}))}),getListProps:()=>({"data-slot":"list",className:Q.list({class:null==w?void 0:w.list})}),getEllipsisProps:()=>({"data-slot":"ellipsis",className:Q.ellipsis({class:null==w?void 0:w.ellipsis})}),getSeparatorProps:()=>({"data-slot":"separator","aria-hidden":(0,F.sE)(!0),className:Q.separator({class:null==w?void 0:w.separator})}),onAction:C}}({...a,ref:b}),u=(0,g.useMemo)(()=>{let a=null==e?void 0:e.map((a,b)=>{var c;let d=b===f-1,e=(null==a?void 0:a.key)||b;return(0,g.cloneElement)(a,{...h,isLast:d,separator:i,isDisabled:m&&!d,isCurrent:d||a.props.isCurrent,...a.props,key:e,onPress:(0,F.cy)(null==(c=a.props)?void 0:c.onPress,()=>null==t?void 0:t(e))})});if(!a)return null;if(f<j)return a;if(k+l>=f)return(0,F.R8)(`You have provided an invalid combination of props to the Breadcrumbs. itemsAfterCollapse={${l}} + itemsBeforeCollapse={${k}} >= itemsCount={${f}}`,"Breadcrumbs"),a;let b=a.slice(k,a.length-l);if(b.length<1)return a;let c=(0,d.jsx)(M,{...q()}),o=(0,g.cloneElement)(b[0],{...b[0].props,key:"ellipsis",children:c}),p="function"==typeof n?n({collapsedItem:o,items:b.map(a=>a.props),maxItems:j,ellipsisIcon:c,itemsBeforeCollapse:k,itemsAfterCollapse:l,separator:(0,d.jsx)("span",{...r(),children:i})}):o;return[...a.slice(0,k),(0,g.isValidElement)(p)&&(0,g.cloneElement)(p,{key:"ellipsis-item"}),...a.slice(a.length-l,a.length)]},[e,f,i,h,k,l,m]);return(0,d.jsx)(c,{...o(),children:(0,d.jsx)("ol",{...p(),children:u})})});N.displayName="HeroUI.Breadcrumbs";var O=c(41894),P=c(10473),Q=c(41871),R=c(16189);function S({params:a}){let b=(0,g.use)(a),[c,e]=(0,g.useState)(null),[f,h]=(0,g.useState)(!0),[k,s]=(0,g.useState)(null),t=new Date().getFullYear(),[u,v]=(0,g.useState)(t.toString()),[w,x]=(0,g.useState)(""),[y,z]=(0,g.useState)(!1),A=(0,R.useRouter)(),{ambilDetailSatker:B}=(0,n.H)(),C=()=>{A.back()};return f?(0,d.jsx)("div",{className:"container mx-auto p-6",children:(0,d.jsx)(i.Z,{className:"max-w-2xl mx-auto",children:(0,d.jsxs)(r.U,{className:"flex flex-col items-center justify-center py-16",children:[(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsx)(j.o,{size:"lg",color:"primary"})}),(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2",children:"Memuat Detail Satker"}),(0,d.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 text-center",children:['Sedang mengambil informasi satker dengan kode "',b.id,'"']}),(0,d.jsx)("div",{className:"mt-4 flex items-center text-sm text-gray-500",children:(0,d.jsxs)("div",{className:"animate-pulse flex space-x-1",children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,d.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,d.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"})]})})]})})}):k||!c?(0,d.jsx)("div",{className:"container mx-auto p-6",children:(0,d.jsx)(i.Z,{className:"max-w-2xl mx-auto",children:(0,d.jsxs)(r.U,{className:"text-center py-16",children:[(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsxs)("div",{className:"w-24 h-24 mx-auto bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center relative",children:[(0,d.jsx)(o,{className:"w-12 h-12 text-gray-400"}),(0,d.jsx)("div",{className:"absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center",children:(0,d.jsx)(p,{className:"w-4 h-4 text-white"})})]})}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-2",children:k||`Satker dengan kode "${b.id}" tidak dapat ditemukan dalam database.`}),(0,d.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-500 mb-8",children:"Pastikan kode satker yang Anda masukkan benar dan coba lagi."}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[(0,d.jsx)(l.T,{color:"primary",variant:"solid",onClick:C,className:"min-w-32",children:"← Kembali"}),(0,d.jsx)(l.T,{color:"secondary",variant:"flat",onClick:()=>A.push("/dashboard"),className:"min-w-32",children:"Ke Dashboard"}),(0,d.jsx)(l.T,{color:"default",variant:"light",onClick:()=>window.location.reload(),className:"min-w-32",children:"Muat Ulang"})]}),(0,d.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg",children:[(0,d.jsx)("h3",{className:"text-sm font-semibold text-blue-800 dark:text-blue-200 mb-2",children:"Bantuan"}),(0,d.jsx)("p",{className:"text-xs text-blue-700 dark:text-blue-300",children:"Jika Anda yakin kode satker benar, silakan hubungi Saudara Taufan/ Restu."})]})]})})}):(0,d.jsxs)("div",{className:"container mx-auto p-4 sm:p-6 space-y-6",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,d.jsxs)(N,{children:[(0,d.jsx)(G,{onClick:()=>A.push("/dashboard"),children:"Dashboard"}),(0,d.jsx)(G,{children:"Satker"}),(0,d.jsx)(G,{children:c.kode})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-900 dark:text-gray-100",children:"Detail Satuan Kerja"}),(0,d.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Informasi lengkap Satuan Kerja"})]}),(0,d.jsx)(l.T,{color:"default",variant:"flat",onClick:C,startContent:(0,d.jsx)("span",{children:"←"}),children:"Kembali"})]})]}),(0,d.jsxs)(i.Z,{className:"w-full shadow-medium",children:[(0,d.jsx)(O.d,{className:"pb-3",children:(0,d.jsx)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between w-full gap-4",children:(0,d.jsxs)("div",{className:"flex flex-wrap items-center gap-3",children:[(0,d.jsx)(P.R,{size:"lg",variant:"flat",color:"primary",className:"font-mono text-base",children:c.kdsatker||c.kode}),"YA"===c.statusblu&&(0,d.jsx)(P.R,{size:"md",variant:"flat",color:"success",children:"BLU"}),(0,d.jsxs)(P.R,{size:"md",variant:"flat",color:"warning",children:["TA ",c.thang]})]})})}),(0,d.jsxs)(r.U,{className:"pt-0",children:[(0,d.jsx)("h2",{className:"text-xl sm:text-2xl font-semibold text-gray-900 dark:text-gray-100 leading-tight",children:c.nmsatker||c.nama}),c.email&&(0,d.jsxs)("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:["\uD83D\uDCE7 ",c.email]})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"xl:col-span-2 space-y-6",children:[(0,d.jsxs)(i.Z,{className:"shadow-medium",children:[(0,d.jsx)(O.d,{children:(0,d.jsx)("h4",{className:"text-lg font-semibold flex items-center gap-2",children:"\uD83C\uDFE2 Informasi Organisasi"})}),(0,d.jsxs)(r.U,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1",children:"Kementerian/Lembaga"}),(0,d.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,d.jsx)(P.R,{size:"sm",variant:"flat",color:"primary",className:"font-mono",children:c.kddept}),(0,d.jsx)("span",{className:"text-gray-800 dark:text-gray-200 text-sm",children:c.nmdept})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1",children:"Unit Eselon I"}),(0,d.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,d.jsx)(P.R,{size:"sm",variant:"flat",color:"secondary",className:"font-mono",children:c.kdunit}),(0,d.jsx)("span",{className:"text-gray-800 dark:text-gray-200 text-sm",children:c.nmunit})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1",children:"Kewenangan"}),(0,d.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,d.jsx)(P.R,{size:"sm",variant:"flat",color:"warning",className:"font-mono",children:c.kddekon}),(0,d.jsx)("span",{className:"text-gray-800 dark:text-gray-200 text-sm",children:c.nmdekon})]})]})]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1",children:"Kanwil DJPBN"}),(0,d.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,d.jsx)(P.R,{size:"sm",variant:"flat",color:"success",className:"font-mono",children:c.kdkanwil}),(0,d.jsx)("span",{className:"text-gray-800 dark:text-gray-200 text-sm",children:c.nmkanwil})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1",children:"KPPN"}),(0,d.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,d.jsx)(P.R,{size:"sm",variant:"flat",color:"danger",className:"font-mono",children:c.kdkppn}),(0,d.jsx)("span",{className:"text-gray-800 dark:text-gray-200 text-sm",children:c.nmkppn})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1",children:"NPWP"}),(0,d.jsx)("span",{className:"font-mono text-gray-800 dark:text-gray-200 bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-md text-sm",children:c.npwp})]})]})]}),(0,d.jsxs)("div",{className:"border-t pt-4 mt-4",children:[(0,d.jsx)("h4",{className:"text-md font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2",children:"\uD83D\uDCCA Status & Info"}),(0,d.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Status BLU"}),(0,d.jsx)(P.R,{size:"sm",color:"YA"===c.statusblu?"success":"default",variant:"flat",children:"YA"===c.statusblu?"Ya":"Tidak"})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Jenis Dokumen"}),(0,d.jsx)(P.R,{size:"sm",color:"YA"===c.statusblu?"success":"default",variant:"flat",children:c.kdjendok||"N/A"})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Tahun Anggaran"}),(0,d.jsx)(P.R,{size:"sm",color:"primary",variant:"flat",children:c.thang})]})]})]})]})]}),(0,d.jsxs)(i.Z,{className:"shadow-medium",children:[(0,d.jsx)(O.d,{children:(0,d.jsx)("h3",{className:"text-lg font-semibold flex items-center gap-2",children:"\uD83D\uDC65 Pejabat & Pengelola Keuangan"})}),(0,d.jsx)(r.U,{className:"space-y-4",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1",children:"Kuasa Pengguna Anggaran (KPA)"}),(0,d.jsx)("p",{className:"text-gray-800 dark:text-gray-200",children:c.kpa||"Belum diisi"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1",children:"Bendahara"}),(0,d.jsx)("p",{className:"text-gray-800 dark:text-gray-200",children:c.bendahara||"Belum diisi"})]}),(0,d.jsxs)("div",{className:"md:col-span-2",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400 mb-1",children:"Pejabat Pembuat SPM (PPSPM)"}),(0,d.jsx)("p",{className:"text-gray-800 dark:text-gray-200",children:c.ppspm||"Belum diisi"})]})]})})]})]}),(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)(i.Z,{className:"shadow-xl h-full bg-gradient-to-br from-blue-50/80 to-white dark:from-gray-900 dark:to-gray-800 border border-blue-100 dark:border-gray-700 flex flex-col",style:{minHeight:"100%",height:"100%"},children:[(0,d.jsx)(O.d,{className:"pb-2 border-b border-blue-100 dark:border-gray-700 bg-transparent",children:(0,d.jsx)("h4",{className:"text-lg font-normal text-blue-700 dark:text-blue-200 tracking-tight",children:"Download ADK DIPA"})}),(0,d.jsxs)(r.U,{className:"flex flex-col gap-4 p-4 flex-1 overflow-hidden",children:[(0,d.jsx)("div",{className:"w-full mb-4 relative",children:(0,d.jsxs)("div",{className:"border border-gray-300 dark:border-gray-700 rounded-xl bg-white dark:bg-gray-800 shadow-md relative",children:[(0,d.jsxs)("div",{className:"flex items-center px-4 py-3 cursor-pointer select-none",onClick:()=>z(a=>!a),children:[(0,d.jsx)("span",{className:"flex-1 text-gray-800 dark:text-gray-100",children:u}),(0,d.jsx)(q,{className:`w-5 h-5 ml-2 transition-transform ${y?"rotate-180":""} text-blue-400`})]}),y&&(0,d.jsxs)("div",{className:"absolute left-0 top-full w-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-b-xl shadow-md z-20",children:[(0,d.jsx)("div",{className:"p-3 border-b border-gray-200 dark:border-gray-600",children:(0,d.jsx)(Q.r,{autoFocus:!0,placeholder:"Cari tahun...",value:w,onChange:a=>x(a.target.value),startContent:(0,d.jsx)(o,{className:"w-4 h-4 text-gray-400"}),size:"sm",className:"w-full"})}),(0,d.jsxs)("div",{className:"max-h-60 overflow-auto",children:[Array.from({length:t-2009+1},(a,b)=>t-b).filter(a=>a.toString().includes(w)).map(a=>(0,d.jsx)("div",{onMouseDown:()=>{v(a.toString()),z(!1),x("")},className:`px-4 py-3 cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 border-b border-gray-100 dark:border-gray-700 last:border-b-0 ${u===a.toString()?"bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 font-medium":"text-gray-800 dark:text-gray-200"}`,children:a},a)),w&&0===Array.from({length:t-2009+1},(a,b)=>t-b).filter(a=>a.toString().includes(w)).length&&(0,d.jsxs)("div",{className:"px-4 py-8 text-center text-gray-500 dark:text-gray-400",children:[(0,d.jsx)(o,{className:"w-8 h-8 mx-auto mb-2 text-gray-300"}),(0,d.jsxs)("p",{className:"text-sm",children:['Tahun "',w,'" tidak ditemukan']}),(0,d.jsxs)("p",{className:"text-xs mt-1",children:["Tersedia tahun 2009 - ",t]})]})]})]})]})}),(0,d.jsx)("div",{className:"flex-1 w-full",children:(0,d.jsx)(m,{kdsatker:c?.kdsatker||c?.kode,tahun:u})})]})]})})]})]})}},9229:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\sintesaNEXT2\\\\src\\\\app\\\\(dashboard)\\\\satker\\\\[id]\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\satker\\[id]\\page.jsx","default")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},17557:(a,b,c)=>{Promise.resolve().then(c.bind(c,9229))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},37624:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(4765),e=c.n(d);let f="mebe23",g=(a,b=f)=>{let c=e().AES.encrypt(JSON.stringify(a),b).toString();return e().enc.Base64.stringify(e().enc.Utf8.parse(c))}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41894:(a,b,c)=>{"use strict";c.d(b,{d:()=>j});var d=c(98869),e=c(62948),f=c(87223),g=c(79910),h=c(60687),i=(0,e.Rf)((a,b)=>{var c;let{as:e,className:i,children:j,...k}=a,l=(0,f.zD)(b),{slots:m,classNames:n}=(0,d.f)(),o=(0,g.$z)(null==n?void 0:n.header,i);return(0,h.jsx)(e||"div",{ref:l,className:null==(c=m.header)?void 0:c.call(m,{class:o}),...k,children:j})});i.displayName="HeroUI.CardHeader";var j=i},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},74442:(a,b,c)=>{Promise.resolve().then(c.bind(c,7762))},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,3598,9697,901],()=>b(b.s=2466));module.exports=c})();