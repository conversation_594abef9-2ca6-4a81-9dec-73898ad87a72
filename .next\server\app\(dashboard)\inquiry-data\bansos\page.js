(()=>{var a={};a.id=6236,a.ids=[6236],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7192:(a,b,c)=>{"use strict";c.d(b,{t:()=>f});var d=c(77567);let e=(a,b,c="error")=>{d.A.fire({title:a,text:b,icon:c,position:"top-end",toast:!0,showConfirmButton:!1,timer:5e3,showCloseButton:!0,background:"red",color:"white",timerProgressBar:!0})},f=(a,b)=>{switch(a){case 400:e(`<PERSON><PERSON><PERSON>, Permintaan tidak valid. (${b})`);break;case 401:e(`<PERSON><PERSON><PERSON>, Anda tidak memiliki izin untuk akses. (${b})`);break;case 403:e(`<PERSON><PERSON><PERSON>, <PERSON><PERSON>es ke sumber daya dilarang. (${b})`);break;case 404:e(`Error Refresh Token. Silahkan Login Ulang... (${b})`);break;case 429:e(`Terlalu Banyak Permintaan, Anda telah melebihi batas permintaan. (${b})`);break;case 422:e(`Unprocessable Entity, Permintaan tidak dapat diolah. (${b})`);break;case 500:e("Kesalahan Pada Query",b);break;case 503:e(`Layanan Tidak Tersedia, Layanan tidak tersedia saat ini. (${b})`);break;case 504:e(`Waktu Habis, Permintaan waktu habis. (${b})`);break;case 505:e(`Versi HTTP Tidak Didukung, Versi HTTP tidak didukung. (${b})`);break;case 507:e(`Penyimpanan Tidak Cukup, Penyimpanan tidak mencukupi. (${b})`);break;case 511:e(`Autentikasi Diperlukan, Autentikasi diperlukan. (${b})`);break;default:e(`Kesalahan Server, ${b} `)}}},8086:a=>{"use strict";a.exports=require("module")},8753:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(60687);c(43210);var e=c(44301),f=c(21988);!function(){var a=Error("Cannot find module '@/data/Kdunit.json'");throw a.code="MODULE_NOT_FOUND",a}();let g=a=>{let{value:b,onChange:c,status:g,size:h,placeholder:i,className:j,kddept:k,popoverClassName:l,triggerClassName:m,isDisabled:n,...o}=a;return(0,d.jsxs)(e.d,{isVirtualized:!0,selectedKeys:[b||"XX"],onSelectionChange:a=>{let b=Array.from(a)[0]||"XX";c&&c(b)},isDisabled:n||"pilihunit"!==g,size:h||"sm",placeholder:i||"Pilih Unit",className:j||"w-full min-w-0 max-w-full",disallowEmptySelection:!0,"aria-label":"Pilih Unit",classNames:{popoverContent:l||"w-80 sm:w-96",trigger:`${m||"w-full"} max-w-full`,value:"truncate pr-8 max-w-full overflow-hidden",mainWrapper:"w-full max-w-full",innerWrapper:"w-full max-w-full overflow-hidden",base:"w-full max-w-full",label:"truncate"},children:[(0,d.jsx)(f.y,{textValue:"Semua Unit",children:"Semua Unit"},"XX"),Object(function(){var a=Error("Cannot find module '@/data/Kdunit.json'");throw a.code="MODULE_NOT_FOUND",a}())(a=>!k||a.kddept===k).map(a=>(0,d.jsx)(f.y,{textValue:`${a.kdunit} - ${a.nmunit}`,children:(0,d.jsxs)("span",{className:"block whitespace-nowrap overflow-hidden text-ellipsis",children:[a.kdunit," - ",a.nmunit]})},a.kdunit))]})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12198:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(60687),e=c(43210),f=c(14221),g=c(44301),h=c(21988);!function(){var a=Error("Cannot find module '@/data/Kdkabkota.json'");throw a.code="MODULE_NOT_FOUND",a}();let i=a=>{let{role:b,kdlokasi:c}=(0,e.useContext)(f.A),i=a.kdlokasi||c,j=Object(function(){var a=Error("Cannot find module '@/data/Kdkabkota.json'");throw a.code="MODULE_NOT_FOUND",a}())(a=>i&&"XX"!==i&&a.kdlokasi===i&&"XX"!==a.kdkabkota),k=["XX",...j.map(a=>a.kdkabkota)],l=a.value||"XX",m=k.includes(l)?l:"XX";return(0,d.jsxs)(g.d,{isVirtualized:!0,selectedKeys:[m],onSelectionChange:b=>{let c=Array.from(b)[0]||"XX";a.onChange&&a.onChange(c)},isDisabled:a.isDisabled||"pilihkdkabkota"!==a.status,size:a.size||"sm",placeholder:a.placeholder||"Pilih Kabupaten/Kota",className:a.className||"min-w-0 flex-[2]",disallowEmptySelection:!0,"aria-label":"Pilih Kabupaten/Kota",children:[" ",(0,d.jsx)(h.y,{textValue:"Semua Kabupaten/Kota",children:"Semua Kabupaten/Kota"},"XX"),j.map(a=>(0,d.jsx)(h.y,{textValue:`${a.kdkabkota} - ${a.nmkabkota}`,children:(0,d.jsxs)("span",{className:"block whitespace-nowrap overflow-hidden text-ellipsis",children:[a.kdkabkota," - ",a.nmkabkota]})},a.kdkabkota))]})}},12412:a=>{"use strict";a.exports=require("assert")},14320:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(60687);c(43210);var e=c(44301),f=c(21988);!function(){var a=Error("Cannot find module '@/data/Kddekon.json'");throw a.code="MODULE_NOT_FOUND",a}();let g=a=>{let{value:b,onChange:c,status:g,size:h,placeholder:i,className:j,popoverClassName:k,triggerClassName:l,isDisabled:m}=a;return(0,d.jsxs)(e.d,{isVirtualized:!0,selectedKeys:[String(b||"XX")],onSelectionChange:a=>{let b=Array.from(a)[0]||"XX";c&&c(b)},isDisabled:m||"pilihdekon"!==g,size:h||"sm",placeholder:i||"Pilih Kewenangan",className:j||"w-full min-w-0 max-w-full",disallowEmptySelection:!0,"aria-label":"Pilih Kewenangan",classNames:{popoverContent:k||"w-80 sm:w-96",trigger:`${l||"w-full"} max-w-full`,value:"truncate pr-8 max-w-full overflow-hidden",mainWrapper:"w-full max-w-full",innerWrapper:"w-full max-w-full overflow-hidden",base:"w-full max-w-full",label:"truncate"},children:[" ",(0,d.jsx)(f.y,{textValue:"Semua Kewenangan",children:"Semua Kewenangan"},"XX"),Object(function(){var a=Error("Cannot find module '@/data/Kddekon.json'");throw a.code="MODULE_NOT_FOUND",a}())(a=>(0,d.jsx)(f.y,{textValue:`${a.kddekon} - ${a.nmdekon}`,children:(0,d.jsxs)("span",{className:"block whitespace-nowrap overflow-hidden text-ellipsis",children:[a.kddekon," - ",a.nmdekon]})},String(a.kddekon)))]})}},19080:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},22982:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("map",[["path",{d:"M14.106 5.553a2 2 0 0 0 1.788 0l3.659-1.83A1 1 0 0 1 21 4.619v12.764a1 1 0 0 1-.553.894l-4.553 2.277a2 2 0 0 1-1.788 0l-4.212-2.106a2 2 0 0 0-1.788 0l-3.659 1.83A1 1 0 0 1 3 19.381V6.618a1 1 0 0 1 .553-.894l4.553-2.277a2 2 0 0 1 1.788 0z",key:"169xi5"}],["path",{d:"M15 5.764v15",key:"1pn4in"}],["path",{d:"M9 3.236v15",key:"1uimfh"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},47820:(a,b,c)=>{"use strict";c.d(b,{A:()=>i});var d=c(60687),e=c(43210),f=c(44301),g=c(21988),h=c(14221);!function(){var a=Error("Cannot find module '@/data/Kdlokasi.json'");throw a.code="MODULE_NOT_FOUND",a}();let i=a=>{let{popoverClassName:b,triggerClassName:c}=a,{role:i,kdlokasi:j}=(0,e.useContext)(h.A),k="0"!==i&&"1"!==i&&"X"!==i&&""!==i&&i?Object(function(){var a=Error("Cannot find module '@/data/Kdlokasi.json'");throw a.code="MODULE_NOT_FOUND",a}())(a=>a.kdlokasi===j):Object(function(){var a=Error("Cannot find module '@/data/Kdlokasi.json'");throw a.code="MODULE_NOT_FOUND",a}()),l=["XX",...k.map(a=>a.kdlokasi)],m=a.value||"XX",n=l.includes(m)?m:"XX";return(0,d.jsxs)(f.d,{isVirtualized:!0,selectedKeys:new Set([n]),onSelectionChange:b=>{let c=Array.from(b)[0]||"XX";a.onChange&&a.onChange(c)},isDisabled:a.isDisabled||"pilihprov"!==a.status,size:a.size||"sm",placeholder:a.placeholder||"Pilih Provinsi",className:a.className||"w-full min-w-0 max-w-full",disallowEmptySelection:!0,"aria-label":"Pilih Provinsi",classNames:{popoverContent:b||"w-80 sm:w-96",trigger:`${c||"w-full"} max-w-full`,value:"truncate pr-8 max-w-full overflow-hidden",mainWrapper:"w-full max-w-full",innerWrapper:"w-full max-w-full overflow-hidden",base:"w-full max-w-full",label:"truncate"},children:[" ",(0,d.jsx)(g.y,{textValue:"Semua Provinsi",children:"Semua Provinsi"},"XX"),k.map(a=>(0,d.jsx)(g.y,{textValue:`${a.kdlokasi} - ${a.nmlokasi}`,children:(0,d.jsxs)("span",{className:"block whitespace-nowrap overflow-hidden text-ellipsis",children:[a.kdlokasi," - ",a.nmlokasi]})},a.kdlokasi))]})}},49530:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(dashboard)",{children:["inquiry-data",{children:["bansos",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,52192)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\inquiry-data\\bansos\\page.jsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,8413)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\layout.jsx"],loading:[()=>Promise.resolve().then(c.bind(c,48221)),"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\loading.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,53361)),"D:\\sintesaNEXT2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\sintesaNEXT2\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["D:\\sintesaNEXT2\\src\\app\\(dashboard)\\inquiry-data\\bansos\\page.jsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(dashboard)/inquiry-data/bansos/page",pathname:"/inquiry-data/bansos",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(dashboard)/inquiry-data/bansos/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},52192:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\sintesaNEXT2\\\\src\\\\app\\\\(dashboard)\\\\inquiry-data\\\\bansos\\\\page.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\app\\(dashboard)\\inquiry-data\\bansos\\page.jsx","default")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},59e3:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(4765),e=c.n(d);let f="mebe23",g=(a,b=f)=>{let c=e().AES.encrypt(JSON.stringify(a),b).toString();return e().enc.Base64.stringify(e().enc.Utf8.parse(c))}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67401:(a,b,c)=>{"use strict";c.d(b,{qs:()=>e});var d=c(77567);d.A.mixin({toast:!0,position:"top-start",showConfirmButton:!1,timer:3e3,timerProgressBar:!0,customClass:{popup:"custom-toast-font custom-toast-primary-light"},didOpen:a=>{a.onmouseenter=d.A.stopTimer,a.onmouseleave=d.A.resumeTimer}});let e=a=>{d.A.close(),setTimeout(()=>{d.A.fire({text:`${a} `,position:"top-end",showConfirmButton:!1,toast:!0,timer:5e3,background:"black",color:"#ffffff",showClass:{popup:"animate__animated "}})},500)}},71018:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(60687);c(43210);var e=c(44301),f=c(21988);!function(){var a=Error("Cannot find module '@/data/Kddept.json'");throw a.code="MODULE_NOT_FOUND",a}();let g=a=>{let{popoverClassName:b,triggerClassName:c,isDisabled:g,...h}=a;return(0,d.jsxs)(e.d,{isVirtualized:!0,selectedKeys:[a.value||"000"],onSelectionChange:b=>{let c=Array.from(b)[0]||"000";a.onChange&&a.onChange(c)},size:a.size||"sm",className:a.className||"w-full min-w-0 max-w-full",disallowEmptySelection:!0,isDisabled:g,"aria-label":"Pilih Kementerian",placeholder:"Pilih Kementerian",classNames:{popoverContent:b||"w-80 sm:w-96",trigger:`${c||"w-full"} max-w-full`,value:"truncate pr-8 max-w-full overflow-hidden",mainWrapper:"w-full max-w-full",innerWrapper:"w-full max-w-full overflow-hidden",base:"w-full max-w-full",label:"truncate"},children:[(0,d.jsx)(f.y,{textValue:"Semua Kementerian",children:"Semua Kementerian"},"000"),Object(function(){var a=Error("Cannot find module '@/data/Kddept.json'");throw a.code="MODULE_NOT_FOUND",a}())(a=>(0,d.jsx)(f.y,{textValue:`${a.kddept} - ${a.nmdept}`,children:(0,d.jsxs)("span",{className:"block whitespace-nowrap overflow-hidden text-ellipsis",children:[a.kddept," - ",a.nmdept]})},a.kddept))]})}},71495:(a,b,c)=>{Promise.resolve().then(c.bind(c,86893))},74075:a=>{"use strict";a.exports=require("zlib")},74593:(a,b,c)=>{"use strict";c.d(b,{A:()=>g});var d=c(60687);c(43210),function(){var a=Error("Cannot find module '@/data/Kdsatker.json'");throw a.code="MODULE_NOT_FOUND",a}();var e=c(44301),f=c(21988);let g=a=>{let{isDisabled:b,...c}=a,g=Object(function(){var a=Error("Cannot find module '@/data/Kdsatker.json'");throw a.code="MODULE_NOT_FOUND",a}())(b=>(!a.kddept||"XX"===a.kddept||b.kddept===a.kddept)&&(!a.kdunit||"XX"===a.kdunit||b.kdunit===a.kdunit)&&(!a.kdlokasi||"XX"===a.kdlokasi||b.kdlokasi===a.kdlokasi)&&(!a.kdkppn||"XX"===a.kdkppn||b.kdkppn===a.kdkppn));return(0,d.jsx)("div",{children:(0,d.jsx)("div",{className:"mt-2",children:(0,d.jsxs)(e.d,{selectedKeys:new Set(a.value?[a.value]:["XX"]),onSelectionChange:b=>{let c=Array.from(b)[0]||"XX";a.onChange&&a.onChange(c)},className:a.className||"form-select form-select-sm","aria-label":"Pilih Satker",isDisabled:b||"pilihsatker"!==a.status,disallowEmptySelection:!1,placeholder:a.placeholder||"Pilih Satker",size:a.size||"sm",children:[(0,d.jsx)(f.y,{value:"XX",textValue:"Semua Satker",children:"Semua Satker"},"XX"),g.map(a=>(0,d.jsxs)(f.y,{value:a.kdsatker,textValue:`${a.kdsatker} - ${a.nmsatker}`,children:[a.kdsatker," - ",a.nmsatker]},a.kdsatker))]})})})}},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86893:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>a_});var d=c(60687),e=c(67401),f=c(14221),g=c(43210),h=c.n(g),i=c(80505);let j=({id:a,checked:b,onChange:c,label:e,size:f="sm",disabled:g=!1})=>(0,d.jsxs)("div",{className:`flex items-center gap-3 px-2 py-1 rounded-2xl shadow-sm transition-all duration-300 group ${g?"opacity-50":""}`,children:[(0,d.jsx)(i.Z,{id:a,isSelected:b,onValueChange:g?void 0:c,size:f,isDisabled:g,"aria-label":e,"aria-labelledby":`${a}-label`,classNames:{wrapper:"group-data-[selected=true]:bg-gradient-to-r group-data-[selected=true]:from-purple-400 group-data-[selected=true]:to-blue-400",thumb:"group-data-[selected=true]:bg-white shadow-lg"}}),(0,d.jsx)("label",{id:`${a}-label`,htmlFor:a,className:`text-sm font-medium transition-colors duration-200 flex-1 ${g?"text-gray-400 cursor-not-allowed":"text-gray-700 group-hover:text-purple-600 cursor-pointer"}`,children:e})]});var k=c(27580),l=c(44301),m=c(21988),n=c(77611),o=c(41871),p=c(19080),q=c(96882);let r=({inquiryState:a})=>{let{bansostype:b,setBansostype:c,bansoskondisi:e,setBansoskondisi:f,katabansos:g,setKatabansos:h,bansosradio:i,setBansosradio:j}=a,r=g&&""!==g.trim(),s=e&&""!==e.trim(),t=b&&"XXX"!==b&&"XX"!==b&&"XX"!==b,u=r||s,v=r||t,w=s||t;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-purple-100 to-fuchsia-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(p.A,{size:20,className:"ml-4 text-secondary"}),"Jenis Bansos"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${u?"text-gray-400":"text-gray-700"}`,children:"Pilih Jenis Bansos"}),t&&!u&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(l.d,{"aria-label":"Pilih Jenis Bansos",className:"w-full min-w-0 max-w-full",size:"sm",selectedKeys:new Set([b||"XX"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&c&&c(b)},isDisabled:u,disallowEmptySelection:!0,children:[{value:"XX",label:"Semua Jenis Bansos"},{value:"01",label:"01 - BNPT"},{value:"02",label:"02 - BPUM"},{value:"03",label:"03 - Kuota Internet"},{value:"04",label:"04 - PKH"},{value:"05",label:"05 - POS BST"},{value:"06",label:"06 - POS Sembako"},{value:"07",label:"07 - Kartu Prakerja"},{value:"08",label:"08 - BST Sembako 1"},{value:"09",label:"09 - BST Sembako 2"},{value:"10",label:"10 - BST Sembako 3"},{value:"11",label:"11 - UKT"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(n.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(q.A,{size:15})})})]}),s&&!v&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>f&&f(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: 01,02,03, dst",className:"w-full min-w-0",size:"sm",value:e||"",isDisabled:v,onChange:a=>f&&f(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${w?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),r&&!w&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: BNPT",className:"w-full min-w-0",size:"sm",value:g||"",isDisabled:w,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(l.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([i||"1"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&j&&j(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]})]})})]})})};var s=c(71018),t=c(79410);let u=({inquiryState:a,status:b})=>{let{dept:c,setDept:e,deptradio:f,setDeptradio:g,deptkondisi:h,setDeptkondisi:i,katadept:j,setKatadept:p}=a||{},r=j&&""!==j.trim(),u=h&&""!==h.trim(),v=c&&"XXX"!==c&&"000"!==c&&"XX"!==c,w=r||u,x=r||v,y=u||v;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-purple-100 to-fuchsia-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(t.A,{size:20,className:"ml-4 text-secondary"}),"Kementerian"]})," ",(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[" ",(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${w?"text-gray-400":"text-gray-700"}`,children:"Pilih Kementerian"}),v&&!w&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>e&&e("000"),children:"Clear"})]}),(0,d.jsx)(s.A,{value:c,onChange:e,className:"w-full min-w-0 max-w-full",size:"sm",status:b,isDisabled:w})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${x?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(n.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(q.A,{size:15})})})]}),u&&!x&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>i&&i(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: 001,002,003, dst",className:"w-full min-w-0",size:"sm",value:h||"",isDisabled:x,onChange:a=>{let b=a.target.value;i&&i(b)}})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${y?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),r&&!y&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>p&&p(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: keuangan",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:y,onChange:a=>{let b=a.target.value;p&&p(b)}})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"})," ",(0,d.jsx)(l.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:[f||"1"],onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),!b){g&&g("1");return}g&&g(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var v=c(79300),w=c(8753);let x=({inquiryState:a})=>{let{dept:b,kdunit:c,setKdunit:e,unitkondisi:f,setUnitkondisi:g,kataunit:i,setKataunit:j,unitradio:p,setUnitradio:r}=a||{},s=i&&""!==i.trim(),t=f&&""!==f.trim(),u=c&&"XXX"!==c&&"XX"!==c,x=s||t,y=s||u,z=t||u;return h().useEffect(()=>{e&&e("XX")},[b,e]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-purple-100 to-fuchsia-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(v.A,{size:20,className:"ml-4 text-secondary"}),"Eselon I"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${x?"text-gray-400":"text-gray-700"}`,children:"Pilih Eselon I"}),u&&!x&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>e&&e("XX"),children:"Clear"})]}),(0,d.jsx)(w.A,{value:c,onChange:e,kddept:b,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihunit",isDisabled:x})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${y?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(n.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"ml-1 cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(q.A,{size:15})})})]}),t&&!y&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>g&&g(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: 01,02,03, dst",className:"w-full min-w-0",size:"sm",value:f||"",isDisabled:y,onChange:a=>g&&g(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${z?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),s&&!z&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: sekretariat",className:"w-full min-w-0",size:"sm",value:i||"",isDisabled:z,onChange:a=>j&&j(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(l.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([p||"1"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&r&&r(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var y=c(71850),z=c(14320);let A=({inquiryState:a})=>{let{dekon:b,setDekon:c,dekonkondisi:e,setDekonkondisi:f,katadekon:g,setKatadekon:h,dekonradio:i,setDekonradio:j}=a,p=g&&""!==g.trim(),r=e&&""!==e.trim(),s=b&&"XXX"!==b&&"XX"!==b&&"000"!==b&&""!==b.trim(),t=p||r,u=p||s,v=r||s;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-purple-100 to-fuchsia-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(y.A,{size:20,className:"ml-4 text-secondary"}),"Kewenangan"]})," ",(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${t?"text-gray-400":"text-gray-700"}`,children:"Pilih Kewenangan"}),s&&!t&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(z.A,{value:b,onChange:c,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihdekon",isDisabled:t})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${u?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(n.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(q.A,{size:15})})})]}),r&&!u&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>f&&f(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: DK,TP,UB, dst",className:"w-full min-w-0",size:"sm",value:e||"",isDisabled:u,onChange:a=>f&&f(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),p&&!v&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: dekonsentrasi",className:"w-full min-w-0",size:"sm",value:g||"",isDisabled:v,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"})," ",(0,d.jsx)(l.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([i]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&j(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var B=c(17313),C=c(74593);let D=({inquiryState:a})=>{let{satker:b,setSatker:c,dept:e,kdunit:f,prov:g,kppn:i,satkerkondisi:j,setSatkerkondisi:p,katasatker:r,setKatasatker:s,satkerradio:t,setSatkerradio:u}=a,v=r&&""!==r.trim(),w=j&&""!==j.trim(),x=b&&"XXX"!==b&&"XX"!==b,y=v||w,z=v||x,A=w||x;return h().useEffect(()=>{c&&c("XX")},[e,f,g,i,c]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-purple-100 to-fuchsia-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(B.A,{size:20,className:"text-secondary ml-4"}),"Satker"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${y?"text-gray-400":"text-gray-700"}`,children:"Pilih Satker"}),x&&!y&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(C.A,{value:b,onChange:c||(()=>console.warn("setSatker is undefined")),kddept:e,kdunit:f,kdlokasi:g,kdkppn:i,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Satker",status:"pilihsatker",isDisabled:y})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${z?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(n.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(q.A,{size:15})})})]}),w&&!z&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>p&&p(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: 647321,647322, dst",className:"w-full min-w-0",size:"sm",value:j||"",isDisabled:z,onChange:a=>p&&p(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${A?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),v&&!A&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>s&&s(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: universitas",className:"w-full min-w-0",size:"sm",value:r||"",isDisabled:A,onChange:a=>s&&s(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(l.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:[t||"1"],onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),"string"==typeof b&&b.startsWith("$.")&&(b=b.replace("$.","")),!b){u&&u("1");return}u&&u(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var E=c(97992),F=c(47820);let G=({inquiryState:a})=>{let{prov:b,setProv:c,locradio:e,setLocradio:f,lokasikondisi:g,setLokasikondisi:h,katalokasi:i,setKatalokasi:j}=a,p=i&&""!==i.trim(),r=g&&""!==g.trim(),s=b&&"XXX"!==b&&"XX"!==b&&"XX"!==b,t=p||r,u=p||s,v=r||s;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-purple-100 to-fuchsia-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(E.A,{size:20,className:"ml-4 text-secondary"}),"Provinsi"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${t?"text-gray-400":"text-gray-700"}`,children:"Pilih Provinsi"}),s&&!t&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(F.A,{value:b,onChange:c,className:"w-full min-w-0 max-w-full",size:"sm",status:"pilihprov",isDisabled:t})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${u?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(n.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(q.A,{size:15})})})]}),r&&!u&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: 31,32,33, dst",className:"w-full min-w-0",size:"sm",value:g||"",isDisabled:u,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),p&&!v&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: jawa",className:"w-full min-w-0",size:"sm",value:i||"",isDisabled:v,onChange:a=>j&&j(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(l.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([e||"1"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&f&&f(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Uraian"},{value:"3",label:"Kode Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})};var H=c(22982),I=c(12198);let J=({inquiryState:a})=>{let{kabkotavalue:b,setKabkotavalue:c,prov:e,kabkotakondisi:f,setKabkotakondisi:g,katakabkota:i,setKatakabkota:j,kabkotaradio:p,setKabkotaradio:r}=a,s=i&&""!==i.trim(),t=f&&""!==f.trim(),u=b&&"XXX"!==b&&"XX"!==b&&"XX"!==b,v=s||t,w=s||u,x=t||u;return h().useEffect(()=>{c&&c("XX")},[e,c]),(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-purple-100 to-fuchsia-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[" ",(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(H.A,{size:20,className:"ml-4 text-secondary"}),"Kabupaten/Kota"]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Pilih Kabupaten/Kota"}),u&&!v&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(I.A,{value:b,onChange:c||(()=>console.warn("setKabkotavalue is undefined")),kdlokasi:e,className:"w-full min-w-0 max-w-full",size:"sm",placeholder:"Pilih Kabupaten/Kota",status:"pilihkdkabkota",isDisabled:v})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${w?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(n.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(q.A,{size:15})})})]}),t&&!w&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs flex-shrink-0",onPress:()=>g&&g(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: 01,02,03, dst",className:"w-full min-w-0",size:"sm",value:f||"",isDisabled:w,onChange:a=>g&&g(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${x?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),s&&!x&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>j&&j(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: jakarta",className:"w-full min-w-0",size:"sm",value:i||"",isDisabled:x,onChange:a=>j&&j(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"})," ",(0,d.jsx)(l.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:[p||"1"],onSelectionChange:a=>{let b=a;if(a&&"string"!=typeof a&&a.size&&(b=Array.from(a)[0]),"string"==typeof b&&b.startsWith("$.")&&(b=b.replace("$.","")),!b){r&&r("1");return}r&&r(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]})]}),(0,d.jsxs)("div",{className:"hidden xl:flex xl:flex-row gap-4 w-full",children:[(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"}),(0,d.jsx)("div",{className:"flex-1"})]})]})]})})},K=({inquiryState:a})=>{let{kecamatanvalue:b,setKecamatanvalue:c,kecamatankondisi:e,setKecamatankondisi:f,katakecamatan:g,setKatakecamatan:h,kecamatanradio:i,setKecamatanradio:j}=a,p=g&&""!==g.trim(),r=e&&""!==e.trim(),s=b&&"XXX"!==b&&"XX"!==b&&"XX"!==b,t=p||r,u=p||s,v=r||s;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-purple-100 to-fuchsia-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(E.A,{size:20,className:"ml-4 text-secondary"}),"Kecamatan"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${t?"text-gray-400":"text-gray-700"}`,children:"Pilih Kecamatan"}),s&&!t&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(l.d,{"aria-label":"Pilih Kecamatan",className:"w-full min-w-0 max-w-full",size:"sm",selectedKeys:new Set([b||"XX"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&c&&c(b)},isDisabled:t,disallowEmptySelection:!0,children:[{value:"XX",label:"Semua Kecamatan"},{value:"1114012",label:"1114012 - Teunom"},{value:"1114022",label:"1114022 - Krueng Sabee"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${u?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(n.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(q.A,{size:15})})})]}),r&&!u&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>f&&f(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: 1114012,1114022, dst",className:"w-full min-w-0",size:"sm",value:e||"",isDisabled:u,onChange:a=>f&&f(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),p&&!v&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: teunom",className:"w-full min-w-0",size:"sm",value:g||"",isDisabled:v,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(l.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([i||"1"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&j&&j(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]})]})})]})})};var L=c(32192);let M=({inquiryState:a})=>{let{desavalue:b,setDesavalue:c,desakondisi:e,setDesakondisi:f,katadesa:g,setKatadesa:h,desaradio:i,setDesaradio:j}=a,p=g&&""!==g.trim(),r=e&&""!==e.trim(),s=b&&"XXX"!==b&&"XX"!==b&&"XX"!==b,t=p||r,u=p||s,v=r||s;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-purple-100 to-fuchsia-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(L.A,{size:20,className:"ml-4 text-secondary"}),"Desa"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${t?"text-gray-400":"text-gray-700"}`,children:"Pilih Desa"}),s&&!t&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(l.d,{"aria-label":"Pilih Desa",className:"w-full min-w-0 max-w-full",size:"sm",selectedKeys:new Set([b||"XX"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&c&&c(b)},isDisabled:t,disallowEmptySelection:!0,children:[{value:"XX",label:"Semua Desa"},{value:"1114012001",label:"1114012 001 - Keude Teunom"},{value:"1114012002",label:"1114012 002 - Alue Ambang"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${u?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(n.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(q.A,{size:15})})})]}),r&&!u&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>f&&f(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: 1114012001,1114012002, dst",className:"w-full min-w-0",size:"sm",value:e||"",isDisabled:u,onChange:a=>f&&f(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),p&&!v&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: keude",className:"w-full min-w-0",size:"sm",value:g||"",isDisabled:v,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(l.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([i||"1"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&j&&j(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]})]})})]})})};var N=c(5336);let O=({inquiryState:a})=>{let{statusvalue:b,setStatusvalue:c,statuskondisi:e,setStatuskondisi:f,katastatus:g,setKatastatus:h,statusradio:i,setStatusradio:j}=a,p=g&&""!==g.trim(),r=e&&""!==e.trim(),s=b&&"XXX"!==b&&"XX"!==b&&"XX"!==b,t=p||r,u=p||s,v=r||s;return(0,d.jsx)("div",{className:"p-3 sm:mx-16 rounded-2xl bg-gradient-to-r from-purple-100 to-fuchsia-100 dark:from-zinc-900 dark:to-zinc-900 shadow-sm",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center gap-4 w-full",children:[(0,d.jsxs)("h6",{className:"font-semibold flex items-center gap-2 lg:min-w-[100px] lg:flex-[2]",children:[(0,d.jsx)(N.A,{size:20,className:"ml-4 text-secondary"}),"Status Transaksi"]}),(0,d.jsx)("div",{className:"flex flex-col lg:flex-[8] gap-3 lg:gap-1 w-full",children:(0,d.jsxs)("div",{className:"flex flex-col xl:flex xl:flex-row xl:items-end gap-3 xl:gap-4 w-full",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1 min-w-0 max-w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${t?"text-gray-400":"text-gray-700"}`,children:"Pilih Status Transaksi"}),s&&!t&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>c&&c("XX"),children:"Clear"})]}),(0,d.jsx)(l.d,{"aria-label":"Pilih Status Transaksi",className:"w-full min-w-0 max-w-full",size:"sm",selectedKeys:new Set([b||"XX"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&c&&c(b)},isDisabled:t,disallowEmptySelection:!0,children:[{value:"XX",label:"Semua Status"},{value:"1",label:"1 - Transaksi Sukses"},{value:"2",label:"2 - Transaksi Gagal"},{value:"3",label:"3 - Transaksi Gagal, Sudah Setor Ke Kas Negara"},{value:"4",label:"4 - Transaksi Sukses, Saldo Lebih Dari 330.000, Belum Setor Ke Kas Negara"},{value:"5",label:"5 - Transaksi Sukses, Saldo Lebih Dari 330.000, Sudah Setor Ke Kas Negara"},{value:"0",label:"0 - Tanpa Status Konfirmasi"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${u?"text-gray-400":"text-gray-700"}`,children:"Masukkan Kondisi"}),(0,d.jsx)(n.I,{content:"Banyak kode pisahkan dengan koma, gunakan tanda ! di depan untuk exclude",showArrow:!0,delay:1e3,motionProps:{variants:{exit:{opacity:0,transition:{duration:.1,ease:"easeIn"}},enter:{opacity:1,transition:{duration:.15,ease:"easeOut"}}}},children:(0,d.jsx)("span",{className:"cursor-pointer text-gray-400 hover:text-gray-600",children:(0,d.jsx)(q.A,{size:15})})})]}),r&&!u&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>f&&f(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: 01,02,03, dst",className:"w-full min-w-0",size:"sm",value:e||"",isDisabled:u,onChange:a=>f&&f(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("label",{className:`text-sm font-medium ${v?"text-gray-400":"text-gray-700"}`,children:"Mengandung Kata"}),p&&!v&&(0,d.jsx)(k.T,{size:"sm",variant:"light",color:"warning",className:"h-6 px-2 text-xs",onPress:()=>h&&h(""),children:"Clear"})]}),(0,d.jsx)(o.r,{placeholder:"misalkan: berhasil",className:"w-full min-w-0",size:"sm",value:g||"",isDisabled:v,onChange:a=>h&&h(a.target.value)})]}),(0,d.jsxs)("div",{className:"flex flex-col gap-1 w-full xl:flex-1",children:[(0,d.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Jenis Tampilan"}),(0,d.jsx)(l.d,{"aria-label":"Pilih tampilan",className:"w-full min-w-0",size:"sm",selectedKeys:new Set([i||"1"]),onSelectionChange:a=>{let b=Array.from(a)[0];b&&j&&j(b)},disallowEmptySelection:!0,children:[{value:"1",label:"Kode"},{value:"2",label:"Kode Uraian"},{value:"3",label:"Uraian"},{value:"4",label:"Jangan Tampilkan"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]})]})})]})})},P=({inquiryState:a})=>{let{jenisbansos:b,setJenisbansos:c,kddept:e,setKddept:f,unit:g,setUnit:h,kddekon:i,setKddekon:k,kdsatker:l,setKdsatker:m,provinsi:n,setProvinsi:o,kabkota:p,setKabkota:q,kecamatan:s,setKecamatan:t,desa:v,setDesa:w,statustransaksi:y,setStatustransaksi:z}=a;return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-full p-3 mb-4 sm:p-4 bg-gradient-to-r from-purple-100 to-fuchsia-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl",children:(0,d.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-5 2xl:grid-cols-10 gap-2 sm:gap-3",children:[(0,d.jsx)(j,{id:"jenisbansos-filter",checked:b,onChange:c,label:"Jenis Bansos",disabled:!1}),(0,d.jsx)(j,{id:"kddept-filter",checked:e,onChange:f,label:"Kementerian",disabled:!1}),(0,d.jsx)(j,{id:"unit-filter",checked:g,onChange:h,label:"Eselon I",disabled:!1}),(0,d.jsx)(j,{id:"dekon-filter",checked:i,onChange:k,label:"Kewenangan",disabled:!1}),(0,d.jsx)(j,{id:"kdsatker-filter",checked:l,onChange:m,label:"Satker",disabled:!1}),(0,d.jsx)(j,{id:"provinsi-filter",checked:n,onChange:o,label:"Provinsi",disabled:!1}),(0,d.jsx)(j,{id:"kabkota-filter",checked:p,onChange:q,label:"Kabkota",disabled:!1}),(0,d.jsx)(j,{id:"kecamatan-filter",checked:s,onChange:t,label:"Kecamatan",disabled:!1}),(0,d.jsx)(j,{id:"desa-filter",checked:v,onChange:w,label:"Desa",disabled:!1}),(0,d.jsx)(j,{id:"statustransaksi-filter",checked:y,onChange:z,label:"Status Transaksi",disabled:!1})]})}),(0,d.jsxs)("div",{className:"space-y-4 mb-4",children:[b&&(0,d.jsx)(r,{inquiryState:a}),e&&(0,d.jsx)(u,{inquiryState:a}),g&&(0,d.jsx)(x,{inquiryState:a}),i&&(0,d.jsx)(A,{inquiryState:a}),l&&(0,d.jsx)(D,{inquiryState:a}),n&&(0,d.jsx)(G,{inquiryState:a}),p&&(0,d.jsx)(J,{inquiryState:a}),s&&(0,d.jsx)(K,{inquiryState:a}),v&&(0,d.jsx)(M,{inquiryState:a}),y&&(0,d.jsx)(O,{inquiryState:a})]})]})},Q=({inquiryState:a,onFilterChange:b})=>{let{thang:c,setThang:e,jenlap:f,setJenlap:g,pembulatan:i,setPembulatan:j,akumulatif:k,setAkumulatif:n}=a||{},[o,p]=h().useState("2025"),[q,r]=h().useState("1"),[s,t]=h().useState("1"),[u,v]=h().useState("0"),w=null!=c?c:o,x=null!=f?f:q,y=null!=i?i:s,z=null!=k?k:u;h().useEffect(()=>{b&&b({thang:w,jenlap:x,pembulatan:y,akumulatif:z})},[w,x,y,z,b]);let A=a=>b=>{let c=Array.from(b)[0];a&&void 0!==c&&a(c)};return(0,d.jsx)("div",{className:"mb-4",children:(0,d.jsx)("div",{className:"w-full p-3 mb-4 sm:p-4 bg-gradient-to-r from-purple-100 to-fuchsia-100 dark:from-zinc-900 dark:to-zinc-900 shadow-none rounded-2xl",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row gap-6 w-full",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("label",{id:"thang-label",className:"block text-sm font-medium mb-2",children:"Tahun Anggaran"}),(0,d.jsx)(l.d,{selectedKeys:[w],onSelectionChange:A(e||p),className:"w-full",placeholder:"Pilih Tahun",disallowEmptySelection:!0,"aria-labelledby":"thang-label","aria-label":"Pilih Tahun Anggaran",children:["2025","2024","2023","2022","2021","2020"].map(a=>(0,d.jsx)(m.y,{textValue:a,children:a},a))})]}),(0,d.jsxs)("div",{className:"flex-[1.5]",children:[(0,d.jsx)("label",{id:"jenlap-label",className:"block text-sm font-medium mb-2",children:"Jenis Laporan"}),(0,d.jsx)(l.d,{selectedKeys:[x],onSelectionChange:A(g||r),className:"w-full",placeholder:"Pilih Jenis Laporan",disallowEmptySelection:!0,"aria-labelledby":"jenlap-label","aria-label":"Pilih Jenis Laporan",children:[{value:"1",label:"Realisasi Bansos"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("label",{id:"akumulatif-label",className:"block text-sm font-medium mb-2",children:"Akumulatif"}),(0,d.jsx)(l.d,{selectedKeys:[z],onSelectionChange:A(n||v),className:"w-full",placeholder:"Pilih Akumulatif",disallowEmptySelection:!0,"aria-labelledby":"akumulatif-label","aria-label":"Pilih Akumulatif",children:[{value:"1",label:"Akumulatif"},{value:"0",label:"Non-Akumulatif"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("label",{id:"pembulatan-label",className:"block text-sm font-medium mb-2",children:"Pembulatan"}),(0,d.jsx)(l.d,{selectedKeys:[y],onSelectionChange:A(j||t),className:"w-full",placeholder:"Pilih Pembulatan",disallowEmptySelection:!0,"aria-labelledby":"pembulatan-label","aria-label":"Pilih Pembulatan",children:[{value:"1",label:"Rupiah"},{value:"1000",label:"Ribuan"},{value:"1000000",label:"Jutaan"},{value:"1000000000",label:"Miliar"},{value:"1000000000000",label:"Triliun"}].map(a=>(0,d.jsx)(m.y,{textValue:a.label,children:a.label},a.value))})]})]})})})};var R=c(21875),S=c(56093),T=c(55110),U=c(49995),V=c(17985),W=c(84292),X=c(53823),Y=c(75378),Z=c(69087),$=c(88977),_=c(14229),aa=c(37911),ab=c(10022),ac=c(5066),ad=c(11860),ae=c(16023);async function af(a,b="data.xlsx"){if(!a||!a.length)return;let d=await c.e(3103).then(c.bind(c,33103)),e=d.utils.json_to_sheet(a),f=d.utils.book_new();d.utils.book_append_sheet(f,e,"Sheet1");let g=new Blob([d.write(f,{bookType:"xlsx",type:"array"})],{type:"application/octet-stream"}),h=URL.createObjectURL(g),i=document.createElement("a");i.setAttribute("href",h),i.setAttribute("download",b),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(h)}async function ag(a,b="data.pdf"){if(!a||!a.length)return;let d=(await c.e(4403).then(c.bind(c,4403))).default,e=(await c.e(8848).then(c.bind(c,88848))).default,f=new d,g=Object.keys(a[0]),h=a.map(a=>g.map(b=>a[b]));e(f,{head:[g],body:h,styles:{fontSize:8},headStyles:{fillColor:[41,128,185]}}),f.save(b)}let ah=({showModalPDF:a,setShowModalPDF:b,selectedFormat:c,setSelectedFormat:e,fetchExportData:f,filename:g="data_export",loading:h})=>{let i=async()=>{try{let a=await f();if(!a||0===a.length)return;switch(c){case"pdf":await ag(a,`${g}.pdf`);break;case"excel":await af(a,`${g}.xlsx`);break;case"json":!function(a,b="data.json"){if(!a||!a.length)return;let c=new Blob([JSON.stringify(a,null,2)],{type:"application/json"}),d=URL.createObjectURL(c),e=document.createElement("a");e.setAttribute("href",d),e.setAttribute("download",b),e.style.visibility="hidden",document.body.appendChild(e),e.click(),document.body.removeChild(e),URL.revokeObjectURL(d)}(a,`${g}.json`);break;case"text":!function(a,b="data.txt"){if(!a||!a.length)return;let c=new Blob([JSON.stringify(a,null,2)],{type:"text/plain"}),d=URL.createObjectURL(c),e=document.createElement("a");e.setAttribute("href",d),e.setAttribute("download",b),e.style.visibility="hidden",document.body.appendChild(e),e.click(),document.body.removeChild(e),URL.revokeObjectURL(d)}(a,`${g}.txt`)}b(!1)}catch(a){console.error("Export failed",a)}};return(0,d.jsx)(R.Y,{isOpen:a,onClose:()=>b(!1),size:"3xl",scrollBehavior:"inside",backdrop:"blur",hideCloseButton:!0,classNames:{header:"bg-gradient-to-r from-green-200 to-emerald-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(S.g,{children:[(0,d.jsx)(T.c,{className:"flex justify-between items-center m-6",children:(0,d.jsxs)("div",{className:"text-lg font-semibold flex items-center",children:[(0,d.jsx)($.A,{className:"mr-2 text-success",size:20}),"Kirim Data ke WhatsApp"]})}),(0,d.jsx)(U.h,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Pilih format file untuk dikirim:"}),(0,d.jsxs)(V.U,{value:c,onValueChange:e,orientation:"horizontal",className:"flex flex-row gap-8 justify-center h-16 items-center",classNames:{wrapper:"gap-8 justify-center h-16 items-center"},children:[(0,d.jsx)(W.O,{value:"pdf",color:"danger",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(_.A,{className:"mr-2 text-red-600",size:18}),(0,d.jsx)("span",{children:"PDF"})]})}),(0,d.jsx)(W.O,{value:"excel",color:"success",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(aa.A,{className:"mr-2 text-green-600",size:18}),(0,d.jsx)("span",{children:"Excel (.xlsx)"})]})}),(0,d.jsx)(W.O,{value:"json",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(ab.A,{className:"mr-2 text-blue-600",size:18}),(0,d.jsx)("span",{children:"JSON"})]})}),(0,d.jsx)(W.O,{value:"text",color:"default",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(ac.A,{className:"mr-2 text-gray-600",size:18}),(0,d.jsx)("span",{children:"Text (.txt)"})]})})]}),(0,d.jsx)(X.y,{className:"my-2"}),(0,d.jsx)("div",{className:"text-xs text-gray-500",children:(0,d.jsxs)("p",{children:["Nama file: ",g,".",c]})})]})}),(0,d.jsxs)(Y.q,{className:"flex justify-between",children:[(0,d.jsx)(k.T,{color:"danger",variant:"light",onPress:()=>b(!1),disabled:h,startContent:(0,d.jsx)(ad.A,{size:16}),children:"Tutup"}),(0,d.jsx)(k.T,{color:"success",variant:"ghost",onPress:i,disabled:h,className:"w-[160px]",startContent:h?(0,d.jsx)(Z.o,{size:"sm"}):(0,d.jsx)(ae.A,{size:16}),children:h?"Mengirim...":"Kirim"})]})]})})};var ai=c(7192),aj=c(51034),ak=c.n(aj),al=c(99270),am=c(98564),an=c(85015),ao=c(55327),ap=c(80273),aq=c(92241),ar=c(98e3),as=c(76142),at=c(18445),au=c(42817),av=c(59e3);let aw=({isOpen:a,onClose:b,sql:c,from:e,thang:i,pembulatan:j})=>{let{axiosJWT:l,token:m,statusLogin:n}=(0,g.useContext)(f.A);(0,g.useEffect)(()=>{},[j]);let[p,q]=(0,g.useState)(!1),[r,s]=(0,g.useState)(""),[t,u]=(0,g.useState)(null),[v,w]=(0,g.useState)(!1),[x,y]=(0,g.useState)(0),[z,A]=(0,g.useState)(null),[B,C]=(0,g.useState)(null),[D,E]=(0,g.useState)(!1),[F,G]=(0,g.useState)(!0),[H,I]=(0,g.useState)(!1),[J,K]=(0,g.useState)(null),L=(0,g.useRef)(""),M=(0,g.useRef)({column:null,direction:null}),[N,O]=(0,g.useState)({column:null,direction:null}),[P,Q]=(0,g.useState)([]),[V,W]=(0,g.useState)(null),[X,$]=(0,g.useState)(!1),_=(0,g.useRef)(1),aa=async(a=1,b=!1)=>{if(!n||!c)return;let d=1===a;d&&!b?(q(!0),G(!0),Q([]),_.current=1):b&&(G(!1),I(!0)),$(!0),C(null);let e=performance.now();try{let f=c;if(M.current.column&&M.current.direction){let a=M.current.column,b="ascending"===M.current.direction?"ASC":"DESC";if(/\bORDER\s+BY\b/i.test(c))f=c.replace(/ORDER\s+BY\s+[^;]*/i,`ORDER BY ${a} ${b}`);else{let d=c.match(/(\s+LIMIT\s+)/i);f=d?c.replace(d[0],` ORDER BY ${a} ${b}${d[0]}`):`${c} ORDER BY ${a} ${b}`}}if(L.current&&L.current.trim()){let a=L.current.trim().replace(/'/g,"''"),b=/\bWHERE\b/i.test(c),d=c.match(/SELECT\s+(.*?)\s+FROM/i);if(d){let e=d[1],g=[];if("*"===e.trim());else if((g=e.split(",").map(a=>{let b=a.trim().split(/\s+AS\s+/i)[0].trim();return b=b.replace(/["`\[\]]/g,"")}).filter(a=>{let b=a.trim();return!(b.includes("(")||b.includes("*")||b.match(/^(COUNT|SUM|AVG|MAX|MIN|DISTINCT|CASE|IF|CONCAT|SUBSTRING|DATE|YEAR|MONTH|DAY)/i)||b.match(/^[0-9]+$/)||b.match(/^['"`].*['"`]$/)||b.match(/^NULL$/i)||0===b.length||b.includes("+")||b.includes("-")||b.includes("*")||b.includes("/")||b.includes("=")||b.includes("<")||b.includes(">"))&&b.match(/^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$/)})).length>0){let d=g.filter(a=>{let b=a.toUpperCase();return"PAGU"!==b&&"PAGU_APBN"!==b&&"PAGU_DIPA"!==b&&"REALISASI"!==b&&"BLOKIR"!==b});if(d.length>0){let e=d.map(b=>`(LOWER(CAST(${b} AS CHAR)) LIKE LOWER('%${a}%'))`).join(" OR "),g=`(${e})`;if(b){let a=c.match(/(\s+(GROUP\s+BY|ORDER\s+BY|HAVING|LIMIT)\s+)/i);f=a?c.replace(a[0],` AND ${g}${a[0]}`):`${c} AND ${g}`}else{let a=c.match(/(\s+(GROUP\s+BY|ORDER\s+BY|HAVING|LIMIT)\s+)/i);f=a?c.replace(a[0],` WHERE ${g}${a[0]}`):`${c} WHERE ${g}`}}}}}let g=encodeURIComponent(f),h=(0,av.A)(g),i=await l.post("http://localhost:88/next/inquiry",{sql:h,page:a},{timeout:3e4}),j=performance.now();if(u((j-e)/1e3),i.data){let c=i.data.data||[],e=i.data.total||0,f=i.data.totalPages||0,g=i.data.grandTotals||null;y(e),d&&g&&A(g);let h=!1;if(f>0)h=a<f;else if(e>0){let b=Math.ceil(e/100);h=a<b}else h=c.length>=100;W(h?(a+1).toString():null),_.current=a,b?Q(a=>[...a,...c]):Q(c)}else y(0),Q([]),W(null)}catch(e){let{status:a,data:c}=e.response||{},d=c&&c.error||e.message||"Terjadi Permasalahan Koneksi atau Server Backend";C(d),(0,ai.t)(a,d),y(0),b||(Q([]),W(null))}finally{$(!1),d&&!b?q(!1):b&&I(!1)}},[ab,ac]=(0,au.X)({hasMore:!!V,isEnabled:a&&n,shouldUseLoader:!0,onLoadMore:()=>{V&&!X&&aa(parseInt(V),!0)}});(0,g.useEffect)(()=>{if(a&&n&&c){let a=setTimeout(()=>{s(""),L.current="",O({column:null,direction:null}),M.current={column:null,direction:null},C(null),G(!0),aa(1,!1)},100);return()=>{clearTimeout(a)}}},[a,n,c]),(0,g.useEffect)(()=>{!a&&(C(null),s(""),L.current="",y(0),u(null),G(!0),I(!1),O({column:null,direction:null}),M.current={column:null,direction:null},W(null),J&&(clearTimeout(J),K(null)))},[a,J]),(0,g.useEffect)(()=>{p||X||G(!1)},[p,X]);let ae=a=>{let b=Number(a);return isNaN(b)?"0":"1000000000000"===j?new Intl.NumberFormat("de-DE",{minimumFractionDigits:0,maximumFractionDigits:2}).format(b):new Intl.NumberFormat("de-DE",{minimumFractionDigits:0,maximumFractionDigits:0}).format(b)},af={kddept:a=>String(a),kdsatker:a=>String(a)},ag=(0,g.useMemo)(()=>0===P.length?[]:Object.keys(P[0]),[P]),ah=(0,g.useMemo)(()=>0===P.length?{}:ag.reduce((a,b)=>(["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(b.toUpperCase())&&P.reduce((a,c)=>{let d=c[b];return isNaN(Number(d))||""===d||"boolean"==typeof d?a:a+1},0)/P.length>.7&&(a[b]=!0),a),{}),[P,ag]);h().useEffect(()=>{},[]);let aj=ag.length>0;return(0,d.jsx)(R.Y,{backdrop:"blur",isOpen:a,onClose:b,size:v?"full":"6xl",scrollBehavior:"inside",hideCloseButton:!0,className:v?"max-h-full":"h-[80vh] w-[80vw]",classNames:{header:"bg-gradient-to-r from-sky-200 to-cyan-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(S.g,{children:[(0,d.jsxs)(T.c,{className:"flex justify-between items-center m-6",children:[(0,d.jsx)("div",{className:"text-lg font-semibold",children:"Hasil Inquiry"}),(0,d.jsx)("div",{className:"flex items-center space-x-2",children:(0,d.jsx)(am.A,{isSelected:v,onValueChange:w,onChange:a=>{w(a.target.checked)},size:"sm",children:(0,d.jsx)("span",{className:"text-sm",children:"Layar Penuh"})})})]}),(0,d.jsxs)(U.h,{className:"flex flex-col h-full min-h-0 p-0",children:[(0,d.jsx)("div",{className:"flex justify-end items-center px-6",children:(0,d.jsx)("div",{className:"flex space-x-2",children:(0,d.jsx)(o.r,{placeholder:"Ketik untuk mencari Kode atau Nama",value:r,onChange:a=>{let b=a.target.value;if(s(b),L.current=b,C(null),J&&clearTimeout(J),""===b){aa(1,!1);let a=ac.current;a&&a.scrollTo({top:0,behavior:"smooth"}),K(null);return}let c=setTimeout(()=>{aa(1,!1);let a=ac.current;a&&a.scrollTo({top:0,behavior:"smooth"}),K(null)},300);K(c)},startContent:(0,d.jsx)(al.A,{size:16}),size:"md",className:"w-96"})})}),B?(0,d.jsxs)("div",{className:"text-center p-8 text-red-500",children:[(0,d.jsxs)("p",{children:["Error loading data: ",B]}),(0,d.jsxs)("div",{className:"mt-2 space-x-2",children:[(0,d.jsx)(k.T,{color:"primary",size:"sm",onClick:()=>{C(null),E(!0),setTimeout(()=>{aa(1,!1),E(!1)},100)},isLoading:D||p,children:"Retry"}),(0,d.jsx)(k.T,{color:"default",size:"sm",variant:"bordered",onClick:b,children:"Close"})]})]}):0!==P.length||p||X?0===ag.length?(0,d.jsx)("div",{className:"flex items-center justify-center h-full py-8",children:p||X?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(Z.o,{color:"primary",size:"lg",variant:"simple"}),(0,d.jsx)("span",{className:"text-lg text-gray-600 ml-6 flex gap-0.5",children:"Memproses query data...".split("").map((a,b)=>(0,d.jsx)("span",{style:{display:"inline-block",animation:"wave 1.2s infinite",animationDelay:`${.08*b}s`},children:" "===a?"\xa0":a},b))}),(0,d.jsx)("style",{children:`
                    @keyframes wave {
                      0%, 60%, 100% { transform: translateY(0); }
                      30% { transform: translateY(-8px); }
                    }
                  `})]}):(0,d.jsx)("span",{className:"text-sm text-gray-600",children:"No data available"})}):(0,d.jsx)("div",{className:"h-full overflow-auto px-6 py-1",ref:ac,children:(0,d.jsx)(an.Z,{className:"h-full p-4 shadow-none border-2",children:(0,d.jsxs)(ao.j,{"aria-label":"Inquiry results table",removeWrapper:!0,sortDescriptor:N,onSortChange:a=>{O(a),M.current=a,aa(1,!1);let b=ac.current;b&&b.scrollTo({top:0,behavior:"smooth"})},classNames:{base:"h-full overflow-auto",table:"h-full",th:"position: sticky top-0 z-20",wrapper:"h-full w-full "},children:[(0,d.jsxs)(ap.X,{children:[aj&&(0,d.jsx)(aq.e,{className:"text-center w-12 uppercase",children:"No"},"index"),ag.map(a=>{ah[a];let b=["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(a.toUpperCase()),c={};return["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(a.toUpperCase())&&(c={width:"160px",minWidth:"160px",maxWidth:"260px"}),(0,d.jsx)(aq.e,{allowsSorting:b,className:"text-center uppercase",style:c,children:a},a)})]}),(0,d.jsxs)(ar.E,{isLoading:!1,emptyContent:"No data to display",children:[0===P.length?(0,d.jsx)(as.s,{children:(0,d.jsx)(at.w,{colSpan:ag.length+ +!!aj,className:"text-center",children:r?`Tidak ada hasil untuk pencarian: "${r}"`:"No data available"})}):P.map((a,b)=>(0,d.jsxs)(as.s,{children:[aj&&(0,d.jsx)(at.w,{className:"text-center",children:b+1}),ag.map(b=>(0,d.jsx)(at.w,{className:ah[b]?"text-right":"text-center",children:af[b]?af[b](a[b]):ah[b]&&!isNaN(Number(a[b]))?ae(a[b]):a[b]},b))]},`${a.id||b}`)),P.length>0&&(0,d.jsx)(as.s,{children:(0,d.jsx)(at.w,{colSpan:ag.length+ +!!aj,className:`text-center ${H?"py-4":"py-2"}`,style:{minHeight:"40px"},children:(0,d.jsx)("div",{ref:ab,className:"w-full",children:H?(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[(0,d.jsx)(Z.o,{color:"primary",size:"md",variant:"simple"}),(0,d.jsx)("span",{className:"text-sm text-default-600",children:"Memuat data selanjutnya..."})]}):(0,d.jsx)("div",{className:"h-1 w-full flex items-center justify-center",children:!1})})})}),P.length>0&&(0,d.jsxs)(as.s,{className:"sticky bottom-0 bg-default-100 z-20 rounded-lg",children:[aj&&(0,d.jsx)(at.w,{className:"text-center font-medium text-foreground-600 bg-default-100 first:rounded-l-lg"}),ag.map((a,b)=>{let c=ah[a],e=a.toUpperCase(),f=0;c&&["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(e)&&(f=P.reduce((b,c)=>{let d=Number(c[a]);return isNaN(d)?b:b+d},0));let g=ag.findLastIndex(a=>!ah[a]);return(0,d.jsx)(at.w,{className:`${c?"text-right":"text-center"} font-medium text-foreground-600 bg-default-100 uppercase ${0===b&&!aj?"first:rounded-l-lg":""} ${b===ag.length-1?"last:rounded-r-lg":""}`,children:c&&["PAGU","PAGU_APBN","PAGU_DIPA","REALISASI","BLOKIR"].includes(e)?ae(f):b===g?"GRAND TOTAL":""},a)})]})]})]})})}):(0,d.jsx)("div",{className:"text-center p-8 text-gray-500",children:r?(0,d.jsxs)("div",{children:[(0,d.jsxs)("p",{children:['Tidak ada hasil ditemukan untuk pencarian: "',r,'"']}),(0,d.jsx)("p",{className:"text-sm mt-2",children:"Coba gunakan kata kunci yang berbeda"})]}):(0,d.jsxs)("div",{children:["No data available",!1]})})]}),(0,d.jsx)(Y.q,{children:(0,d.jsxs)("div",{className:"flex justify-between items-center gap-8 w-full",children:[(0,d.jsx)("div",{className:"flex text-sm",children:x>0?(0,d.jsxs)(d.Fragment,{children:["Total Baris: ",ak()(x).format("0,0"),", Ditampilkan:"," ",P.length," item",r&&` (hasil pencarian: "${r}")`]}):r?`Tidak ada hasil untuk pencarian: "${r}"`:"No data"}),(0,d.jsx)(k.T,{color:"danger",variant:"ghost",className:"w-[120px]",onPress:b,startContent:(0,d.jsx)(ad.A,{size:16}),children:"Tutup"})]})})]})})};var ax=c(61611),ay=c(8819),az=c(40611),aA=c(30485),aB=c(62085);let aC=({isOpen:a,onClose:b,query:c,thang:e,queryType:h="INQUIRY"})=>{let[i,j]=(0,g.useState)(!1),{axiosJWT:n,token:p,name:q}=(0,g.useContext)(f.A),{showToast:r}=(0,az.d)(),s=aA.Ik().shape({queryName:aA.Yj().required("Nama Query harus diisi"),queryType:aA.Yj().required("Tipe Query harus dipilih")}),t={queryName:"",queryType:h,thang:e||new Date().getFullYear().toString()},u=async(a,{resetForm:d})=>{j(!0);try{let e={tipe:a.queryType,nama:a.queryName,name:q,query:c,thang:a.thang};await n.post("http://localhost:88/user/simpanquery",e,{headers:{Authorization:`Bearer ${p}`,"Content-Type":"application/json"}}),r("Query berhasil disimpan","success"),d(),b()}catch(a){r(a.response?.data?.error||"Gagal menyimpan query","error")}finally{j(!1)}};return(0,d.jsx)(R.Y,{isOpen:a,onClose:b,size:"3xl",scrollBehavior:"inside",backdrop:"blur",hideCloseButton:!0,classNames:{header:"bg-gradient-to-r from-yellow-200 to-amber-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(S.g,{children:[(0,d.jsx)(T.c,{className:"flex justify-between items-center m-6",children:(0,d.jsxs)("div",{className:"text-lg font-semibold flex items-center",children:[(0,d.jsx)(ax.A,{className:"mr-2 text-blue-600",size:20}),"Simpan Query"]})}),(0,d.jsx)(aB.l1,{initialValues:t,validationSchema:s,onSubmit:u,children:({values:a,errors:c,touched:e,handleChange:f,isSubmitting:g})=>(0,d.jsxs)(aB.lV,{children:[(0,d.jsx)(U.h,{children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tahun Anggaran"}),(0,d.jsx)(o.r,{name:"thang",value:a.thang,onChange:f,disabled:!0,className:"bg-gray-100"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Tipe Query"}),(0,d.jsxs)(l.d,{name:"queryType",value:a.queryType,onChange:f,disabled:i,children:[(0,d.jsx)(m.y,{value:"INQUIRY",children:"Inquiry"},"INQUIRY"),(0,d.jsx)(m.y,{value:"BELANJA",children:"Belanja"},"BELANJA"),(0,d.jsx)(m.y,{value:"PENERIMAAN",children:"Penerimaan"},"PENERIMAAN"),(0,d.jsx)(m.y,{value:"BLOKIR",children:"Blokir"},"BLOKIR")]}),c.queryType&&e.queryType&&(0,d.jsx)("div",{className:"text-red-500 text-xs mt-1",children:c.queryType})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nama Query"}),(0,d.jsx)(o.r,{name:"queryName",value:a.queryName,onChange:f,placeholder:"Masukkan nama untuk query ini...",disabled:i}),c.queryName&&e.queryName&&(0,d.jsx)("div",{className:"text-red-500 text-xs mt-1",children:c.queryName})]}),(0,d.jsx)("div",{className:"text-xs text-gray-500 italic",children:"*) Query yang tersimpan dapat diakses di menu Profile, tab Query Data"})]})}),(0,d.jsxs)(Y.q,{className:"flex justify-between",children:[(0,d.jsx)(k.T,{color:"danger",variant:"light",onPress:b,disabled:i,startContent:(0,d.jsx)(ad.A,{size:16}),children:"Tutup"}),(0,d.jsx)(k.T,{color:"warning",variant:"ghost",type:"submit",disabled:i,className:"w-[160px]",startContent:i?(0,d.jsx)(Z.o,{size:"sm"}):(0,d.jsx)(ay.A,{size:16}),children:i?"Menyimpan...":"Simpan Query"})]})]})})]})})};var aD=c(13964),aE=c(70615);let aF=({isOpen:a,onClose:b,query:c,title:e})=>{let[f,h]=(0,g.useState)(!1),i=async()=>{if(c)try{await navigator.clipboard.writeText(c),h(!0),setTimeout(()=>h(!1),1500)}catch(a){h(!1)}};return(0,d.jsx)(R.Y,{isOpen:a,onClose:b,size:"3xl",scrollBehavior:"inside",backdrop:"blur",hideCloseButton:!0,classNames:{header:"bg-gradient-to-r from-gray-200 to-zinc-200 dark:from-zinc-800 dark:to-zinc-800 rounded-xl"},children:(0,d.jsxs)(S.g,{children:[(0,d.jsx)(T.c,{className:"flex justify-between items-center m-6",children:(0,d.jsx)("div",{className:"text-lg font-semibold",children:e||"SQL Preview"})}),(0,d.jsx)(U.h,{children:(0,d.jsx)("div",{className:"bg-gray-100 p-8 rounded-xl overflow-auto max-h-[60vh]",children:(0,d.jsx)("pre",{className:"whitespace-pre-wrap text-sm font-mono text-gray-800",style:{textAlign:"center"},children:c&&c.replace(/\s+/g," ").trim()})})}),(0,d.jsxs)(Y.q,{className:"flex justify-between",children:[(0,d.jsx)(k.T,{color:"danger",variant:"light",onPress:b,startContent:(0,d.jsx)(ad.A,{size:16}),children:"Tutup"}),(0,d.jsx)(k.T,{color:"default",variant:"ghost",onPress:i,startContent:f?(0,d.jsx)(aD.A,{size:16}):(0,d.jsx)(aE.A,{size:16}),children:f?"Tersalin!":"Salin ke Clipboard"})]})]})})};var aG=c(36220),aH=c(2840),aI=c(97840),aJ=c(78122),aK=c(31158);let aL=({onExecuteQuery:a,onExportExcel:b,onExportCSV:c,onExportPDF:e,onReset:f,onSaveQuery:g,onShowSQL:h,isLoading:i})=>(0,d.jsx)(an.Z,{className:"mb-4 shadow-none bg-transparent",children:(0,d.jsx)(aG.U,{children:(0,d.jsxs)("div",{className:"flex flex-wrap gap-6 justify-center md:justify-center",children:[(0,d.jsx)(k.T,{color:"primary",startContent:(0,d.jsx)(aI.A,{size:16}),onClick:a,isLoading:i,className:"w-[160px] h-[50px]",children:"Tayang Data"}),(0,d.jsx)(k.T,{color:"danger",variant:"ghost",startContent:(0,d.jsx)(aJ.A,{size:16}),onClick:f,isDisabled:i,className:"w-[160px] h-[50px]",children:"Reset Filter"}),(0,d.jsxs)(aH.x,{children:[(0,d.jsx)(k.T,{color:"success",variant:"flat",startContent:(0,d.jsx)(aK.A,{size:16}),onClick:b,isDisabled:i,className:"w-[120px] h-[50px]",children:"Excel"}),(0,d.jsx)(k.T,{color:"secondary",variant:"flat",startContent:(0,d.jsx)(aK.A,{size:16}),onClick:c,isDisabled:i,className:"w-[120px] h-[50px]",children:"CSV"})]}),(0,d.jsx)(k.T,{color:"success",variant:"flat",startContent:(0,d.jsx)($.A,{size:16}),onClick:e,isDisabled:i,className:"w-[160px] h-[50px]",children:"Kirim WA"}),(0,d.jsx)(k.T,{color:"warning",variant:"flat",startContent:(0,d.jsx)(ay.A,{size:16}),onClick:g,isDisabled:i,className:"w-[160px] h-[50px]",children:"Simpan Query"}),(0,d.jsx)(k.T,{color:"default",variant:"flat",startContent:(0,d.jsx)(ab.A,{size:16}),onClick:h,isDisabled:i,className:"w-[160px] h-[50px]",children:"Tayang SQL"})]})})});class aM{constructor(a,b,c=null){this.fieldName=a,this.tableName=b,this.referenceTable=c}buildColumns(a,b="",c={}){let d={columns:[],joinClause:"",groupBy:[]};if(!a)return d;let e=`a.${this.fieldName}`,f=this.referenceTable?`${this.referenceTable.alias}.${this.referenceTable.nameField}`:null,g=c&&"1"===c.jenlap?"a.pagu_apbn":"a.pagu";switch(a){case"1":d.columns.push(e),d.groupBy.push(e);break;case"2":d.columns.push(e),f&&(d.columns.push(f),d.joinClause=this.buildJoinClause(b)),d.groupBy.push(e);break;case"3":f&&(d.columns.push(f),d.joinClause=this.buildJoinClause(b)),d.groupBy.push(e)}return d.paguField=g,d}buildJoinClause(a=""){if(!this.referenceTable)return"";let b=this.referenceTable.hasYear?`_${a}`:"",c=`${this.referenceTable.schema}.${this.referenceTable.table}${b}`;return` LEFT JOIN ${c} ${this.referenceTable.alias} ON ${this.referenceTable.joinCondition}`}buildWhereConditions(a){let b=[],{pilihValue:c,kondisiValue:d,kataValue:e,opsiType:f,defaultValues:g=["XXX","000","XX","00","XXXX","0000","XXXXXX","000000"]}=a;if(e&&""!==e.trim()){let a=this.referenceTable?`${this.referenceTable.alias}.${this.referenceTable.nameField}`:`a.${this.fieldName}`;b.push(`${a} LIKE '%${e}%'`)}else d&&""!==d.trim()?b.push(this.parseKondisiConditions(d)):c&&!g.includes(c)&&b.push(`a.${this.fieldName} = '${c}'`);return b.filter(a=>a&&""!==a.trim())}parseKondisiConditions(a){if(!a||""===a.trim())return"";let b=`a.${this.fieldName}`;if("!"===a.substring(0,1)){let c=a.substring(1).split(",").map(a=>a.trim()).filter(a=>""!==a);if(c.length>0){let a=c.map(a=>`'${a}'`).join(",");return`${b} NOT IN (${a})`}}else if(a.includes("%"))return`${b} LIKE '${a}'`;else if(a.includes("-")&&!a.includes(",")){let[c,d]=a.split("-").map(a=>a.trim());if(c&&d)return`${b} BETWEEN '${c}' AND '${d}'`}else{let c=a.split(",").map(a=>a.trim()).filter(a=>""!==a);if(c.length>0){let a=c.map(a=>`'${a}'`).join(",");return`${b} IN (${a})`}}return""}build(a,b=""){let{isEnabled:c,radio:d,pilihValue:e,kondisiValue:f,kataValue:g,opsiType:h}=a,i={columns:[],joinClause:"",groupBy:[],whereConditions:[]};if(!c)return i;let j=this.buildColumns(d,b);if(i.columns=j.columns,i.joinClause=j.joinClause,i.groupBy=j.groupBy,g&&""!==g.trim()&&this.referenceTable){let a=`${this.referenceTable.alias}.${this.referenceTable.nameField}`;i.joinClause||(i.joinClause=this.buildJoinClause(b)),i.columns.includes(a)||i.columns.push(a)}return i.whereConditions=this.buildWhereConditions({pilihValue:e,kondisiValue:f,kataValue:g,opsiType:h}),i}getEmptyResult(){return{columns:[],joinClause:"",whereConditions:[],groupBy:[]}}}let aN=aM;class aO extends aN{constructor(){super("kddept","department",{schema:"dbref",table:"t_dept",alias:"b",nameField:"nmdept",hasYear:!0,joinCondition:"a.kddept=b.kddept"})}buildFromState(a){let{kddept:b,dept:c,deptkondisi:d,katadept:e,deptradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aP extends aN{constructor(){super("kdunit","unit",{schema:"dbref",table:"t_unit",alias:"c",nameField:"nmunit",hasYear:!0,joinCondition:"a.kddept=c.kddept AND a.kdunit=c.kdunit"})}buildFromState(a){let{unit:b,kdunit:c,unitkondisi:d,kataunit:e,unitradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aQ extends aN{constructor(){super("kddekon","dekonsentrasi",{schema:"dbref",table:"t_dekon",alias:"d",nameField:"nmdekon",hasYear:!0,joinCondition:"a.kddekon=d.kddekon"})}buildFromState(a){let{kddekon:b,dekon:c,dekonkondisi:d,katadekon:e,dekonradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aR extends aN{constructor(){super("kdsatker","satker",{schema:"dbref",table:"t_satker",alias:"s",nameField:"nmsatker",hasYear:!0,joinCondition:"a.kddept=s.kddept AND a.kdunit=s.kdunit AND a.kdsatker=s.kdsatker"})}buildFromState(a){let{kdsatker:b,satker:c,satkerkondisi:d,katasatker:e,satkerradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aS extends aN{constructor(){super("kdprov","provinsi",{schema:"dbref",table:"t_provinsi",alias:"e",nameField:"nmprov",hasYear:!1,joinCondition:"a.kdprov=e.kdprov"})}buildFromState(a){let{provinsi:b,prov:c,lokasikondisi:d,katalokasi:e,locradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aT extends aN{constructor(){super("kdkabkota","kabkota",{schema:"dbref",table:"t_kabkota_bansos",alias:"f",nameField:"nmkabkota",hasYear:!1,joinCondition:"a.kdprov=f.kdprov AND a.kdkabkota=f.kdkabkota"})}buildFromState(a){let{kabkota:b,kabkotavalue:c,kabkotakondisi:d,katakabkota:e,kabkotaradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aU extends aN{constructor(){super("kdjenis_transaksi","jenis_bansos",{schema:"dbref",table:"t_jenis_bansos",alias:"k",nameField:"jenis_transaksi",hasYear:!1,joinCondition:"a.kdjenis_transaksi=k.kdjenis_transaksi"})}buildFromState(a){let{jenisbansos:b,bansostype:c,bansoskondisi:d,katabansos:e,bansosradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aV extends aN{constructor(){super("return_code","status_transaksi",{schema:"dbref",table:"t_return_status",alias:"dd",nameField:"nmreturn",hasYear:!1,joinCondition:"a.return_code=dd.return_code"})}buildFromState(a){let{statustransaksi:b,statusvalue:c,statuskondisi:d,katastatus:e,statusradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aW extends aN{constructor(){super("kdkec","kecamatan",{schema:"dbref",table:"t_kecamatan",alias:"h",nameField:"nmkec",hasYear:!1,joinCondition:"a.kdprov=h.kdprov and a.kdkabkota=h.kdkabkota and a.kdkec=h.kdkec"})}buildFromState(a){let{kecamatan:b,kecamatanvalue:c,kecamatankondisi:d,katakecamatan:e,kecamatanradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aX extends aN{constructor(){super("kddesa","desa",{schema:"dbref",table:"t_desa",alias:"ii",nameField:"nmdesa",hasYear:!1,joinCondition:"a.kdprov=ii.kdprov and a.kdkabkota=ii.kdkabkota and a.kdkec=ii.kdkec and a.kddesa=ii.kddesa"})}buildFromState(a){let{desa:b,desavalue:c,desakondisi:d,katadesa:e,desaradio:f,thang:g}=a;return this.build({isEnabled:b,radio:f,pilihValue:c,kondisiValue:d,kataValue:e},g)}}class aY{constructor(){this.filters={jenisbansos:new aU,department:new aO,unit:new aP,dekon:new aQ,satker:new aR,provinsi:new aS,kabkota:new aT,kecamatan:new aW,desa:new aX,statustransaksi:new aV},this.filterStateMap={jenisbansos:"jenisbansos",department:"kddept",unit:"unit",dekon:"kddekon",satker:"kdsatker",provinsi:"provinsi",kabkota:"kabkota",kecamatan:"kecamatan",desa:"desa",statustransaksi:"statustransaksi"}}isFilterEnabled(a,b){let c=this.filterStateMap[a];if(!c)return console.warn(`Unknown filter key: ${a}`),!1;let d=!!b[c];return console.log(`🔍 isFilterEnabled ${a}:`,{stateKey:c,stateValue:b[c],isEnabled:d}),d}buildFilter(a,b){let c=this.filters[a];if(!c)throw Error(`Bansos Filter ${a} not found`);return c.buildFromState?c.buildFromState(b):c.build(b)}buildAllFilters(a){let b={columns:[],joinClauses:[],groupBy:[],whereConditions:[]};return Object.entries(this.filters).forEach(([c,d])=>{let e=this.isFilterEnabled(c,a);if(console.log(`🔍 Bansos Filter ${c}:`,{enabled:e,stateKey:this.filterStateMap[c]}),e)try{let d=this.buildFilter(c,a);console.log(`✅ Filter ${c} result:`,d),d.columns&&b.columns.push(...d.columns),d.joinClause&&b.joinClauses.push(d.joinClause),d.groupBy&&b.groupBy.push(...d.groupBy),d.whereConditions&&b.whereConditions.push(...d.whereConditions)}catch(a){console.error(`❌ Error building filter ${c}:`,a)}}),console.log("\uD83D\uDD0D buildAllFilters final result:",b),b}getFilterMetrics(a){let b=Object.keys(this.filters).filter(b=>this.isFilterEnabled(b,a));return{totalFilters:Object.keys(this.filters).length,enabledFilters:b.length,enabledFilterNames:b,filterUtilization:b.length/Object.keys(this.filters).length*100}}validateFilters(a){let b=[],c=[],d=this.getFilterMetrics(a);return 0===d.enabledFilters&&b.push("No filters are enabled - query may return too much data"),d.enabledFilters>5&&c.push("Consider reducing the number of active filters for better performance"),Object.entries(this.filterStateMap).forEach(([c,d])=>{this.isFilterEnabled(c,a)&&!a[d]&&b.push(`Filter ${c} is enabled but ${d} is missing`)}),{isValid:0===b.length,warnings:b,recommendations:c,stats:d}}}class aZ{constructor(){this.filterBuilder=new aY}buildQuery(a){try{let b=performance.now(),c=this.buildSelectClause(a),d=this.buildFromClause(a),e=this.buildJoinClause(a),f=this.buildWhereClause(a),g=this.buildGroupByClause(a),h=this.buildOrderByClause(a),i=[c,d,e,f,g,h].filter(a=>a&&""!==a.trim()).join(" "),j=performance.now();return console.log(`🚀 Bansos Query built in ${(j-b).toFixed(2)}ms`),i}catch(a){return console.error("Error building Bansos query:",a),""}}buildSelectClause(a){let b=this.filterBuilder.buildAllFilters(a);console.log("\uD83D\uDD0D buildSelectClause - filterResult.columns:",b.columns);let c=[...b.columns||[],"a.tahap AS TAHAP",...this.buildMonthColumns(a)];console.log("\uD83D\uDD0D buildSelectClause - allColumns:",c);let d=`SELECT ${c.join(", ")}`;return console.log("\uD83D\uDD0D buildSelectClause - final SELECT:",d),d}buildMonthColumns(a){let{akumulatif:b}=a,c=[];if("1"===b)for(let a=1;a<=12;a++){let b=[];for(let c=1;c<=a;c++)b.push(`JML${c}`);let d=b.length>1?`(${b.map(a=>`SUM(${a})`).join(" + ")})`:`SUM(${b[0]})`,e=[];for(let b=1;b<=a;b++)e.push(`REAL${b}`);let f=e.length>1?`(${e.map(a=>`SUM(${a})`).join(" + ")}) / 1`:`SUM(${e[0]}) / 1`;c.push(`${d} AS JML${a}`),c.push(`${f} AS REAL${a}`)}else for(let a=1;a<=12;a++)c.push(`SUM(JML${a}) AS JML${a}`),c.push(`SUM(REAL${a})/1 AS REAL${a}`);return c}buildFromClause(a){let{thang:b}=a;return`FROM monev${b}.bansos_pkh_bulanan a`}buildJoinClause(a){let b=this.filterBuilder.buildAllFilters(a);console.log("\uD83D\uDD0D buildJoinClause - filterResult.joinClauses:",b.joinClauses);let c=(b.joinClauses||[]).join(" ");return console.log("\uD83D\uDD0D buildJoinClause - final JOIN:",c),c}buildWhereClause(a){let b=this.filterBuilder.buildAllFilters(a);console.log("\uD83D\uDD0D buildWhereClause - filterResult.whereConditions:",b.whereConditions);let c=b.whereConditions||[],d=c.length>0?`WHERE ${c.join(" AND ")}`:"";return console.log("\uD83D\uDD0D buildWhereClause - final WHERE:",d),d}buildGroupByClause(a){let b=this.filterBuilder.buildAllFilters(a);console.log("\uD83D\uDD0D buildGroupByClause - filterResult.groupBy:",b.groupBy);let c=[...new Set([...b.groupBy||[],"a.tahap"])];console.log("\uD83D\uDD0D buildGroupByClause - allGroupBy:",c);let d=c.length>0?`GROUP BY ${c.join(", ")}`:"";return console.log("\uD83D\uDD0D buildGroupByClause - final GROUP BY:",d),d}buildOrderByClause(a){return""}generateSqlPreview(a){return{selectClause:this.buildSelectClause(a),fromClause:this.buildFromClause(a),joinClause:this.buildJoinClause(a),whereClause:this.buildWhereClause(a),groupByClause:this.buildGroupByClause(a),orderByClause:this.buildOrderByClause(a)}}getQueryPerformanceMetrics(a){let b=performance.now(),c=this.buildQuery(a),d=performance.now(),e=this.filterBuilder.getFilterMetrics(a),f=this.filterBuilder.validateFilters(a);return{buildTime:d-b,queryLength:c.length,filterStats:e,validation:f,recommendations:this.getQueryOptimizationRecommendations(a)}}getQueryOptimizationRecommendations(a){let b=[],c=this.filterBuilder.getFilterMetrics(a);return 0===c.enabledFilters&&b.push("Add filters to improve query performance"),c.enabledFilters>6&&b.push("Consider reducing filters for better performance"),b}}let a$=()=>{let a=function(){let{role:a,telp:b,verified:c,loadingExcell:d,setloadingExcell:e,kdkppn:h,kdkanwil:i,settampilAI:j}=(0,g.useContext)(f.A),[k,l]=(0,g.useState)(!1),[m,n]=(0,g.useState)(!1),[o,p]=(0,g.useState)(!1),[q,r]=(0,g.useState)(!1),[s,t]=(0,g.useState)(!1),[u,v]=(0,g.useState)(!1),[w,x]=(0,g.useState)(!1),[y,z]=(0,g.useState)(!1),[A,B]=(0,g.useState)(!1),[C,D]=(0,g.useState)(!1),[E,F]=(0,g.useState)(!1),[G,H]=(0,g.useState)(!1),[I,J]=(0,g.useState)("1"),[K,L]=(0,g.useState)(new Date().getFullYear().toString()),[M,N]=(0,g.useState)("1"),[O,P]=(0,g.useState)("0"),[Q,R]=(0,g.useState)("pdf"),[S,T]=(0,g.useState)(!1),[U,V]=(0,g.useState)(!1),[W,X]=(0,g.useState)(!1),[Y,Z]=(0,g.useState)(!0),[$,_]=(0,g.useState)(!0),[aa,ab]=(0,g.useState)(!1),[ac,ad]=(0,g.useState)(!1),[ae,af]=(0,g.useState)(!1),[ag,ah]=(0,g.useState)(!1),[ai,aj]=(0,g.useState)(!1),[ak,al]=(0,g.useState)(!1),[am,an]=(0,g.useState)(!1),[ao,ap]=(0,g.useState)(!1),[aq,ar]=(0,g.useState)("XX"),[as,at]=(0,g.useState)(""),[au,av]=(0,g.useState)(""),[aw,ax]=(0,g.useState)("000"),[ay,az]=(0,g.useState)(""),[aA,aB]=(0,g.useState)(""),[aC,aD]=(0,g.useState)("XX"),[aE,aF]=(0,g.useState)(""),[aG,aH]=(0,g.useState)(""),[aI,aJ]=(0,g.useState)("XX"),[aK,aL]=(0,g.useState)(""),[aM,aN]=(0,g.useState)(""),[aO,aP]=(0,g.useState)("XX"),[aQ,aR]=(0,g.useState)(""),[aS,aT]=(0,g.useState)(""),[aU,aV]=(0,g.useState)("XX"),[aW,aX]=(0,g.useState)(""),[aY,aZ]=(0,g.useState)(""),[a$,a_]=(0,g.useState)("XX"),[a0,a1]=(0,g.useState)(""),[a2,a3]=(0,g.useState)(""),[a4,a5]=(0,g.useState)("XX"),[a6,a7]=(0,g.useState)(""),[a8,a9]=(0,g.useState)(""),[ba,bb]=(0,g.useState)("XX"),[bc,bd]=(0,g.useState)(""),[be,bf]=(0,g.useState)(""),[bg,bh]=(0,g.useState)("XX"),[bi,bj]=(0,g.useState)(""),[bk,bl]=(0,g.useState)(""),[bm,bn]=(0,g.useState)("1"),[bo,bp]=(0,g.useState)("1"),[bq,br]=(0,g.useState)("1"),[bs,bt]=(0,g.useState)("1"),[bu,bv]=(0,g.useState)("1"),[bw,bx]=(0,g.useState)("1"),[by,bz]=(0,g.useState)("1"),[bA,bB]=(0,g.useState)("1"),[bC,bD]=(0,g.useState)("1"),[bE,bF]=(0,g.useState)("1"),[bG,bH]=(0,g.useState)("pilihdept"),[bI,bJ]=(0,g.useState)(""),[bK,bL]=(0,g.useState)(""),[bM,bN]=(0,g.useState)(", round(sum(rupiah)/1,0) as rupiah");return{role:a,telp:b,verified:c,loadingExcell:d,setloadingExcell:e,kodekppn:h,kodekanwil:i,settampilAI:j,showModal:k,setShowModal:l,showModalKedua:m,setShowModalKedua:n,showModalsql:o,setShowModalsql:p,showModalApbn:q,setShowModalApbn:r,showModalAkumulasi:s,setShowModalAkumulasi:t,showModalBulanan:u,setShowModalBulanan:v,showModalBlokir:w,setShowModalBlokir:x,showModalPN:y,setShowModalPN:z,showModalPN2:A,setShowModalPN2:B,showModalJnsblokir:C,setShowModalJnsblokir:D,showModalPDF:E,setShowModalPDF:F,showModalsimpan:G,setShowModalsimpan:H,jenlap:I,setJenlap:J,thang:K,setThang:L,pembulatan:M,setPembulatan:N,akumulatif:O,setAkumulatif:P,selectedFormat:Q,setSelectedFormat:R,export2:S,setExport2:T,loadingStatus:U,setLoadingStatus:V,showFormatDropdown:W,setShowFormatDropdown:X,jenisbansos:Y,setJenisbansos:Z,kddept:$,setKddept:_,unit:aa,setUnit:ab,kddekon:ac,setKddekon:ad,kdsatker:ae,setKdsatker:af,provinsi:ag,setProvinsi:ah,kabkota:ai,setKabkota:aj,kecamatan:ak,setKecamatan:al,desa:am,setDesa:an,statustransaksi:ao,setStatustransaksi:ap,bansostype:aq,setBansostype:ar,bansoskondisi:as,setBansoskondisi:at,katabansos:au,setKatabansos:av,dept:aw,setDept:ax,deptkondisi:ay,setDeptkondisi:az,katadept:aA,setKatadept:aB,kdunit:aC,setKdunit:aD,unitkondisi:aE,setUnitkondisi:aF,kataunit:aG,setKataunit:aH,dekon:aI,setDekon:aJ,dekonkondisi:aK,setDekonkondisi:aL,katadekon:aM,setKatadekon:aN,satker:aO,setSatker:aP,satkerkondisi:aQ,setSatkerkondisi:aR,katasatker:aS,setKatasatker:aT,prov:aU,setProv:aV,lokasikondisi:aW,setLokasikondisi:aX,katalokasi:aY,setKatalokasi:aZ,kabkotavalue:a$,setKabkotavalue:a_,kabkotakondisi:a0,setKabkotakondisi:a1,katakabkota:a2,setKatakabkota:a3,kecamatanvalue:a4,setKecamatanvalue:a5,kecamatankondisi:a6,setKecamatankondisi:a7,katakecamatan:a8,setKatakecamatan:a9,desavalue:ba,setDesavalue:bb,desakondisi:bc,setDesakondisi:bd,katadesa:be,setKatadesa:bf,statusvalue:bg,setStatusvalue:bh,statuskondisi:bi,setStatuskondisi:bj,katastatus:bk,setKatastatus:bl,bansosradio:bm,setBansosradio:bn,deptradio:bo,setDeptradio:bp,unitradio:bq,setUnitradio:br,dekonradio:bs,setDekonradio:bt,satkerradio:bu,setSatkerradio:bv,locradio:bw,setLocradio:bx,kabkotaradio:by,setKabkotaradio:bz,kecamatanradio:bA,setKecamatanradio:bB,desaradio:bC,setDesaradio:bD,statusradio:bE,setStatusradio:bF,opsidept:bG,setOpsidept:bH,sql:bI,setSql:bJ,from:bK,setFrom:bL,select:bM,setSelect:bN}}(),{statusLogin:b,token:c,axiosJWT:i}=(0,g.useContext)(f.A),{buildQuery:j}=function(a){let[b,c]=(0,g.useState)({}),d=(0,g.useMemo)(()=>new aZ,[]),{thang:e,jenlap:f,pembulatan:h,akumulatif:i,jenisbansos:j,kddept:k,unit:l,kddekon:m,kdsatker:n,provinsi:o,kabkota:p,kecamatan:q,desa:r,statustransaksi:s,bansostype:t,bansoskondisi:u,katabansos:v,bansosradio:w,dept:x,deptkondisi:y,katadept:z,deptradio:A,kdunit:B,unitkondisi:C,kataunit:D,unitradio:E,dekon:F,dekonkondisi:G,katadekon:H,dekonradio:I,satker:J,satkerkondisi:K,katasatker:L,satkerradio:M,prov:N,lokasikondisi:O,katalokasi:P,locradio:Q,kabkotavalue:R,kabkotakondisi:S,katakabkota:T,kabkotaradio:U,kecamatanvalue:V,kecamatankondisi:W,katakecamatan:X,kecamatanradio:Y,desavalue:Z,desakondisi:$,katadesa:_,desaradio:aa,statusvalue:ab,statuskondisi:ac,katastatus:ad,statusradio:ae,setFrom:af,setSelect:ag,setSql:ah}=a,ai=()=>{try{let b=d.buildQuery(a),c=d.generateSqlPreview(a);return af&&af(c.fromClause),ag&&ag(c.selectClause),ah&&ah(b),b}catch(a){return console.error("Error building query:",a),""}},aj=()=>d.getQueryPerformanceMetrics(a),ak=()=>d.generateSqlPreview(a),al=(a=ai)=>d.validateQuery(a),am=()=>d.filterBuilder.getFilterStats(a),an=b=>d.filterBuilder.isFilterEnabled(b,a),ao=b=>d.filterBuilder.buildFilter(b,a);return{buildQuery:ai,getBuildQuery:()=>ai,generateSqlPreview:ak,validateQuery:al,getQueryPerformanceMetrics:aj,getFilterStats:am,analyzeQueryComplexity:()=>{let a=aj(),b=am();return{complexity:{low:b.enabledFilters<=3&&a.validation.stats.joinCount<=3,medium:b.enabledFilters<=6&&a.validation.stats.joinCount<=6,high:b.enabledFilters>6||a.validation.stats.joinCount>6},metrics:a,stats:b,recommendations:a.recommendations}},isFilterEnabled:an,getAvailableFilters:()=>d.filterBuilder.getAvailableFilters(),buildFilter:ao,debugFilter:a=>{let b=ao(a),c=an(a);return console.log(`🔍 Debug Filter: ${a}`,{isEnabled:c,columns:b.columns,joinClause:b.joinClause,whereConditions:b.whereConditions,groupBy:b.groupBy}),{filterName:a,isEnabled:c,...b}},getCachedQuery:a=>b[a],setCachedQuery:(a,b)=>{c(c=>({...c,[a]:{query:b,timestamp:Date.now()}}))},clearQueryCache:()=>{c({})},generateSqlPreview:ak,generateOptimizedSql:()=>ai,parseAdvancedConditions:(a,b)=>{try{return d.filterBuilder.filters.department.parseKondisiConditions(a)}catch(a){return console.warn("Error parsing advanced conditions:",a),[]}},optimizeGroupBy:(a,b)=>[...new Set(b)].filter(b=>a.some(a=>a.includes(b)||b.includes("a."))),optimizeJoins:a=>d.filterBuilder.optimizeJoins(Array.isArray(a)?a:[a]),validateQuery:al,getQueryPerformanceMetrics:aj,getQueryStats:am}}(a),{role:k,telp:l,verified:m,loadingExcell:n,setloadingExcell:o,kodekppn:p,kodekanwil:q,settampilAI:r,showModal:s,setShowModal:t,showModalKedua:u,setShowModalKedua:v,showModalsql:w,setShowModalsql:x,showModalPDF:y,setShowModalPDF:z,showModalsimpan:A,setShowModalsimpan:B,jenlap:C,setJenlap:D,thang:E,setThang:F,pembulatan:G,setPembulatan:H,akumulatif:I,setAkumulatif:J,selectedFormat:K,setSelectedFormat:L,export2:M,setExport2:N,loadingStatus:O,setLoadingStatus:R,showFormatDropdown:S,setShowFormatDropdown:T,cutoff:U,setCutoff:V,jenisbansos:W,setJenisbansos:X,kddept:Y,setKddept:Z,unit:$,setUnit:_,kddekon:aa,setKddekon:ab,kdsatker:ac,setKdsatker:ad,provinsi:ae,setProvinsi:ag,kabkota:ai,setKabkota:aj,kecamatan:ak,setKecamatan:al,desa:am,setDesa:an,statustransaksi:ao,setStatustransaksi:ap,bansostype:aq,setBansostype:ar,bansoskondisi:as,setBansoskondisi:at,katabansos:au,setKatabansos:av,dept:ax,setDept:ay,deptkondisi:az,setDeptkondisi:aA,katadept:aB,setKatadept:aD,kdunit:aE,setKdunit:aG,unitkondisi:aH,setUnitkondisi:aI,kataunit:aJ,setKataunit:aK,dekon:aM,setDekon:aN,dekonkondisi:aO,setDekonkondisi:aP,katadekon:aQ,setKatadekon:aR,satker:aS,setSatker:aT,satkerkondisi:aU,setSatkerkondisi:aV,katasatker:aW,setKatasatker:aX,prov:aY,setProv:a$,lokasikondisi:a_,setLokasikondisi:a0,katalokasi:a1,setKatalokasi:a2,kabkotavalue:a3,setKabkotavalue:a4,kabkotakondisi:a5,setKabkotakondisi:a6,katakabkota:a7,setKatakabkota:a8,kecamatanvalue:a9,setKecamatanvalue:ba,kecamatankondisi:bb,setKecamatankondisi:bc,katakecamatan:bd,setKatakecamatan:be,desavalue:bf,setDesavalue:bg,desakondisi:bh,setDesakondisi:bi,katadesa:bj,setKatadesa:bk,statusvalue:bl,setStatusvalue:bm,statuskondisi:bn,setStatuskondisi:bo,katastatus:bp,setKatastatus:bq,bansosradio:br,setBansosradio:bs,deptradio:bt,setDeptradio:bu,unitradio:bv,setUnitradio:bw,dekonradio:bx,setDekonradio:by,satkerradio:bz,setSatkerradio:bA,locradio:bB,setLocradio:bC,kabkotaradio:bD,setKabkotaradio:bE,kecamatanradio:bF,setKecamatanradio:bG,desaradio:bH,setDesaradio:bI,statusradio:bJ,setStatusradio:bK,sql:bL,setSql:bM,from:bN,setFrom:bO,select:bP,setSelect:bQ}=a,bR=()=>{let a=j();return"string"==typeof a&&a.length>0?console.log("\uD83D\uDD04 Query Generated:",a.substring(0,600)):console.log("\uD83D\uDD04 Query Generated: (empty or invalid)"),a},bS=async()=>{let b=bR();a.setSql(b),t(!0)};h().useEffect(()=>{j()},[E,G]),h().useRef(!1);let[bT,bU]=h().useState(!1);async function bV(){console.log("fetchExportData called");let a=bR();if(!a||"string"!=typeof a||""===a.trim())return(0,e.qs)("Query tidak valid, silakan cek filter dan parameter."),console.error("Export aborted: SQL query is empty or invalid.",{sql:a}),[];if(!b)return console.log("Not logged in, cannot export data."),[];try{let b=await i.post("http://localhost:88/next/inquiry",{sql:a,page:1},{headers:{Authorization:`Bearer ${c}`}});if(console.log("[Export Debug] Backend response:",b.data),b.data&&Array.isArray(b.data.data))return b.data.data;return[]}catch(a){return console.error("Export API error:",a),a&&a.response&&console.error("[Export Debug] Backend error response:",a.response.data),[]}}let bW=async()=>{o(!0);try{let a=await bV();if(!a||!a.length){(0,e.qs)("Tidak ada data untuk diexport"),o(!1);return}await af(a,"inquiry_data.xlsx"),(0,e.qs)("Data berhasil diexport ke Excel")}catch(a){(0,e.qs)("Gagal export Excel")}o(!1)},bX=async()=>{o(!0);try{let a=await bV();if(!a||!a.length){(0,e.qs)("Tidak ada data untuk diexport"),o(!1);return}!function(a,b="data.csv"){if(!a||!a.length)return;let c=(a,b)=>null==b?"":b,d=Object.keys(a[0]),e=new Blob([[d.join(","),...a.map(a=>d.map(b=>JSON.stringify(a[b],c)).join(","))].join("\r\n")],{type:"text/csv"}),f=URL.createObjectURL(e),g=document.createElement("a");g.setAttribute("href",f),g.setAttribute("download",b),g.style.visibility="hidden",document.body.appendChild(g),g.click(),document.body.removeChild(g),URL.revokeObjectURL(f)}(a,"inquiry_data.csv"),(0,e.qs)("Data berhasil diexport ke CSV")}catch(a){(0,e.qs)("Gagal export CSV")}o(!1)};return(0,d.jsxs)("div",{className:"w-full",children:[(0,d.jsxs)("div",{className:"xl:px-8 p-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Inquiry Data Bansos"}),(0,d.jsx)(Q,{inquiryState:{jenlap:C,setJenlap:D,pembulatan:G,setPembulatan:H,akumulatif:I,setAkumulatif:J,thang:E,setThang:F}}),(0,d.jsx)(P,{inquiryState:{jenlap:C,jenisbansos:W,setJenisbansos:X,kddept:Y,setKddept:Z,unit:$,setUnit:_,kddekon:aa,setKddekon:ab,kdsatker:ac,setKdsatker:ad,provinsi:ae,setProvinsi:ag,kabkota:ai,setKabkota:aj,kecamatan:ak,setKecamatan:al,desa:am,setDesa:an,statustransaksi:ao,setStatustransaksi:ap,bansostype:aq,setBansostype:ar,bansoskondisi:as,setBansoskondisi:at,katabansos:au,setKatabansos:av,bansosradio:br,setBansosradio:bs,dept:ax,setDept:ay,deptkondisi:az,setDeptkondisi:aA,katadept:aB,setKatadept:aD,deptradio:bt,setDeptradio:bu,kdunit:aE,setKdunit:aG,unitkondisi:aH,setUnitkondisi:aI,kataunit:aJ,setKataunit:aK,unitradio:bv,setUnitradio:bw,dekon:aM,setDekon:aN,dekonkondisi:aO,setDekonkondisi:aP,katadekon:aQ,setKatadekon:aR,dekonradio:bx,setDekonradio:by,satker:aS,setSatker:aT,satkerkondisi:aU,setSatkerkondisi:aV,katasatker:aW,setKatasatker:aX,satkerradio:bz,setSatkerradio:bA,prov:aY,setProv:a$,lokasikondisi:a_,setLokasikondisi:a0,katalokasi:a1,setKatalokasi:a2,locradio:bB,setLocradio:bC,kabkotavalue:a3,setKabkotavalue:a4,kabkotakondisi:a5,setKabkotakondisi:a6,katakabkota:a7,setKatakabkota:a8,kabkotaradio:bD,setKabkotaradio:bE,kecamatanvalue:a9,setKecamatanvalue:ba,kecamatankondisi:bb,setKecamatankondisi:bc,katakecamatan:bd,setKatakecamatan:be,kecamatanradio:bF,setKecamatanradio:bG,desavalue:bf,setDesavalue:bg,desakondisi:bh,setDesakondisi:bi,katadesa:bj,setKatadesa:bk,desaradio:bH,setDesaradio:bI,statusvalue:bl,setStatusvalue:bm,statuskondisi:bn,setStatuskondisi:bo,katastatus:bp,setKatastatus:bq,statusradio:bJ,setStatusradio:bK}}),(0,d.jsx)("div",{className:"my-3 sm:px-16",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row md:flex-wrap lg:flex-nowrap gap-2 border-2 dark:border-zinc-600 rounded-xl shadow-sm py-2 px-4 font-mono tracking-wide bg-zinc-100 dark:bg-black",children:[(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-blue-600 ml-4",children:"Tahun Anggaran:"}),(0,d.jsx)("span",{className:"ml-2",children:E})]}),(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-green-600 ml-4",children:"Jenis Laporan:"}),(0,d.jsx)("span",{className:"ml-2",children:"1"===C?"Realisasi Bansos":""})]}),(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-blue-600 ml-4",children:"Akumulatif:"}),(0,d.jsx)("span",{className:"ml-2",children:"1"===I?"Akumulatif":"Non-Akumulatif"})]}),(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-purple-600 ml-4",children:"Pembulatan:"}),(0,d.jsx)("span",{className:"ml-2",children:"1"===G?"Rupiah":"1000"===G?"Ribuan":"1000000"===G?"Jutaan":"1000000000"===G?"Miliaran":"Triliunan"})]}),(0,d.jsxs)("div",{className:"text-xs",children:[(0,d.jsx)("span",{className:"font-semibold text-orange-600 ml-4",children:"Filter Aktif:"}),(0,d.jsxs)("span",{className:"ml-2",children:[[W,Y,$,aa,ac,ae,ai,ak,am,ao].filter(Boolean).length," ","dari"," ",10]})]})]})}),(0,d.jsx)(aL,{onExecuteQuery:bS,onExportExcel:bW,onExportCSV:bX,onExportPDF:()=>{z(!0)},onReset:()=>{D("1"),F(new Date().getFullYear().toString()),J("0"),X(!0),Z(!0),_(!1),ab(!1),ad(!1),ag(!1),aj(!1),al(!1),an(!1),ap(!1),ar("XX"),at(""),av(""),ay("000"),aA(""),aD(""),aG("XX"),aI(""),aK(""),aN("XX"),aP(""),aR(""),aT("XX"),aV(""),aX(""),a$("XX"),a0(""),a2(""),a4("XX"),a6(""),a8(""),ba("XX"),bc(""),be(""),bg("XX"),bi(""),bk(""),bm("XX"),bo(""),bq(""),bs("1"),bu("1"),bw("1"),by("1"),bA("1"),bC("1"),bE("1"),bG("1"),bI("1"),bK("1"),H("1"),bM(""),bO(""),bQ(", round(sum(rupiah)/1,0) as rupiah")},isLoading:n,onSaveQuery:()=>bU(!0),onShowSQL:()=>{console.log("\uD83D\uDD0D handlegetQuerySQL - Debug inquiry state:",{jenlap:a.jenlap,cutoff:a.cutoff,cutoffFilter:a.cutoffFilter,type:typeof a.jenlap,timestamp:new Date().toISOString()});let b=bR();a.setSql(b),x(!0)}})]}),w&&(0,d.jsx)(aF,{isOpen:w,onClose:()=>{x(!1),window.scrollTo({top:0,behavior:"smooth"})},query:bL}),s&&(0,d.jsx)(aw,{isOpen:s,onClose:()=>{t(!1),B(!1),window.scrollTo({top:0,behavior:"smooth"})},sql:bL,from:bN,thang:E,pembulatan:G}),A&&(0,d.jsx)(aC,{isOpen:A,onClose:()=>{B(!1),window.scrollTo({top:0,behavior:"smooth"})},sql:bL}),y&&(0,d.jsx)(ah,{showModalPDF:y,setShowModalPDF:z,selectedFormat:K,setSelectedFormat:L,fetchExportData:bV,filename:"inquiry_data",loading:n}),bT&&(0,d.jsx)(aC,{isOpen:bT,onClose:()=>bU(!1),query:bL,thang:E,queryType:"INQUIRY"})]})},a_=()=>(0,d.jsx)(a$,{})},88367:(a,b,c)=>{Promise.resolve().then(c.bind(c,52192))},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},97992:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,3598,9161,6159,6942,9697,901],()=>b(b.s=49530));module.exports=c})();