#!/usr/bin/env node

/**
 * Redis Connection Test Script for Sintesa Backend
 * 
 * This script tests Redis connectivity and basic operations
 * Run with: node test-redis-connection.js
 */

import Redis from 'ioredis';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: process.env.REDIS_DB || 0,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  connectTimeout: 5000,
  commandTimeout: 3000
};

console.log('🔍 Testing Redis Connection...');
console.log('📋 Configuration:');
console.log(`   Host: ${redisConfig.host}`);
console.log(`   Port: ${redisConfig.port}`);
console.log(`   Database: ${redisConfig.db}`);
console.log(`   Password: ${redisConfig.password ? '***' : 'None'}`);
console.log('');

async function testRedisConnection() {
  let redis = null;
  
  try {
    // Create Redis instance
    redis = new Redis(redisConfig);
    
    // Test 1: Basic connectivity
    console.log('🔌 Test 1: Basic Connectivity');
    const pong = await redis.ping();
    console.log(`   ✅ PING response: ${pong}`);
    
    // Test 2: Set and Get operations
    console.log('\n📝 Test 2: Set/Get Operations');
    const testKey = 'sintesa:test:connection';
    const testValue = {
      message: 'Hello from Sintesa Backend!',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    };
    
    await redis.set(testKey, JSON.stringify(testValue), 'EX', 60); // Expire in 60 seconds
    console.log(`   ✅ SET: ${testKey}`);
    
    const retrievedValue = await redis.get(testKey);
    const parsedValue = JSON.parse(retrievedValue);
    console.log(`   ✅ GET: ${parsedValue.message}`);
    
    // Test 3: Hash operations
    console.log('\n🗂️  Test 3: Hash Operations');
    const hashKey = 'sintesa:test:hash';
    await redis.hset(hashKey, 'user', 'test_user');
    await redis.hset(hashKey, 'role', 'admin');
    await redis.hset(hashKey, 'login_time', new Date().toISOString());
    
    const hashData = await redis.hgetall(hashKey);
    console.log(`   ✅ HASH data:`, hashData);
    
    // Test 4: List operations
    console.log('\n📋 Test 4: List Operations');
    const listKey = 'sintesa:test:list';
    await redis.lpush(listKey, 'item1', 'item2', 'item3');
    const listLength = await redis.llen(listKey);
    console.log(`   ✅ LIST length: ${listLength}`);
    
    // Test 5: Expiration
    console.log('\n⏰ Test 5: TTL Operations');
    await redis.expire(listKey, 30); // Expire in 30 seconds
    const ttl = await redis.ttl(listKey);
    console.log(`   ✅ TTL: ${ttl} seconds`);
    
    // Test 6: Session simulation
    console.log('\n👤 Test 6: Session Simulation');
    const sessionId = 'test_session_' + Date.now();
    const sessionData = {
      userId: 'user123',
      username: 'test_user',
      role: 'admin',
      loginTime: new Date().toISOString(),
      permissions: ['read', 'write', 'admin']
    };
    
    await redis.setex(`session:${sessionId}`, 3600, JSON.stringify(sessionData)); // 1 hour
    const session = await redis.get(`session:${sessionId}`);
    console.log(`   ✅ Session created: ${sessionId}`);
    console.log(`   ✅ Session data:`, JSON.parse(session));
    
    // Cleanup test data
    console.log('\n🧹 Cleaning up test data...');
    await redis.del(testKey, hashKey, listKey, `session:${sessionId}`);
    console.log('   ✅ Test data cleaned up');
    
    // Test 7: Performance test
    console.log('\n⚡ Test 7: Performance Test');
    const startTime = Date.now();
    const promises = [];
    
    for (let i = 0; i < 100; i++) {
      promises.push(redis.set(`perf:test:${i}`, `value${i}`, 'EX', 10));
    }
    
    await Promise.all(promises);
    const endTime = Date.now();
    console.log(`   ✅ 100 SET operations completed in ${endTime - startTime}ms`);
    
    // Cleanup performance test data
    const keys = await redis.keys('perf:test:*');
    if (keys.length > 0) {
      await redis.del(...keys);
    }
    
    console.log('\n🎉 All Redis tests passed successfully!');
    console.log('\n📊 Redis Status:');
    const info = await redis.info('server');
    const lines = info.split('\r\n');
    const version = lines.find(line => line.startsWith('redis_version:'));
    const uptime = lines.find(line => line.startsWith('uptime_in_seconds:'));
    
    if (version) console.log(`   ${version}`);
    if (uptime) {
      const seconds = parseInt(uptime.split(':')[1]);
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      console.log(`   Uptime: ${hours}h ${minutes}m ${seconds % 60}s`);
    }
    
  } catch (error) {
    console.error('\n❌ Redis test failed:');
    console.error(`   Error: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n💡 Troubleshooting tips:');
      console.error('   1. Make sure Redis server is running');
      console.error('   2. Check if Redis is listening on the correct port');
      console.error('   3. Verify firewall settings');
      console.error('   4. Try: redis-server (to start Redis manually)');
    } else if (error.code === 'ENOTFOUND') {
      console.error('\n💡 Troubleshooting tips:');
      console.error('   1. Check REDIS_HOST in your .env file');
      console.error('   2. Verify DNS resolution for the Redis host');
    } else if (error.message.includes('WRONGPASS')) {
      console.error('\n💡 Troubleshooting tips:');
      console.error('   1. Check REDIS_PASSWORD in your .env file');
      console.error('   2. Verify Redis authentication settings');
    }
    
    process.exit(1);
  } finally {
    if (redis) {
      await redis.quit();
      console.log('\n🔌 Redis connection closed');
    }
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n⚠️  Test interrupted by user');
  process.exit(0);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('\n❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the test
testRedisConnection();