# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.development.local
.env.test.local
.env.production.local
.env.local
.env.*.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp
.cache

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IntelliJ based IDEs
.idea

# Finder (MacOS)
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~

# Application specific

# Uploads directory
uploads/
!uploads/.gitkeep

# Logs directory
logs/
!logs/.gitkeep

# Temporary files
temp/
tmp/
!temp/.gitkeep
!tmp/.gitkeep

# Database files
*.db
*.sqlite
*.sqlite3

# SSL certificates
*.pem
*.key
*.crt
*.cert
ssl/
certs/

# Configuration files with sensitive data
config/local.js
config/production.js
config/secrets.js

# PM2 files
.pm2/

# Docker volumes
data/
mysql_data/
redis_data/

# Backup files
*.bak
*.backup
*.old
*.orig

# Editor files
*.swp
*.swo
*~
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# OS generated files
.DS_Store?
._*
.Spotlight-V100
.Trashes

# Test coverage
coverage/
.nyc_output/

# Build artifacts
build/
dist/

# Package manager lock files (choose one)
# npm
# package-lock.json
# yarn
# yarn.lock
# pnpm
# pnpm-lock.yaml

# Local development
.local
.dev

# Monitoring and profiling
*.heapsnapshot
*.cpuprofile

# Redis dump
dump.rdb

# MySQL binary logs
*.bin
*.index

# Application logs
app.log
error.log
access.log
combined.log

# Session files
sessions/

# Cache files
cache/
.cache/

# Generated documentation
docs/generated/

# Compiled assets
public/assets/
static/assets/

# User uploaded content
public/uploads/
static/uploads/

# Development database
dev.db
development.db

# Test database
test.db
testing.db

# Seed data (if contains sensitive information)
# database/seeds/production/

# Migration files with sensitive data
# database/migrations/*_sensitive_*

# Webpack bundles
bundle.js
bundle.js.map

# Rollup bundles
rollup.config.js

# Parcel bundles
.parcel-cache/

# ESLint cache
.eslintcache

# Prettier cache
.prettiercache

# Stylelint cache
.stylelintcache

# Jest cache
.jest/

# Cypress
cypress/videos/
cypress/screenshots/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Storybook build outputs
storybook-static

# Temporary folders
tmp/
temp/

# Runtime configuration
runtime.json

# Local environment overrides
.env.override
.env.local.override

# IDE specific files
*.sublime-project
*.sublime-workspace

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Visual Studio
*.suo
*.user
*.userosscache
*.sln.docstates

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Tags
TAGS
tags
.tags
.tags1

# Global gitignore
*~
.DS_Store