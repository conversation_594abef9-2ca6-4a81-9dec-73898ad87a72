"use strict";exports.id=7426,exports.ids=[7426],exports.modules={20172:(a,b,c)=>{c.d(b,{CQ:()=>e,HQ:()=>f,N:()=>j,jA:()=>l,vb:()=>h});var d=c(68028);function e(a){return a.max-a.min}function f(a,b,c){return Math.abs(a-b)<=c}function g(a,b,c,f=.5){a.origin=f,a.originPoint=(0,d.k)(b.min,b.max,a.origin),a.scale=e(c)/e(b),a.translate=(0,d.k)(c.min,c.max,a.origin)-a.originPoint,(a.scale>=.9999&&a.scale<=1.0001||isNaN(a.scale))&&(a.scale=1),(a.translate>=-.01&&a.translate<=.01||isNaN(a.translate))&&(a.translate=0)}function h(a,b,c,d){g(a.x,b.x,c.x,d?d.originX:void 0),g(a.y,b.y,c.y,d?d.originY:void 0)}function i(a,b,c){a.min=c.min+b.min,a.max=a.min+e(b)}function j(a,b,c){i(a.x,b.x,c.x),i(a.y,b.y,c.y)}function k(a,b,c){a.min=b.min-c.min,a.max=a.min+e(b)}function l(a,b,c){k(a.x,b.x,c.x),k(a.y,b.y,c.y)}},25103:(a,b,c)=>{c.d(b,{w:()=>d});let d={hasAnimatedSinceResize:!0,hasEverUpdated:!1}},27642:(a,b,c)=>{c.d(b,{X:()=>d});function d(a){return[a("x"),a("y")]}},31208:(a,b,c)=>{c.d(b,{Z:()=>f});var d=c(76242),e=c(86652);let f={layout:{ProjectionNode:d.P,MeasureLayout:e.$}}},76242:(a,b,c)=>{c.d(b,{P:()=>aH});var d=c(23671),e=c(82082),f=c(74479);function g(a){return(0,f.G)(a)&&"ownerSVGElement"in a}var h=c(62923),i=c(24325),j=c(59039),k=c(96184),l=c(24342),m=c(68028),n=c(14296),o=c(97758),p=c(83361),q=c(5927),r=c(96259),s=c(80722),t=c(87556);let u=(a,b)=>a.depth-b.depth;class v{constructor(){this.children=[],this.isDirty=!1}add(a){(0,t.Kq)(this.children,a),this.isDirty=!0}remove(a){(0,t.Ai)(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(u),this.isDirty=!1,this.children.forEach(a)}}var w=c(61866),x=c(32874),y=c(52716),z=c(64068);let A=["TopLeft","TopRight","BottomLeft","BottomRight"],B=A.length,C=a=>"string"==typeof a?parseFloat(a):a,D=a=>"number"==typeof a||x.px.test(a);function E(a,b){return void 0!==a[b]?a[b]:a.borderRadius}let F=H(0,.5,y.yT),G=H(.5,.95,p.l);function H(a,b,c){return d=>d<a?0:d>b?1:c((0,z.q)(a,b,d))}function I(a,b){a.min=b.min,a.max=b.max}function J(a,b){I(a.x,b.x),I(a.y,b.y)}function K(a,b){a.translate=b.translate,a.scale=b.scale,a.originPoint=b.originPoint,a.origin=b.origin}var L=c(42485),M=c(20172);function N(a,b,c,d,e){return a-=b,a=(0,L.hq)(a,1/c,d),void 0!==e&&(a=(0,L.hq)(a,1/e,d)),a}function O(a,b,[c,d,e],f,g){!function(a,b=0,c=1,d=.5,e,f=a,g=a){if(x.KN.test(b)&&(b=parseFloat(b),b=(0,m.k)(g.min,g.max,b/100)-g.min),"number"!=typeof b)return;let h=(0,m.k)(f.min,f.max,d);a===f&&(h-=b),a.min=N(a.min,b,c,h,e),a.max=N(a.max,b,c,h,e)}(a,b[c],b[d],b[e],b.scale,f,g)}let P=["x","scaleX","originX"],Q=["y","scaleY","originY"];function R(a,b,c,d){O(a.x,b,P,c?c.x:void 0,d?d.x:void 0),O(a.y,b,Q,c?c.y:void 0,d?d.y:void 0)}var S=c(54538);function T(a){return 0===a.translate&&1===a.scale}function U(a){return T(a.x)&&T(a.y)}function V(a,b){return a.min===b.min&&a.max===b.max}function W(a,b){return Math.round(a.min)===Math.round(b.min)&&Math.round(a.max)===Math.round(b.max)}function X(a,b){return W(a.x,b.x)&&W(a.y,b.y)}function Y(a){return(0,M.CQ)(a.x)/(0,M.CQ)(a.y)}function Z(a,b){return a.translate===b.translate&&a.scale===b.scale&&a.originPoint===b.originPoint}class ${constructor(){this.members=[]}add(a){(0,t.Kq)(this.members,a),a.scheduleRender()}remove(a){if((0,t.Ai)(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){let a=this.members[this.members.length-1];a&&this.promote(a)}}relegate(a){let b,c=this.members.findIndex(b=>a===b);if(0===c)return!1;for(let a=c;a>=0;a--){let c=this.members[a];if(!1!==c.isPresent){b=c;break}}return!!b&&(this.promote(b),!0)}promote(a,b){let c=this.lead;if(a!==c&&(this.prevLead=c,this.lead=a,a.show(),c)){c.instance&&c.scheduleRender(),a.scheduleRender(),a.resumeFrom=c,b&&(a.resumeFrom.preserveOpacity=!0),c.snapshot&&(a.snapshot=c.snapshot,a.snapshot.latestValues=c.animationValues||c.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);let{crossfade:d}=a.options;!1===d&&c.hide()}}exitAnimationComplete(){this.members.forEach(a=>{let{options:b,resumingFrom:c}=a;b.onExitComplete&&b.onExitComplete(),c&&c.options.onExitComplete&&c.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var _=c(96633),aa=c(27642),ab=c(67606),ac=c(25103);let ad={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},ae=["","X","Y","Z"],af=0;function ag(a,b,c,d){let{latestValues:e}=b;e[a]&&(c[a]=e[a],b.setStaticValue(a,0),d&&(d[a]=0))}function ah({attachResizeListener:a,defaultParent:b,measureScroll:c,checkIsScrollRoot:f,resetTransform:p}){return class{constructor(a={},c=b?.()){this.id=af++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,e.Q.value&&(ad.nodes=ad.calculatedTargetDeltas=ad.calculatedProjections=0),this.nodes.forEach(ak),this.nodes.forEach(ar),this.nodes.forEach(as),this.nodes.forEach(al),e.Q.addProjectionMetrics&&e.Q.addProjectionMetrics(ad)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=c?c.root||c:this,this.path=c?[...c.path,c]:[],this.parent=c,this.depth=c?c.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new v)}addEventListener(a,b){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new n.v),this.eventHandlers.get(a).add(b)}notifyListeners(a,...b){let c=this.eventHandlers.get(a);c&&c.notify(...b)}hasListeners(a){return this.eventHandlers.has(a)}mount(b){if(this.instance)return;this.isSVG=g(b)&&!(g(b)&&"svg"===b.tagName),this.instance=b;let{layoutId:c,layout:e,visualElement:f}=this.options;if(f&&!f.current&&f.mount(b),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(e||c)&&(this.isLayoutDirty=!0),a){let c,e=0,f=()=>this.root.updateBlockedByResize=!1;d.Gt.read(()=>{e=window.innerWidth}),a(b,()=>{let a=window.innerWidth;a!==e&&(e=a,this.root.updateBlockedByResize=!0,c&&c(),c=function(a,b){let c=i.k.now(),e=({timestamp:b})=>{let f=b-c;f>=250&&((0,d.WG)(e),a(f-250))};return d.Gt.setup(e,!0),()=>(0,d.WG)(e)}(f,250),ac.w.hasAnimatedSinceResize&&(ac.w.hasAnimatedSinceResize=!1,this.nodes.forEach(aq)))})}c&&this.root.registerSharedNode(c,this),!1!==this.options.animate&&f&&(c||e)&&this.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b,hasRelativeLayoutChanged:c,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let e=this.options.transition||f.getDefaultTransition()||ay,{onLayoutAnimationStart:g,onLayoutAnimationComplete:i}=f.getProps(),j=!this.targetLayout||!X(this.targetLayout,d),k=!b&&c;if(this.options.layoutRoot||this.resumeFrom||k||b&&(j||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let b={...(0,h.r)(e,"layout"),onPlay:g,onComplete:i};(f.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b),this.setAnimationOrigin(a,k)}else b||aq(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,d.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(at),this.animationId++)}getTransformTemplate(){let{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function a(b){if(b.hasCheckedOptimisedAppear=!0,b.root===b)return;let{visualElement:c}=b.options;if(!c)return;let e=(0,s.P)(c);if(window.MotionHasOptimisedAnimation(e,"transform")){let{layout:a,layoutId:c}=b.options;window.MotionCancelOptimisedAnimation(e,"transform",d.Gt,!(a||c))}let{parent:f}=b;f&&!f.hasCheckedOptimisedAppear&&a(f)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){let b=this.path[a];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}let{layoutId:b,layout:c}=this.options;if(void 0===b&&!c)return;let e=this.getTransformTemplate();this.prevTransformTemplateValue=e?e(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(an);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(ao);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(ap),this.nodes.forEach(ai),this.nodes.forEach(aj)):this.nodes.forEach(ao),this.clearAllSnapshots();let a=i.k.now();d.uv.delta=(0,o.q)(0,1e3/60,a-d.uv.timestamp),d.uv.timestamp=a,d.uv.isProcessing=!0,d.PP.update.process(d.uv),d.PP.preRender.process(d.uv),d.PP.render.process(d.uv),d.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,j.k.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(am),this.sharedNodes.forEach(au)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,d.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){d.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||(0,M.CQ)(this.snapshot.measuredBox.x)||(0,M.CQ)(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();let a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,S.ge)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:b}=this.options;b&&b.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let b=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(b=!1),b&&this.instance){let b=f(this.instance);this.scroll={animationId:this.root.animationId,phase:a,isRoot:b,offset:c(this.instance),wasRoot:this.scroll?this.scroll.isRoot:b}}}resetTransform(){if(!p)return;let a=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,b=this.projectionDelta&&!U(this.projectionDelta),c=this.getTransformTemplate(),d=c?c(this.latestValues,""):void 0,e=d!==this.prevTransformTemplateValue;a&&this.instance&&(b||(0,ab.HD)(this.latestValues)||e)&&(p(this.instance,d),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){var b;let c=this.measurePageBox(),d=this.removeElementScroll(c);return a&&(d=this.removeTransform(d)),aB((b=d).x),aB(b.y),{animationId:this.root.animationId,measuredBox:c,layoutBox:d,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:a}=this.options;if(!a)return(0,S.ge)();let b=a.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(aD))){let{scroll:a}=this.root;a&&((0,L.Ql)(b.x,a.offset.x),(0,L.Ql)(b.y,a.offset.y))}return b}removeElementScroll(a){let b=(0,S.ge)();if(J(b,a),this.scroll?.wasRoot)return b;for(let c=0;c<this.path.length;c++){let d=this.path[c],{scroll:e,options:f}=d;d!==this.root&&e&&f.layoutScroll&&(e.wasRoot&&J(b,a),(0,L.Ql)(b.x,e.offset.x),(0,L.Ql)(b.y,e.offset.y))}return b}applyTransform(a,b=!1){let c=(0,S.ge)();J(c,a);for(let a=0;a<this.path.length;a++){let d=this.path[a];!b&&d.options.layoutScroll&&d.scroll&&d!==d.root&&(0,L.Ww)(c,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),(0,ab.HD)(d.latestValues)&&(0,L.Ww)(c,d.latestValues)}return(0,ab.HD)(this.latestValues)&&(0,L.Ww)(c,this.latestValues),c}removeTransform(a){let b=(0,S.ge)();J(b,a);for(let a=0;a<this.path.length;a++){let c=this.path[a];if(!c.instance||!(0,ab.HD)(c.latestValues))continue;(0,ab.vk)(c.latestValues)&&c.updateSnapshot();let d=(0,S.ge)();J(d,c.measurePageBox()),R(b,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,d)}return(0,ab.HD)(this.latestValues)&&R(b,this.latestValues),b}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:void 0===a.crossfade||a.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==d.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){let b=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=b.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=b.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=b.isSharedProjectionDirty);let c=!!this.resumingFrom||this!==b;if(!(a||c&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:f,layoutId:g}=this.options;if(this.layout&&(f||g)){if(this.resolvedRelativeTargetAt=d.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let a=this.getClosestProjectingParent();a&&a.layout&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,S.ge)(),this.relativeTargetOrigin=(0,S.ge)(),(0,M.jA)(this.relativeTargetOrigin,this.layout.layoutBox,a.layout.layoutBox),J(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,S.ge)(),this.targetWithTransforms=(0,S.ge)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),(0,M.N)(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):J(this.target,this.layout.layoutBox),(0,L.o4)(this.target,this.targetDelta)):J(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let a=this.getClosestProjectingParent();a&&!!a.resumingFrom==!!this.resumingFrom&&!a.options.layoutScroll&&a.target&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,S.ge)(),this.relativeTargetOrigin=(0,S.ge)(),(0,M.jA)(this.relativeTargetOrigin,this.target,a.target),J(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}e.Q.value&&ad.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||(0,ab.vk)(this.parent.latestValues)||(0,ab.vF)(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let a=this.getLead(),b=!!this.resumingFrom||this!==a,c=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(c=!1),b&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===d.uv.timestamp&&(c=!1),c)return;let{layout:f,layoutId:g}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(f||g))return;J(this.layoutCorrected,this.layout.layoutBox);let h=this.treeScale.x,i=this.treeScale.y;(0,L.OU)(this.layoutCorrected,this.treeScale,this.path,b),a.layout&&!a.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=(0,S.ge)());let{target:j}=a;if(!j){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(K(this.prevProjectionDelta.x,this.projectionDelta.x),K(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),(0,M.vb)(this.projectionDelta,this.layoutCorrected,j,this.latestValues),this.treeScale.x===h&&this.treeScale.y===i&&Z(this.projectionDelta.x,this.prevProjectionDelta.x)&&Z(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",j)),e.Q.value&&ad.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){if(this.options.visualElement?.scheduleRender(),a){let a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=(0,S.xU)(),this.projectionDelta=(0,S.xU)(),this.projectionDeltaWithTransform=(0,S.xU)()}setAnimationOrigin(a,b=!1){let c,d=this.snapshot,e=d?d.latestValues:{},f={...this.latestValues},g=(0,S.xU)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!b;let h=(0,S.ge)(),i=(d?d.source:void 0)!==(this.layout?this.layout.source:void 0),j=this.getStack(),k=!j||j.members.length<=1,l=!!(i&&!k&&!0===this.options.crossfade&&!this.path.some(ax));this.animationProgress=0,this.mixTargetDelta=b=>{let d=b/1e3;if(av(g.x,a.x,d),av(g.y,a.y,d),this.setTargetDelta(g),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var j,n,o,p,q,r;(0,M.jA)(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),o=this.relativeTarget,p=this.relativeTargetOrigin,q=h,r=d,aw(o.x,p.x,q.x,r),aw(o.y,p.y,q.y,r),c&&(j=this.relativeTarget,n=c,V(j.x,n.x)&&V(j.y,n.y))&&(this.isProjectionDirty=!1),c||(c=(0,S.ge)()),J(c,this.relativeTarget)}i&&(this.animationValues=f,function(a,b,c,d,e,f){e?(a.opacity=(0,m.k)(0,c.opacity??1,F(d)),a.opacityExit=(0,m.k)(b.opacity??1,0,G(d))):f&&(a.opacity=(0,m.k)(b.opacity??1,c.opacity??1,d));for(let e=0;e<B;e++){let f=`border${A[e]}Radius`,g=E(b,f),h=E(c,f);(void 0!==g||void 0!==h)&&(g||(g=0),h||(h=0),0===g||0===h||D(g)===D(h)?(a[f]=Math.max((0,m.k)(C(g),C(h),d),0),(x.KN.test(h)||x.KN.test(g))&&(a[f]+="%")):a[f]=h)}(b.rotate||c.rotate)&&(a.rotate=(0,m.k)(b.rotate||0,c.rotate||0,d))}(f,e,this.latestValues,d,l,k)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=d},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,d.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=d.Gt.update(()=>{ac.w.hasAnimatedSinceResize=!0,k.q.layout++,this.motionValue||(this.motionValue=(0,l.OQ)(0)),this.currentAnimation=function(a,b,c){let d=(0,q.S)(a)?a:(0,l.OQ)(a);return d.start((0,r.f)("",d,b,c)),d.animation}(this.motionValue,[0,1e3],{...a,velocity:0,isSync:!0,onUpdate:b=>{this.mixTargetDelta(b),a.onUpdate&&a.onUpdate(b)},onStop:()=>{k.q.layout--},onComplete:()=>{k.q.layout--,a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let a=this.getLead(),{targetWithTransforms:b,target:c,layout:d,latestValues:e}=a;if(b&&c&&d){if(this!==a&&this.layout&&d&&aC(this.options.animationType,this.layout.layoutBox,d.layoutBox)){c=this.target||(0,S.ge)();let b=(0,M.CQ)(this.layout.layoutBox.x);c.x.min=a.target.x.min,c.x.max=c.x.min+b;let d=(0,M.CQ)(this.layout.layoutBox.y);c.y.min=a.target.y.min,c.y.max=c.y.min+d}J(b,c),(0,L.Ww)(b,e),(0,M.vb)(this.projectionDeltaWithTransform,this.layoutCorrected,b,e)}}registerSharedNode(a,b){this.sharedNodes.has(a)||this.sharedNodes.set(a,new $),this.sharedNodes.get(a).add(b);let c=b.options.initialPromotionConfig;b.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(b):void 0})}isLead(){let a=this.getStack();return!a||a.lead===this}getLead(){let{layoutId:a}=this.options;return a&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:a}=this.options;return a?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:b,preserveFollowOpacity:c}={}){let d=this.getStack();d&&d.promote(this,c),a&&(this.projectionDelta=void 0,this.needsReset=!0),b&&this.setOptions({transition:b})}relegate(){let a=this.getStack();return!!a&&a.relegate(this)}resetSkewAndRotation(){let{visualElement:a}=this.options;if(!a)return;let b=!1,{latestValues:c}=a;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(b=!0),!b)return;let d={};c.z&&ag("z",a,d,this.animationValues);for(let b=0;b<ae.length;b++)ag(`rotate${ae[b]}`,a,d,this.animationValues),ag(`skew${ae[b]}`,a,d,this.animationValues);for(let b in a.render(),d)a.setStaticValue(b,d[b]),this.animationValues&&(this.animationValues[b]=d[b]);a.scheduleRender()}applyProjectionStyles(a,b){if(!this.instance||this.isSVG)return;if(!this.isVisible){a.visibility="hidden";return}let c=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,a.visibility="",a.opacity="",a.pointerEvents=(0,w.u)(b?.pointerEvents)||"",a.transform=c?c(this.latestValues,""):"none";return}let d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){this.options.layoutId&&(a.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,a.pointerEvents=(0,w.u)(b?.pointerEvents)||""),this.hasProjected&&!(0,ab.HD)(this.latestValues)&&(a.transform=c?c({},""):"none",this.hasProjected=!1);return}a.visibility="";let e=d.animationValues||d.latestValues;this.applyTransformsToTarget();let f=function(a,b,c){let d="",e=a.x.translate/b.x,f=a.y.translate/b.y,g=c?.z||0;if((e||f||g)&&(d=`translate3d(${e}px, ${f}px, ${g}px) `),(1!==b.x||1!==b.y)&&(d+=`scale(${1/b.x}, ${1/b.y}) `),c){let{transformPerspective:a,rotate:b,rotateX:e,rotateY:f,skewX:g,skewY:h}=c;a&&(d=`perspective(${a}px) ${d}`),b&&(d+=`rotate(${b}deg) `),e&&(d+=`rotateX(${e}deg) `),f&&(d+=`rotateY(${f}deg) `),g&&(d+=`skewX(${g}deg) `),h&&(d+=`skewY(${h}deg) `)}let h=a.x.scale*b.x,i=a.y.scale*b.y;return(1!==h||1!==i)&&(d+=`scale(${h}, ${i})`),d||"none"}(this.projectionDeltaWithTransform,this.treeScale,e);c&&(f=c(e,f)),a.transform=f;let{x:g,y:h}=this.projectionDelta;for(let b in a.transformOrigin=`${100*g.origin}% ${100*h.origin}% 0`,d.animationValues?a.opacity=d===this?e.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:e.opacityExit:a.opacity=d===this?void 0!==e.opacity?e.opacity:"":void 0!==e.opacityExit?e.opacityExit:0,_.H){if(void 0===e[b])continue;let{correct:c,applyTo:g,isCSSVariable:h}=_.H[b],i="none"===f?e[b]:c(e[b],d);if(g){let b=g.length;for(let c=0;c<b;c++)a[g[c]]=i}else h?this.options.visualElement.renderState.vars[b]=i:a[b]=i}this.options.layoutId&&(a.pointerEvents=d===this?(0,w.u)(b?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>a.currentAnimation?.stop()),this.root.nodes.forEach(an),this.root.sharedNodes.clear()}}}function ai(a){a.updateLayout()}function aj(a){let b=a.resumeFrom?.snapshot||a.snapshot;if(a.isLead()&&a.layout&&b&&a.hasListeners("didUpdate")){let{layoutBox:c,measuredBox:d}=a.layout,{animationType:e}=a.options,f=b.source!==a.layout.source;"size"===e?(0,aa.X)(a=>{let d=f?b.measuredBox[a]:b.layoutBox[a],e=(0,M.CQ)(d);d.min=c[a].min,d.max=d.min+e}):aC(e,b.layoutBox,c)&&(0,aa.X)(d=>{let e=f?b.measuredBox[d]:b.layoutBox[d],g=(0,M.CQ)(c[d]);e.max=e.min+g,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[d].max=a.relativeTarget[d].min+g)});let g=(0,S.xU)();(0,M.vb)(g,c,b.layoutBox);let h=(0,S.xU)();f?(0,M.vb)(h,a.applyTransform(d,!0),b.measuredBox):(0,M.vb)(h,c,b.layoutBox);let i=!U(g),j=!1;if(!a.resumeFrom){let d=a.getClosestProjectingParent();if(d&&!d.resumeFrom){let{snapshot:e,layout:f}=d;if(e&&f){let g=(0,S.ge)();(0,M.jA)(g,b.layoutBox,e.layoutBox);let h=(0,S.ge)();(0,M.jA)(h,c,f.layoutBox),X(g,h)||(j=!0),d.options.layoutRoot&&(a.relativeTarget=h,a.relativeTargetOrigin=g,a.relativeParent=d)}}}a.notifyListeners("didUpdate",{layout:c,snapshot:b,delta:h,layoutDelta:g,hasLayoutChanged:i,hasRelativeLayoutChanged:j})}else if(a.isLead()){let{onExitComplete:b}=a.options;b&&b()}a.options.transition=void 0}function ak(a){e.Q.value&&ad.nodes++,a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function al(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function am(a){a.clearSnapshot()}function an(a){a.clearMeasurements()}function ao(a){a.isLayoutDirty=!1}function ap(a){let{visualElement:b}=a.options;b&&b.getProps().onBeforeLayoutMeasure&&b.notify("BeforeLayoutMeasure"),a.resetTransform()}function aq(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function ar(a){a.resolveTargetDelta()}function as(a){a.calcProjection()}function at(a){a.resetSkewAndRotation()}function au(a){a.removeLeadSnapshot()}function av(a,b,c){a.translate=(0,m.k)(b.translate,0,c),a.scale=(0,m.k)(b.scale,1,c),a.origin=b.origin,a.originPoint=b.originPoint}function aw(a,b,c,d){a.min=(0,m.k)(b.min,c.min,d),a.max=(0,m.k)(b.max,c.max,d)}function ax(a){return a.animationValues&&void 0!==a.animationValues.opacityExit}let ay={duration:.45,ease:[.4,0,.1,1]},az=a=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),aA=az("applewebkit/")&&!az("chrome/")?Math.round:p.l;function aB(a){a.min=aA(a.min),a.max=aA(a.max)}function aC(a,b,c){return"position"===a||"preserve-aspect"===a&&!(0,M.HQ)(Y(b),Y(c),.2)}function aD(a){return a!==a.root&&a.scroll?.wasRoot}var aE=c(58902);let aF=ah({attachResizeListener:(a,b)=>(0,aE.k)(a,"resize",b),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),aG={current:void 0},aH=ah({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!aG.current){let a=new aF({});a.mount(window),a.setOptions({layoutScroll:!0}),aG.current=a}return aG.current},resetTransform:(a,b)=>{a.style.transform=void 0!==b?b:"none"},checkIsScrollRoot:a=>"fixed"===window.getComputedStyle(a).position})},81939:(a,b,c)=>{c.d(b,{$:()=>S});var d=c(64496),e=c(83361),f=c(26181),g=c(32874),h=c(23671),i=c(68028),j=c(66244),k=c(96259),l=c(58902),m=c(27100);function n(a,b,c,d){return(0,l.k)(a,b,(0,m.F)(c),d)}var o=c(32572),p=c(20172),q=c(54538),r=c(27642),s=c(92953);let t=({current:a})=>a?a.ownerDocument.defaultView:null;var u=c(39853),v=c(67283),w=c(28328),x=c(78205),y=c(57211);let z=(a,b)=>Math.abs(a-b);class A{constructor(a,b,{transformPagePoint:c,contextWindow:d=window,dragSnapToOrigin:e=!1,distanceThreshold:f=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=D(this.lastMoveEventInfo,this.history),b=null!==this.startEvent,c=function(a,b){return Math.sqrt(z(a.x,b.x)**2+z(a.y,b.y)**2)}(a.offset,{x:0,y:0})>=this.distanceThreshold;if(!b&&!c)return;let{point:d}=a,{timestamp:e}=h.uv;this.history.push({...d,timestamp:e});let{onStart:f,onMove:g}=this.handlers;b||(f&&f(this.lastMoveEvent,a),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,a)},this.handlePointerMove=(a,b)=>{this.lastMoveEvent=a,this.lastMoveEventInfo=B(b,this.transformPagePoint),h.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(a,b)=>{this.end();let{onEnd:c,onSessionEnd:d,resumeAnimation:e}=this.handlers;if(this.dragSnapToOrigin&&e&&e(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let f=D("pointercancel"===a.type?this.lastMoveEventInfo:B(b,this.transformPagePoint),this.history);this.startEvent&&c&&c(a,f),d&&d(a,f)},!(0,w.M)(a))return;this.dragSnapToOrigin=e,this.handlers=b,this.transformPagePoint=c,this.distanceThreshold=f,this.contextWindow=d||window;let g=B((0,m.e)(a),this.transformPagePoint),{point:i}=g,{timestamp:j}=h.uv;this.history=[{...i,timestamp:j}];let{onSessionStart:k}=b;k&&k(a,D(g,this.history)),this.removeListeners=(0,x.F)(n(this.contextWindow,"pointermove",this.handlePointerMove),n(this.contextWindow,"pointerup",this.handlePointerUp),n(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),(0,h.WG)(this.updatePoint)}}function B(a,b){return b?{point:b(a.point)}:a}function C(a,b){return{x:a.x-b.x,y:a.y-b.y}}function D({point:a},b){return{point:a,delta:C(a,E(b)),offset:C(a,b[0]),velocity:function(a,b){if(a.length<2)return{x:0,y:0};let c=a.length-1,d=null,e=E(a);for(;c>=0&&(d=a[c],!(e.timestamp-d.timestamp>(0,y.f)(.1)));)c--;if(!d)return{x:0,y:0};let f=(0,y.X)(e.timestamp-d.timestamp);if(0===f)return{x:0,y:0};let g={x:(e.x-d.x)/f,y:(e.y-d.y)/f};return g.x===1/0&&(g.x=0),g.y===1/0&&(g.y=0),g}(b,.1)}}function E(a){return a[a.length-1]}var F=c(64068),G=c(97758);function H(a,b,c){return{min:void 0!==b?a.min+b:void 0,max:void 0!==c?a.max+c-(a.max-a.min):void 0}}function I(a,b){let c=b.min-a.min,d=b.max-a.max;return b.max-b.min<a.max-a.min&&([c,d]=[d,c]),{min:c,max:d}}function J(a,b,c){return{min:K(a,b),max:K(a,c)}}function K(a,b){return"number"==typeof a?a:a[b]||0}let L=new WeakMap;class M{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,q.ge)(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=a}start(a,{snapToCursor:b=!1,distanceThreshold:c}={}){let{presenceContext:d}=this.visualElement;if(d&&!1===d.isPresent)return;let e=a=>{let{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),b&&this.snapToCursor((0,m.e)(a).point)},i=(a,b)=>{let{drag:c,dragPropagation:d,onDragStart:e}=this.getProps();if(c&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(a){if("x"===a||"y"===a)if(f.I[a])return null;else return f.I[a]=!0,()=>{f.I[a]=!1};return f.I.x||f.I.y?null:(f.I.x=f.I.y=!0,()=>{f.I.x=f.I.y=!1})}(c),!this.openDragLock))return;this.latestPointerEvent=a,this.latestPanInfo=b,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),(0,r.X)(a=>{let b=this.getAxisMotionValue(a).get()||0;if(g.KN.test(b)){let{projection:c}=this.visualElement;if(c&&c.layout){let d=c.layout.layoutBox[a];d&&(b=(0,p.CQ)(d)*(parseFloat(b)/100))}}this.originPoint[a]=b}),e&&h.Gt.postRender(()=>e(a,b)),(0,v.g)(this.visualElement,"transform");let{animationState:i}=this.visualElement;i&&i.setActive("whileDrag",!0)},j=(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b;let{dragPropagation:c,dragDirectionLock:d,onDirectionLock:e,onDrag:f}=this.getProps();if(!c&&!this.openDragLock)return;let{offset:g}=b;if(d&&null===this.currentDirection){this.currentDirection=function(a,b=10){let c=null;return Math.abs(a.y)>b?c="y":Math.abs(a.x)>b&&(c="x"),c}(g),null!==this.currentDirection&&e&&e(this.currentDirection);return}this.updateAxis("x",b.point,g),this.updateAxis("y",b.point,g),this.visualElement.render(),f&&f(a,b)},k=(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b,this.stop(a,b),this.latestPointerEvent=null,this.latestPanInfo=null},l=()=>(0,r.X)(a=>"paused"===this.getAnimationState(a)&&this.getAxisMotionValue(a).animation?.play()),{dragSnapToOrigin:n}=this.getProps();this.panSession=new A(a,{onSessionStart:e,onStart:i,onMove:j,onSessionEnd:k,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,distanceThreshold:c,contextWindow:t(this.visualElement)})}stop(a,b){let c=a||this.latestPointerEvent,d=b||this.latestPanInfo,e=this.isDragging;if(this.cancel(),!e||!d||!c)return;let{velocity:f}=d;this.startAnimation(f);let{onDragEnd:g}=this.getProps();g&&h.Gt.postRender(()=>g(c,d))}cancel(){this.isDragging=!1;let{projection:a,animationState:b}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:c}=this.getProps();!c&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),b&&b.setActive("whileDrag",!1)}updateAxis(a,b,c){let{drag:d}=this.getProps();if(!c||!N(a,d,this.currentDirection))return;let e=this.getAxisMotionValue(a),f=this.originPoint[a]+c[a];this.constraints&&this.constraints[a]&&(f=function(a,{min:b,max:c},d){return void 0!==b&&a<b?a=d?(0,i.k)(b,a,d.min):Math.max(a,b):void 0!==c&&a>c&&(a=d?(0,i.k)(c,a,d.max):Math.min(a,c)),a}(f,this.constraints[a],this.elastic[a])),e.set(f)}resolveConstraints(){let{dragConstraints:a,dragElastic:b}=this.getProps(),c=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,d=this.constraints;a&&(0,u.X)(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&c?this.constraints=function(a,{top:b,left:c,bottom:d,right:e}){return{x:H(a.x,c,e),y:H(a.y,b,d)}}(c.layoutBox,a):this.constraints=!1,this.elastic=function(a=.35){return!1===a?a=0:!0===a&&(a=.35),{x:J(a,"left","right"),y:J(a,"top","bottom")}}(b),d!==this.constraints&&c&&this.constraints&&!this.hasMutatedConstraints&&(0,r.X)(a=>{!1!==this.constraints&&this.getAxisMotionValue(a)&&(this.constraints[a]=function(a,b){let c={};return void 0!==b.min&&(c.min=b.min-a.min),void 0!==b.max&&(c.max=b.max-a.min),c}(c.layoutBox[a],this.constraints[a]))})}resolveRefConstraints(){var a;let{dragConstraints:b,onMeasureDragConstraints:c}=this.getProps();if(!b||!(0,u.X)(b))return!1;let d=b.current;(0,j.V)(null!==d,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:e}=this.visualElement;if(!e||!e.layout)return!1;let f=(0,s.L)(d,e.root,this.visualElement.getTransformPagePoint()),g=(a=e.layout.layoutBox,{x:I(a.x,f.x),y:I(a.y,f.y)});if(c){let a=c((0,o.pA)(g));this.hasMutatedConstraints=!!a,a&&(g=(0,o.FY)(a))}return g}startAnimation(a){let{drag:b,dragMomentum:c,dragElastic:d,dragTransition:e,dragSnapToOrigin:f,onDragTransitionEnd:g}=this.getProps(),h=this.constraints||{};return Promise.all((0,r.X)(g=>{if(!N(g,b,this.currentDirection))return;let i=h&&h[g]||{};f&&(i={min:0,max:0});let j={type:"inertia",velocity:c?a[g]:0,bounceStiffness:d?200:1e6,bounceDamping:d?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...e,...i};return this.startAxisValueAnimation(g,j)})).then(g)}startAxisValueAnimation(a,b){let c=this.getAxisMotionValue(a);return(0,v.g)(this.visualElement,a),c.start((0,k.f)(a,c,0,b,this.visualElement,!1))}stopAnimation(){(0,r.X)(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){(0,r.X)(a=>this.getAxisMotionValue(a).animation?.pause())}getAnimationState(a){return this.getAxisMotionValue(a).animation?.state}getAxisMotionValue(a){let b=`_drag${a.toUpperCase()}`,c=this.visualElement.getProps();return c[b]||this.visualElement.getValue(a,(c.initial?c.initial[a]:void 0)||0)}snapToCursor(a){(0,r.X)(b=>{let{drag:c}=this.getProps();if(!N(b,c,this.currentDirection))return;let{projection:d}=this.visualElement,e=this.getAxisMotionValue(b);if(d&&d.layout){let{min:c,max:f}=d.layout.layoutBox[b];e.set(a[b]-(0,i.k)(c,f,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:a,dragConstraints:b}=this.getProps(),{projection:c}=this.visualElement;if(!(0,u.X)(b)||!c||!this.constraints)return;this.stopAnimation();let d={x:0,y:0};(0,r.X)(a=>{let b=this.getAxisMotionValue(a);if(b&&!1!==this.constraints){let c=b.get();d[a]=function(a,b){let c=.5,d=(0,p.CQ)(a),e=(0,p.CQ)(b);return e>d?c=(0,F.q)(b.min,b.max-d,a.min):d>e&&(c=(0,F.q)(a.min,a.max-e,b.min)),(0,G.q)(0,1,c)}({min:c,max:c},this.constraints[a])}});let{transformTemplate:e}=this.visualElement.getProps();this.visualElement.current.style.transform=e?e({},""):"none",c.root&&c.root.updateScroll(),c.updateLayout(),this.resolveConstraints(),(0,r.X)(b=>{if(!N(b,a,null))return;let c=this.getAxisMotionValue(b),{min:e,max:f}=this.constraints[b];c.set((0,i.k)(e,f,d[b]))})}addListeners(){if(!this.visualElement.current)return;L.set(this.visualElement,this);let a=n(this.visualElement.current,"pointerdown",a=>{let{drag:b,dragListener:c=!0}=this.getProps();b&&c&&this.start(a)}),b=()=>{let{dragConstraints:a}=this.getProps();(0,u.X)(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",b);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),h.Gt.read(b);let e=(0,l.k)(window,"resize",()=>this.scalePositionWithinConstraints()),f=c.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b})=>{this.isDragging&&b&&((0,r.X)(b=>{let c=this.getAxisMotionValue(b);c&&(this.originPoint[b]+=a[b].translate,c.set(c.get()+a[b].translate))}),this.visualElement.render())});return()=>{e(),a(),d(),f&&f()}}getProps(){let a=this.visualElement.getProps(),{drag:b=!1,dragDirectionLock:c=!1,dragPropagation:d=!1,dragConstraints:e=!1,dragElastic:f=.35,dragMomentum:g=!0}=a;return{...a,drag:b,dragDirectionLock:c,dragPropagation:d,dragConstraints:e,dragElastic:f,dragMomentum:g}}}function N(a,b,c){return(!0===b||b===a)&&(null===c||c===a)}class O extends d.X{constructor(a){super(a),this.removeGroupControls=e.l,this.removeListeners=e.l,this.controls=new M(a)}mount(){let{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||e.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let P=a=>(b,c)=>{a&&h.Gt.postRender(()=>a(b,c))};class Q extends d.X{constructor(){super(...arguments),this.removePointerDownListener=e.l}onPointerDown(a){this.session=new A(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:t(this.node)})}createPanHandlers(){let{onPanSessionStart:a,onPanStart:b,onPan:c,onPanEnd:d}=this.node.getProps();return{onSessionStart:P(a),onStart:P(b),onMove:c,onEnd:(a,b)=>{delete this.session,d&&h.Gt.postRender(()=>d(a,b))}}}mount(){this.removePointerDownListener=n(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var R=c(86652);let S={pan:{Feature:Q},drag:{Feature:O,ProjectionNode:c(76242).P,MeasureLayout:R.$}}},86652:(a,b,c)=>{c.d(b,{$:()=>t});var d=c(60687),e=c(23671),f=c(59039),g=c(43210),h=c(86044),i=c(12157),j=c(83641),k=c(25103),l=c(32874);function m(a,b){return b.max===b.min?0:a/(b.max-b.min)*100}let n={correct:(a,b)=>{if(!b.target)return a;if("string"==typeof a)if(!l.px.test(a))return a;else a=parseFloat(a);let c=m(a,b.target.x),d=m(a,b.target.y);return`${c}% ${d}%`}};var o=c(39664),p=c(68028),q=c(96633);let r=!1;class s extends g.Component{componentDidMount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c,layoutId:d}=this.props,{projection:e}=a;(0,q.$)(u),e&&(b.group&&b.group.add(e),c&&c.register&&d&&c.register(e),r&&e.root.didUpdate(),e.addEventListener("animationComplete",()=>{this.safeToRemove()}),e.setOptions({...e.options,onExitComplete:()=>this.safeToRemove()})),k.w.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){let{layoutDependency:b,visualElement:c,drag:d,isPresent:f}=this.props,{projection:g}=c;return g&&(g.isPresent=f,r=!0,d||a.layoutDependency!==b||void 0===b||a.isPresent!==f?g.willUpdate():this.safeToRemove(),a.isPresent!==f&&(f?g.promote():g.relegate()||e.Gt.postRender(()=>{let a=g.getStack();a&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),f.k.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c}=this.props,{projection:d}=a;r=!0,d&&(d.scheduleCheckAfterUnmount(),b&&b.group&&b.group.remove(d),c&&c.deregister&&c.deregister(d))}safeToRemove(){let{safeToRemove:a}=this.props;a&&a()}render(){return null}}function t(a){let[b,c]=(0,h.xQ)(),e=(0,g.useContext)(i.L);return(0,d.jsx)(s,{...a,layoutGroup:e,switchLayoutGroup:(0,g.useContext)(j.N),isPresent:b,safeToRemove:c})}let u={borderRadius:{...n,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:n,borderTopRightRadius:n,borderBottomLeftRadius:n,borderBottomRightRadius:n,boxShadow:{correct:(a,{treeScale:b,projectionDelta:c})=>{let d=o.f.parse(a);if(d.length>5)return a;let e=o.f.createTransformer(a),f=+("number"!=typeof d[0]),g=c.x.scale*b.x,h=c.y.scale*b.y;d[0+f]/=g,d[1+f]/=h;let i=(0,p.k)(g,h,.5);return"number"==typeof d[2+f]&&(d[2+f]/=i),"number"==typeof d[3+f]&&(d[3+f]/=i),e(d)}}}}};