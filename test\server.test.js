import { test, describe, before, after } from 'node:test';
import assert from 'node:assert';
import { build } from '../src/server.js';

describe('Sintesa Backend API Tests', () => {
  let app;

  before(async () => {
    // Build the app for testing
    app = build({ logger: false });
    await app.ready();
  });

  after(async () => {
    await app.close();
  });

  describe('Health Check Endpoints', () => {
    test('GET /api/health should return 200', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/health'
      });

      assert.strictEqual(response.statusCode, 200);
      const payload = JSON.parse(response.payload);
      assert.strictEqual(payload.status, 'ok');
      assert.ok(payload.timestamp);
      assert.ok(typeof payload.uptime === 'number');
    });

    test('GET /api/ping should return 200', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/ping'
      });

      assert.strictEqual(response.statusCode, 200);
      const payload = JSON.parse(response.payload);
      assert.strictEqual(payload.message, 'pong');
    });

    test('GET /api/ready should return 200', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/ready'
      });

      assert.strictEqual(response.statusCode, 200);
      const payload = JSON.parse(response.payload);
      assert.strictEqual(payload.status, 'ready');
    });
  });

  describe('Authentication Endpoints', () => {
    test('POST /api/auth/login with invalid credentials should return 401', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/login',
        payload: {
          username: 'invalid',
          password: 'invalid'
        }
      });

      assert.strictEqual(response.statusCode, 401);
      const payload = JSON.parse(response.payload);
      assert.strictEqual(payload.success, false);
    });

    test('POST /api/auth/login with missing fields should return 400', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/login',
        payload: {
          username: 'test'
          // missing password
        }
      });

      assert.strictEqual(response.statusCode, 400);
      const payload = JSON.parse(response.payload);
      assert.strictEqual(payload.success, false);
    });

    test('GET /api/auth/status without token should return 401', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/auth/status'
      });

      assert.strictEqual(response.statusCode, 401);
    });
  });

  describe('Protected Routes', () => {
    test('GET /api/users without authentication should return 401', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/users'
      });

      assert.strictEqual(response.statusCode, 401);
    });

    test('GET /api/dashboard without authentication should return 401', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/dashboard'
      });

      assert.strictEqual(response.statusCode, 401);
    });

    test('GET /api/referensi/categories without authentication should return 401', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/referensi/categories'
      });

      assert.strictEqual(response.statusCode, 401);
    });
  });

  describe('Error Handling', () => {
    test('GET /api/nonexistent should return 404', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/nonexistent'
      });

      assert.strictEqual(response.statusCode, 404);
      const payload = JSON.parse(response.payload);
      assert.strictEqual(payload.success, false);
      assert.strictEqual(payload.error.code, 404);
    });

    test('POST with invalid JSON should return 400', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/auth/login',
        payload: 'invalid json',
        headers: {
          'content-type': 'application/json'
        }
      });

      assert.strictEqual(response.statusCode, 400);
    });
  });

  describe('CORS Headers', () => {
    test('OPTIONS request should include CORS headers', async () => {
      const response = await app.inject({
        method: 'OPTIONS',
        url: '/api/health',
        headers: {
          'origin': 'http://localhost:3000',
          'access-control-request-method': 'GET'
        }
      });

      assert.ok(response.headers['access-control-allow-origin']);
      assert.ok(response.headers['access-control-allow-methods']);
    });
  });

  describe('Rate Limiting', () => {
    test('Multiple rapid requests should eventually be rate limited', async () => {
      const requests = [];
      
      // Make many requests rapidly
      for (let i = 0; i < 150; i++) {
        requests.push(
          app.inject({
            method: 'GET',
            url: '/api/health'
          })
        );
      }

      const responses = await Promise.all(requests);
      
      // At least one should be rate limited
      const rateLimited = responses.some(response => response.statusCode === 429);
      
      // Note: This test might be flaky depending on rate limit configuration
      // In a real scenario, you might want to configure different limits for testing
      console.log('Rate limiting test - some requests may be limited:', rateLimited);
    });
  });

  describe('Security Headers', () => {
    test('Response should include security headers', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/health'
      });

      // Check for security headers added by helmet
      assert.ok(response.headers['x-frame-options']);
      assert.ok(response.headers['x-content-type-options']);
      assert.ok(response.headers['x-xss-protection'] || response.headers['x-xss-protection'] === '0');
    });
  });

  describe('Content Type Handling', () => {
    test('JSON endpoints should return application/json', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/health'
      });

      assert.ok(response.headers['content-type'].includes('application/json'));
    });
  });

  describe('Request Logging', () => {
    test('Requests should be logged with proper format', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/health'
      });

      // Check that response time header is added
      assert.ok(response.headers['x-response-time']);
      assert.ok(response.headers['x-powered-by']);
    });
  });
});

// Integration tests with database (if available)
describe('Database Integration Tests', () => {
  let app;

  before(async () => {
    app = build({ logger: false });
    await app.ready();
  });

  after(async () => {
    await app.close();
  });

  test('Database connection should be available', async () => {
    try {
      // Test database connection through system status endpoint
      const response = await app.inject({
        method: 'GET',
        url: '/api/system',
        headers: {
          'authorization': 'Bearer test-token' // This will fail but test the endpoint
        }
      });

      // Even if unauthorized, the endpoint should exist
      assert.ok(response.statusCode === 401 || response.statusCode === 200);
    } catch (error) {
      console.log('Database integration test skipped - no connection available');
    }
  });
});

// Performance tests
describe('Performance Tests', () => {
  let app;

  before(async () => {
    app = build({ logger: false });
    await app.ready();
  });

  after(async () => {
    await app.close();
  });

  test('Health check should respond quickly', async () => {
    const start = Date.now();
    
    const response = await app.inject({
      method: 'GET',
      url: '/api/health'
    });

    const duration = Date.now() - start;
    
    assert.strictEqual(response.statusCode, 200);
    assert.ok(duration < 100, `Health check took ${duration}ms, should be under 100ms`);
  });

  test('Concurrent requests should be handled efficiently', async () => {
    const start = Date.now();
    const concurrentRequests = 10;
    
    const requests = Array(concurrentRequests).fill().map(() => 
      app.inject({
        method: 'GET',
        url: '/api/health'
      })
    );

    const responses = await Promise.all(requests);
    const duration = Date.now() - start;
    
    // All requests should succeed
    responses.forEach(response => {
      assert.strictEqual(response.statusCode, 200);
    });
    
    // Should handle concurrent requests efficiently
    assert.ok(duration < 500, `Concurrent requests took ${duration}ms, should be under 500ms`);
  });
});