import Fastify from 'fastify';

const fastify = Fastify({
  logger: true
});

// Test route
fastify.get('/', async (request, reply) => {
  return { hello: 'world' };
});

// Start server
async function start() {
  try {
    const port = 88;
    const host = '0.0.0.0';
    
    await fastify.listen({ port, host });
    console.log(`Server running on http://${host}:${port}`);
  } catch (err) {
    console.error('Error starting server:', err);
    process.exit(1);
  }
}

start();
