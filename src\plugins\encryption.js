import CryptoJS from "crypto-js";
import fp from "fastify-plugin";

async function encryptionPlugin(fastify, options) {
  // Encryption configuration (matching frontend)
  const secretKey = process.env.ENCRYPTION_SECRET || "mebe23";

  // Encryption utilities
  const encryption = {
    // Encrypt data (matching frontend Encrypt.jsx)
    encrypt(data, key = secretKey) {
      try {
        const encJson = CryptoJS.AES.encrypt(
          JSON.stringify(data),
          key
        ).toString();
        const encData = CryptoJS.enc.Base64.stringify(
          CryptoJS.enc.Utf8.parse(encJson)
        );
        return encData;
      } catch (error) {
        fastify.log.error("Encryption error:", error);
        throw new Error("Failed to encrypt data");
      }
    },

    // Decrypt data (matching frontend Decrypt.jsx)
    decrypt(encryptedData, key = secretKey) {
      try {
        const decData = CryptoJS.enc.Base64.parse(encryptedData).toString(
          CryptoJS.enc.Utf8
        );
        const bytes = CryptoJS.AES.decrypt(decData, key).toString(
          CryptoJS.enc.Utf8
        );
        return JSON.parse(bytes);
      } catch (error) {
        fastify.log.error("Decryption error:", error);
        throw new Error("Failed to decrypt data");
      }
    },

    // Encrypt string data (for tokens, queries, etc. - matching frontend exactly)
    encryptString(data, key = secretKey) {
      try {
        // This matches the frontend Encrypt.jsx for strings
        const encJson = CryptoJS.AES.encrypt(data, key).toString();
        const encData = CryptoJS.enc.Base64.stringify(
          CryptoJS.enc.Utf8.parse(encJson)
        );
        return encData;
      } catch (error) {
        fastify.log.error("String encryption error:", error);
        throw new Error("Failed to encrypt string data");
      }
    },

    // Decrypt string data (for tokens, queries, etc. - matching frontend exactly)
    decryptString(encryptedData, key = secretKey) {
      try {
        // This matches the frontend Decrypt.jsx exactly
        const decData = CryptoJS.enc.Base64.parse(encryptedData).toString(
          CryptoJS.enc.Utf8
        );
        const bytes = CryptoJS.AES.decrypt(decData, key).toString(
          CryptoJS.enc.Utf8
        );
        return bytes; // Return string directly, no JSON.parse
      } catch (error) {
        fastify.log.error("String decryption error:", error);
        throw new Error("Failed to decrypt string data");
      }
    },

    // Encrypt SQL query (for inquiry system)
    encryptQuery(sql, key = secretKey) {
      try {
        const encodedQuery = encodeURIComponent(sql);
        return this.encrypt(encodedQuery, key);
      } catch (error) {
        fastify.log.error("Query encryption error:", error);
        throw new Error("Failed to encrypt query");
      }
    },

    // Decrypt SQL query (for inquiry system)
    decryptQuery(encryptedQuery, key = secretKey) {
      try {
        const decryptedQuery = this.decrypt(encryptedQuery, key);
        return decodeURIComponent(decryptedQuery);
      } catch (error) {
        fastify.log.error("Query decryption error:", error);
        throw new Error("Failed to decrypt query");
      }
    },

    // Encrypt sensitive user data
    encryptUserData(userData, key = secretKey) {
      try {
        const sensitiveFields = ["password", "email", "phone", "nip"];
        const encrypted = { ...userData };

        for (const field of sensitiveFields) {
          if (encrypted[field]) {
            encrypted[field] = this.encrypt(encrypted[field], key);
          }
        }

        return encrypted;
      } catch (error) {
        fastify.log.error("User data encryption error:", error);
        throw new Error("Failed to encrypt user data");
      }
    },

    // Decrypt sensitive user data
    decryptUserData(encryptedUserData, key = secretKey) {
      try {
        const sensitiveFields = ["password", "email", "phone", "nip"];
        const decrypted = { ...encryptedUserData };

        for (const field of sensitiveFields) {
          if (decrypted[field]) {
            try {
              decrypted[field] = this.decrypt(decrypted[field], key);
            } catch {
              // Field might not be encrypted, keep original value
            }
          }
        }

        return decrypted;
      } catch (error) {
        fastify.log.error("User data decryption error:", error);
        throw new Error("Failed to decrypt user data");
      }
    },

    // Generate secure hash
    generateHash(data) {
      try {
        return CryptoJS.SHA256(JSON.stringify(data)).toString();
      } catch (error) {
        fastify.log.error("Hash generation error:", error);
        throw new Error("Failed to generate hash");
      }
    },

    // Generate HMAC
    generateHMAC(data, key = secretKey) {
      try {
        return CryptoJS.HmacSHA256(JSON.stringify(data), key).toString();
      } catch (error) {
        fastify.log.error("HMAC generation error:", error);
        throw new Error("Failed to generate HMAC");
      }
    },

    // Verify HMAC
    verifyHMAC(data, hmac, key = secretKey) {
      try {
        const expectedHMAC = this.generateHMAC(data, key);
        return (
          CryptoJS.enc.Hex.parse(hmac).toString() ===
          CryptoJS.enc.Hex.parse(expectedHMAC).toString()
        );
      } catch (error) {
        fastify.log.error("HMAC verification error:", error);
        return false;
      }
    },

    // Generate random key
    generateRandomKey(length = 32) {
      try {
        return CryptoJS.lib.WordArray.random(length).toString();
      } catch (error) {
        fastify.log.error("Random key generation error:", error);
        throw new Error("Failed to generate random key");
      }
    },

    // Encrypt with timestamp (for time-sensitive data)
    encryptWithTimestamp(data, key = secretKey, validityMinutes = 60) {
      try {
        const timestamp = Date.now();
        const expiryTime = timestamp + validityMinutes * 60 * 1000;
        const dataWithTimestamp = {
          data,
          timestamp,
          expiryTime,
        };
        return this.encrypt(dataWithTimestamp, key);
      } catch (error) {
        fastify.log.error("Timestamp encryption error:", error);
        throw new Error("Failed to encrypt data with timestamp");
      }
    },

    // Decrypt with timestamp validation
    decryptWithTimestamp(encryptedData, key = secretKey) {
      try {
        const decryptedData = this.decrypt(encryptedData, key);
        const currentTime = Date.now();

        if (currentTime > decryptedData.expiryTime) {
          throw new Error("Encrypted data has expired");
        }

        return decryptedData.data;
      } catch (error) {
        if (error.message === "Encrypted data has expired") {
          throw error;
        }
        fastify.log.error("Timestamp decryption error:", error);
        throw new Error("Failed to decrypt data with timestamp");
      }
    },

    // Encrypt file data
    encryptFile(fileBuffer, key = secretKey) {
      try {
        const wordArray = CryptoJS.lib.WordArray.create(fileBuffer);
        const encrypted = CryptoJS.AES.encrypt(wordArray, key).toString();
        return encrypted;
      } catch (error) {
        fastify.log.error("File encryption error:", error);
        throw new Error("Failed to encrypt file");
      }
    },

    // Decrypt file data
    decryptFile(encryptedFile, key = secretKey) {
      try {
        const decrypted = CryptoJS.AES.decrypt(encryptedFile, key);
        const wordArray = decrypted;
        const arrayBuffer = new ArrayBuffer(wordArray.sigBytes);
        const uint8Array = new Uint8Array(arrayBuffer);

        for (let i = 0; i < wordArray.sigBytes; i++) {
          uint8Array[i] =
            (wordArray.words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
        }

        return Buffer.from(arrayBuffer);
      } catch (error) {
        fastify.log.error("File decryption error:", error);
        throw new Error("Failed to decrypt file");
      }
    },

    // Secure compare (constant time)
    secureCompare(a, b) {
      try {
        if (a.length !== b.length) {
          return false;
        }

        let result = 0;
        for (let i = 0; i < a.length; i++) {
          result |= a.charCodeAt(i) ^ b.charCodeAt(i);
        }

        return result === 0;
      } catch (error) {
        fastify.log.error("Secure compare error:", error);
        return false;
      }
    },
  };

  // Request/Response encryption middleware
  fastify.addHook("preHandler", async (request, reply) => {
    // Add encryption utilities to request
    request.encrypt = encryption.encrypt.bind(encryption);
    request.decrypt = encryption.decrypt.bind(encryption);
    request.decryptString = encryption.decryptString.bind(encryption);
    request.encryptQuery = encryption.encryptQuery.bind(encryption);
    request.decryptQuery = encryption.decryptQuery.bind(encryption);
  });

  // Response encryption hook (for sensitive data)
  fastify.addHook("onSend", async (request, reply, payload) => {
    // Check if response should be encrypted
    const shouldEncrypt = request.headers["x-encrypt-response"] === "true";

    if (shouldEncrypt && payload) {
      try {
        const encryptedPayload = encryption.encrypt(JSON.parse(payload));
        reply.header("content-type", "application/json");
        return JSON.stringify({ encrypted: true, data: encryptedPayload });
      } catch (error) {
        fastify.log.error("Response encryption error:", error);
        // Return original payload if encryption fails
      }
    }

    return payload;
  });

  // Register encryption utilities
  fastify.decorate("encryption", encryption);
}

export default fp(encryptionPlugin, {
  name: "encryption",
  dependencies: [],
});
