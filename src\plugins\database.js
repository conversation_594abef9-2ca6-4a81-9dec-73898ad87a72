import mysql from 'mysql2/promise';
import fp from 'fastify-plugin';
import dotenv from 'dotenv';

// Ensure environment variables are loaded
dotenv.config();

async function databasePlugin(fastify, options) {
  // Database configuration
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'sintesa',
    charset: 'utf8mb4',
    timezone: '+07:00',
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true,
    connectionLimit: 20,
    queueLimit: 0,
    multipleStatements: true,
    supportBigNumbers: true,
    bigNumberStrings: true
  };

  // Create connection pool
  const pool = mysql.createPool(dbConfig);

  // Test connection
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    fastify.log.info('✅ Database connected successfully');
  } catch (error) {
    fastify.log.error('❌ Database connection failed:', error);
    throw error;
  }

  // Database utilities
  const db = {
    // Execute query with parameters
    async query(sql, params = []) {
      try {
        const [rows, fields] = await pool.execute(sql, params);
        return { rows, fields };
      } catch (error) {
        fastify.log.error('Database query error:', { sql, params, error: error.message });
        throw error;
      }
    },

    // Execute raw SQL (for complex queries from frontend)
    async rawQuery(sql, params = []) {
      try {
        const [rows, fields] = await pool.query(sql, params);
        return { rows, fields };
      } catch (error) {
        fastify.log.error('Database raw query error:', { sql, error: error.message });
        throw error;
      }
    },

    // Execute query with pagination
    async queryWithPagination(sql, params = [], page = 1, limit = 50) {
      try {
        // Count total records
        const countSql = `SELECT COUNT(*) as total FROM (${sql}) as count_query`;
        const [countResult] = await pool.execute(countSql, params);
        const total = countResult[0].total;

        // Calculate pagination
        const offset = (page - 1) * limit;
        const totalPages = Math.ceil(total / limit);

        // Execute paginated query
        const paginatedSql = `${sql} LIMIT ${limit} OFFSET ${offset}`;
        const [rows, fields] = await pool.execute(paginatedSql, params);

        return {
          data: rows,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: parseInt(total),
            totalPages: parseInt(totalPages),
            hasNext: page < totalPages,
            hasPrev: page > 1
          },
          fields
        };
      } catch (error) {
        fastify.log.error('Database pagination query error:', { sql, params, error: error.message });
        throw error;
      }
    },

    // Transaction support
    async transaction(callback) {
      const connection = await pool.getConnection();
      try {
        await connection.beginTransaction();
        const result = await callback(connection);
        await connection.commit();
        return result;
      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }
    },

    // Get connection for complex operations
    async getConnection() {
      return await pool.getConnection();
    },

    // Execute multiple queries
    async multiQuery(queries) {
      const connection = await pool.getConnection();
      try {
        const results = [];
        for (const { sql, params } of queries) {
          const [rows] = await connection.execute(sql, params || []);
          results.push(rows);
        }
        return results;
      } finally {
        connection.release();
      }
    },

    // Check if table exists
    async tableExists(tableName) {
      try {
        const sql = `
          SELECT COUNT(*) as count 
          FROM information_schema.tables 
          WHERE table_schema = ? AND table_name = ?
        `;
        const [rows] = await pool.execute(sql, [dbConfig.database, tableName]);
        return rows[0].count > 0;
      } catch (error) {
        fastify.log.error('Error checking table existence:', error);
        return false;
      }
    },

    // Get table structure
    async getTableStructure(tableName) {
      try {
        const sql = `DESCRIBE ${tableName}`;
        const [rows] = await pool.execute(sql);
        return rows;
      } catch (error) {
        fastify.log.error('Error getting table structure:', error);
        throw error;
      }
    },

    // Execute stored procedure
    async callProcedure(procedureName, params = []) {
      try {
        const placeholders = params.map(() => '?').join(',');
        const sql = `CALL ${procedureName}(${placeholders})`;
        const [rows] = await pool.execute(sql, params);
        return rows;
      } catch (error) {
        fastify.log.error('Error calling stored procedure:', error);
        throw error;
      }
    },

    // Bulk insert
    async bulkInsert(tableName, data) {
      if (!data || data.length === 0) return { affectedRows: 0 };
      
      try {
        const columns = Object.keys(data[0]);
        const placeholders = columns.map(() => '?').join(',');
        const sql = `INSERT INTO ${tableName} (${columns.join(',')}) VALUES (${placeholders})`;
        
        const connection = await pool.getConnection();
        try {
          await connection.beginTransaction();
          
          let totalAffectedRows = 0;
          for (const row of data) {
            const values = columns.map(col => row[col]);
            const [result] = await connection.execute(sql, values);
            totalAffectedRows += result.affectedRows;
          }
          
          await connection.commit();
          return { affectedRows: totalAffectedRows };
        } catch (error) {
          await connection.rollback();
          throw error;
        } finally {
          connection.release();
        }
      } catch (error) {
        fastify.log.error('Bulk insert error:', error);
        throw error;
      }
    }
  };

  // Register database instance
  fastify.decorate('db', db);

  // Graceful shutdown
  fastify.addHook('onClose', async () => {
    await pool.end();
    fastify.log.info('Database connection pool closed');
  });
}

export default fp(databasePlugin, {
  name: 'database',
  dependencies: []
});