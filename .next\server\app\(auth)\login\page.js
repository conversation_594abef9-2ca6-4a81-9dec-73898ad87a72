(()=>{var a={};a.id=72,a.ids=[72],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},299:(a,b,c)=>{"use strict";c.d(b,{AuthLayoutWrapper:()=>m});var d=c(60687),e=c(43210),f=c(58445),g=c(62948),h=(0,c(98462).tv)({slots:{wrapper:"relative shadow-black/5",zoomedWrapper:"relative overflow-hidden rounded-inherit",img:"relative z-10 opacity-0 shadow-black/5 data-[loaded=true]:opacity-100",blurredImg:["absolute","z-0","inset-0","w-full","h-full","object-cover","filter","blur-lg","scale-105","saturate-150","opacity-30","translate-y-1"]},variants:{radius:{none:{},sm:{},md:{},lg:{},full:{}},shadow:{none:{wrapper:"shadow-none",img:"shadow-none"},sm:{wrapper:"shadow-small",img:"shadow-small"},md:{wrapper:"shadow-medium",img:"shadow-medium"},lg:{wrapper:"shadow-large",img:"shadow-large"}},isZoomed:{true:{img:["object-cover","transform","hover:scale-125"]}},showSkeleton:{true:{wrapper:["group","relative","overflow-hidden","bg-content3 dark:bg-content2"],img:"opacity-0"}},disableAnimation:{true:{img:"transition-none"},false:{img:"transition-transform-opacity motion-reduce:transition-none !duration-300"}}},defaultVariants:{radius:"lg",shadow:"none",isZoomed:!1,isBlurred:!1,showSkeleton:!1},compoundVariants:[{showSkeleton:!0,disableAnimation:!1,class:{wrapper:["before:opacity-100","before:absolute","before:inset-0","before:-translate-x-full","before:animate-shimmer","before:border-t","before:border-content4/30","before:bg-gradient-to-r","before:from-transparent","before:via-content4","dark:before:via-default-700/10","before:to-transparent","after:opacity-100","after:absolute","after:inset-0","after:-z-10","after:bg-content3","dark:after:bg-content2"]}}],compoundSlots:[{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"none",class:"rounded-none"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"full",class:"rounded-full"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"sm",class:"rounded-small"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"md",class:"rounded-md"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"lg",class:"rounded-large"}]}),i=c(87223),j=c(79910),k=c(61822),l=(0,g.Rf)((a,b)=>{let{Component:c,domRef:l,slots:m,classNames:n,isBlurred:o,isZoomed:p,fallbackSrc:q,removeWrapper:r,disableSkeleton:s,getImgProps:t,getWrapperProps:u,getBlurredImgProps:v}=function(a){var b,c;let d=(0,f.o)(),[l,m]=(0,g.rE)(a,h.variantKeys),{ref:n,as:o,src:p,className:q,classNames:r,loading:s,isBlurred:t,fallbackSrc:u,isLoading:v,disableSkeleton:w=!!u,removeWrapper:x=!1,onError:y,onLoad:z,srcSet:A,sizes:B,crossOrigin:C,...D}=l,E=(0,k.l)({src:p,loading:s,onError:y,onLoad:z,ignoreFallback:!1,srcSet:A,sizes:B,crossOrigin:C,shouldBypassImageLoad:void 0!==o}),F=null!=(c=null!=(b=a.disableAnimation)?b:null==d?void 0:d.disableAnimation)&&c,G="loaded"===E&&!v,H="loading"===E||v,I=a.isZoomed,J=(0,i.zD)(n),{w:K,h:L}=(0,e.useMemo)(()=>({w:l.width?"number"==typeof l.width?`${l.width}px`:l.width:"fit-content",h:l.height?"number"==typeof l.height?`${l.height}px`:l.height:"auto"}),[null==l?void 0:l.width,null==l?void 0:l.height]),M=(!p||!G)&&!!u,N=H&&!w,O=(0,e.useMemo)(()=>h({...m,disableAnimation:F,showSkeleton:N}),[(0,j.t6)(m),F,N]),P=(0,j.$z)(q,null==r?void 0:r.img),Q=(0,e.useCallback)(()=>{let a=M?{backgroundImage:`url(${u})`}:{};return{className:O.wrapper({class:null==r?void 0:r.wrapper}),style:{...a,maxWidth:K}}},[O,M,u,null==r?void 0:r.wrapper,K]),R=(0,e.useCallback)(()=>({src:p,"aria-hidden":(0,j.sE)(!0),className:O.blurredImg({class:null==r?void 0:r.blurredImg})}),[O,p,null==r?void 0:r.blurredImg]);return{Component:o||"img",domRef:J,slots:O,classNames:r,isBlurred:t,disableSkeleton:w,fallbackSrc:u,removeWrapper:x,isZoomed:I,isLoading:H,getImgProps:(a={})=>{let b=(0,j.$z)(P,null==a?void 0:a.className);return{src:p,ref:J,"data-loaded":(0,j.sE)(G),className:O.img({class:b}),loading:s,srcSet:A,sizes:B,crossOrigin:C,...D,style:{...(null==D?void 0:D.height)&&{height:L},...a.style,...D.style}}},getWrapperProps:Q,getBlurredImgProps:R}}({...a,ref:b}),w=(0,d.jsx)(c,{ref:l,...t()});if(r)return w;let x=(0,d.jsx)("div",{className:m.zoomedWrapper({class:null==n?void 0:n.zoomedWrapper}),children:w});return o?(0,d.jsxs)("div",{...u(),children:[p?x:w,(0,e.cloneElement)(w,v())]}):p||!s||q?(0,d.jsxs)("div",{...u(),children:[" ",p?x:w]}):w});l.displayName="HeroUI.Image";let m=({children:a})=>(0,d.jsx)("div",{className:"flex h-screen",children:(0,d.jsxs)("div",{className:"flex-1 flex-col flex items-center justify-start md:justify-center p-6 overflow-y-auto md:overflow-hidden min-h-screen relative",children:[(0,d.jsx)("div",{className:"absolute inset-0 z-0 overflow-hidden",children:(0,d.jsx)(l,{className:"w-full h-full object-cover object-center",src:"https://nextui.org/gradients/docs-right.png",alt:"gradient"})}),(0,d.jsx)("div",{className:"relative z-10 w-full py-8 md:py-0",children:a})]})})},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6475:(a,b,c)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{callServer:function(){return d.callServer},createServerReference:function(){return f.createServerReference},findSourceMapURL:function(){return e.findSourceMapURL}});let d=c(11264),e=c(11448),f=c(7379)},10343:(a,b,c)=>{"use strict";c.d(b,{AuthLayoutWrapper:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call AuthLayoutWrapper() from the server but AuthLayoutWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\components\\features\\auth\\authLayout.tsx","AuthLayoutWrapper")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},14719:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20319:(a,b,c)=>{"use strict";c.d(b,{T:()=>e});var d=c(6475);let e=(0,d.createServerReference)("7f752043a54f8f73684b4ad1c89d25e1004858fbe7",d.callServer,void 0,d.findSourceMapURL,"createAuthCookie")},21820:a=>{"use strict";a.exports=require("os")},22746:(a,b,c)=>{"use strict";c.d(b,{Login:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Login() from the server but Login is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\sintesaNEXT2\\src\\components\\features\\auth\\login.jsx","Login")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34631:a=>{"use strict";a.exports=require("tls")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41912:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(37413),e=c(22746);let f=()=>(0,d.jsx)(e.Login,{})},42921:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(37413),e=c(10343);function f({children:a}){return(0,d.jsx)(e.AuthLayoutWrapper,{children:a})}c(35692)},47397:(a,b,c)=>{"use strict";c.d(b,{D:()=>f,Z:()=>e});var d=c(30485);let e=(0,d.Ik)().shape({username:(0,d.Yj)().required("Username is required"),password:(0,d.Yj)().required("Password is required"),captcha:(0,d.Yj)().when("$chap",{is:"0",then:a=>a.required("Captcha is required"),otherwise:a=>a.notRequired()})}),f=(0,d.Ik)().shape({name:(0,d.Yj)().required("Name is required"),email:(0,d.Yj)().email("This field must be an email").required("Email is required"),password:(0,d.Yj)().required("Password is required"),confirmPassword:(0,d.Yj)().required("Confirm password is required").oneOf([(0,d.KR)("password")],"Passwords must match")})},48634:(a,b,c)=>{Promise.resolve().then(c.bind(c,299))},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},61365:(a,b,c)=>{Promise.resolve().then(c.bind(c,71203))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71093:(a,b,c)=>{Promise.resolve().then(c.bind(c,22746))},71203:(a,b,c)=>{"use strict";c.d(b,{Login:()=>I});var d=c(60687),e=c(20319),f=c(40611),g=c(47397),h=c(41871),i=c(85015),j=c(36220),k=c(27580),l=c(53823),m=c(51060),n=c(62085);let o=(0,c(62688).A)("copyright",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M14.83 14.83a4 4 0 1 1 0-5.66",key:"1i56pz"}]]);var p=c(85814),q=c.n(p),r=c(16189),s=c(43210),t=c(67492),u=c(31514),v=c(14221),w=c(53117),x=c(88920),y=c(82319),z=c(18584),A=c(41299),B=c(81939),C=c(58875),D=c(31208);let E={...A.W,...C.n,...B.$,...D.Z},F=(0,z.H)(E,y.J);var G=c(14719);let H=({isVisible:a,onComplete:b,duration:c=2500})=>{let[e,f]=(0,s.useState)("hidden");return((0,s.useEffect)(()=>{if(a){f("showing");let a=setTimeout(()=>{f("transitioning")},1500),d=setTimeout(()=>{b?.()},c);return()=>{clearTimeout(a),clearTimeout(d)}}f("hidden")},[a,b,c]),"hidden"===e)?null:(0,d.jsx)(x.N,{mode:"wait",children:(0,d.jsxs)(F.div,{className:"fixed inset-0 z-50 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:[(0,d.jsx)(F.div,{className:"absolute inset-0 bg-gradient-to-br from-primary-500/20 via-secondary-500/20 to-success-500/20",initial:{opacity:0},animate:{opacity:1},transition:{duration:.8}}),(0,d.jsx)(F.div,{className:"absolute inset-0 backdrop-blur-lg",initial:{backdropFilter:"blur(0px)"},animate:{backdropFilter:"blur(20px)"},transition:{duration:.6}}),(0,d.jsxs)(x.N,{mode:"wait",children:["showing"===e&&(0,d.jsx)(F.div,{initial:{scale:.5,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},exit:{scale:1.1,opacity:0,y:-20},transition:{type:"spring",damping:20,stiffness:300,duration:.6},className:"relative z-10",children:(0,d.jsx)(i.Z,{className:"w-96 bg-zinc-100/90 dark:bg-zinc-900/90 backdrop-blur-xl shadow-2xl border border-white/30",children:(0,d.jsxs)(j.U,{className:"flex flex-col items-center justify-center p-10 text-center",children:[(0,d.jsxs)(F.div,{className:"relative mb-6",initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",damping:12,stiffness:150},children:[(0,d.jsx)(F.div,{className:"w-20 h-20 bg-gradient-to-br from-success-400 to-success-600 rounded-full flex items-center justify-center shadow-lg",initial:{rotate:-180},animate:{rotate:0},transition:{delay:.3,duration:.8,ease:"easeOut"},children:(0,d.jsx)(F.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.6,duration:.4},children:(0,d.jsx)(G.A,{size:40,className:"text-white",strokeWidth:2.5})})}),[void 0,void 0,void 0].map((a,b)=>(0,d.jsx)(F.div,{className:"absolute inset-0 w-20 h-20 border-2 border-success/40 rounded-full",initial:{scale:1,opacity:.8},animate:{scale:2+.5*b,opacity:0},transition:{delay:.8+.2*b,duration:1.5,ease:"easeOut"}},b))]}),(0,d.jsxs)(F.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8,duration:.5},className:"space-y-3 mb-6",children:[(0,d.jsx)("h2",{className:"text-3xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent",children:"Login Berhasil!"}),(0,d.jsx)("p",{className:"text-foreground/70 text-base",children:"Sedang mengkonfigurasi sistem..."})]}),(0,d.jsx)(F.div,{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden",initial:{opacity:0},animate:{opacity:1},transition:{delay:1},children:(0,d.jsx)(F.div,{className:"h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full",initial:{width:"0%"},animate:{width:"100%"},transition:{delay:1.1,duration:1.2,ease:"easeInOut"}})})]})})},"success-content"),"transitioning"===e&&(0,d.jsx)(F.div,{initial:{scale:.8,opacity:0},animate:{scale:1,opacity:1},transition:{duration:.4},className:"flex items-center space-x-3 text-white",children:(0,d.jsx)("span",{className:"text-xl font-medium",children:"Welcome to sintesaNEXT"})},"transition-content")]}),(0,d.jsx)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[...Array(15)].map((a,b)=>(0,d.jsx)(F.div,{className:"absolute w-1 h-1 bg-primary-400/60 rounded-full",style:{left:`${100*Math.random()}%`,top:`${100*Math.random()}%`},initial:{scale:0,opacity:0},animate:{scale:[0,1,0],opacity:[0,1,0],y:[0,-50,-100]},transition:{duration:3,delay:2*Math.random(),repeat:1/0,ease:"easeOut"}},b))})]},"success-overlay")})},I=()=>{let a=(0,s.useContext)(v.A);if(!a)throw Error("Login must be used within MyContextProvider");let{setVerified:b,setNmrole:c,setRole:p,setName:x,setActive:y,setKdlokasi:z,setKdkanwil:A,setDeptlimit:B,setKdkppn:C,setExpire:D,setToken:E,setIduser:F,setUrl:G,setstatusLogin:I,setUsername:J,setMode:K,setTampil:L,setTampilverify:M,setStatus:N,setPersentase:O,setSession:P,setNamelogin:Q,setLoggedInUser2:R,setLoggedinUsers:S,telp:T,setTelp:U,offline:V,setOffline:W,offlinest:X,setOfflinest:Y}=a,Z=(0,r.useRouter)(),{showToast:$}=(0,f.d)(),[_,aa]=(0,s.useState)(!1),[ab,ac]=(0,s.useState)(!1),[ad,ae]=(0,s.useState)(""),[af,ag]=(0,s.useState)("0"),[ah,ai]=(0,s.useState)(""),[aj,ak]=(0,s.useState)(""),[al,am]=(0,s.useState)(""),[an,ao]=(0,s.useState)(!1),[ap,aq]=(0,s.useState)(!1),ar=(0,s.useCallback)(()=>{ai(as((Math.floor(9e3*Math.random())+1e3).toString()))},[]),as=a=>{let b=Math.floor(Math.random()*(a.length-1))+1,c="";for(let d=0;d<a.length;d++)c+=a[d],d<a.length-1&&d<b&&(c+=" ");return c};(0,s.useEffect)(()=>{if("0"===af){ar();let a=setInterval(()=>{ar()},2e4);return()=>{clearInterval(a)}}},[af,ar]);let at=(0,s.useCallback)(async a=>{if("1"===af&&""===aj){am("Captcha belum Diverifikasi"),$("Please complete the captcha verification.","warning");return}if("0"===af){if((a.captcha?.replace(/\s/g,"")||"")!==ah.replace(/\s/g,""))return void $("Captcha code is incorrect.","error")}else if(""===af){am("Captcha Error"),$("Captcha system error.","error");return}aa(!0);try{let d=(await m.A.post("http://localhost:88/next/auth/login",a,{withCredentials:!0})).data;if(d.success){x(""),p(""),c(""),b(""),y(""),z(""),A(""),B(""),C(""),D(""),E(""),F(""),G(""),I(""),J(""),K(""),L(""),M(""),N(""),O(""),P(""),Q(""),R(""),S("");let a=(0,u.a)(d.data.token),f=(0,w.s)(a);console.log(d.data.token),U(f.telp),E(d.data.token),I(!0),aq(!1),x(f.name),D(f.exp.toString()),p(f.role),A(f.kdkanwil),C(f.kdkppn),z(f.kdlokasi),y(f.active),B(f.dept_limit),c(f.namarole),F(f.userId),G(f.url),J(f.username),K(f.mode),L(f.tampil),M(f.tampilverify),P(f.session),b(f.verified),ao(!0),localStorage.setItem("status","true"),localStorage.setItem("token",d.data.token),await (0,e.T)("token",d.data.token)}else"Password Anda Tidak Sesuai"===d.msg?(am("Password Anda Tidak Sesuai"),$("Password Anda Tidak Sesuai","error")):"User tidak ditemukan"===d.msg?(am("User tidak ditemukan"),$("User Tidak Ditemukan","error")):(am("Terjadi kesalahan saat login"),$("Terjadi Kesalahan saat login","error")),aq(!1)}catch(a){console.log(a),$("Login Gagal","error")}finally{aa(!1)}},[Z,$,af,aj,ah]),au=(0,s.useCallback)(async()=>{if(!ad)return void $("Please enter your 6-digit PIN.","warning");if(6!==ad.length)return void $("PIN must be exactly 6 digits.","warning");ac(!0);try{if("123456"!==ad){$("Invalid PIN. Please try again.","error"),ae("");return}ao(!0),localStorage.setItem("status","true"),await (0,e.T)("token","pin_authenticated")}catch(a){$("PIN login failed. Please try again.","error")}finally{ac(!1)}},[ad,$]),av=(0,s.useCallback)(()=>{ao(!1),Z.replace("/")},[Z]);return(0,d.jsxs)("div",{className:"flex flex-col items-center w-full",children:[(0,d.jsx)(H,{isVisible:an,onComplete:av,duration:2500}),(0,d.jsx)("div",{className:"text-center text-[25px] font-bold mb-8",children:(0,d.jsx)(t.A,{className:"h-8 w-auto"})}),(0,d.jsx)(n.l1,{initialValues:{username:"",password:"",captcha:""},validationSchema:g.Z,context:{chap:af},onSubmit:at,children:({values:a,errors:b,touched:c,handleChange:e,handleSubmit:f})=>(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"flex flex-col w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm gap-4 mb-4",children:[(0,d.jsx)(h.r,{autoFocus:!0,variant:"bordered",label:"Username",type:"text",value:a.username,isInvalid:!!b.username&&!!c.username,errorMessage:b.username,onChange:e("username")}),(0,d.jsx)(h.r,{variant:"bordered",label:"Password",type:"password",value:a.password,isInvalid:!!b.password&&!!c.password,errorMessage:b.password,onChange:e("password"),onKeyDown:a=>{"Enter"===a.key&&f()}})]}),"0"===af&&(0,d.jsx)("div",{className:"w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm mb-4",children:(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,d.jsx)(i.Z,{className:"flex-1 min-w-0 flex items-center justify-center h-12 sm:h-14 bg-gray-200 dark:bg-gray-700 border-2 border-dashed border-gray-300 dark:border-gray-600",children:(0,d.jsx)(j.U,{className:"p-2 flex items-center justify-center",children:(0,d.jsx)("div",{className:"text-lg sm:text-2xl font-bold text-center text-slate-600 dark:text-white tracking-wider break-all",children:ah})})}),(0,d.jsx)("div",{className:"flex-1 min-w-0",children:(0,d.jsx)(h.r,{variant:"bordered",label:"Kode Captcha",type:"text",maxLength:4,value:a.captcha||"",onChange:a=>{let b=a.target.value.replace(/\D/g,""),c={...a,target:{...a.target,value:b}};e("captcha")(c)},onKeyDown:a=>{"Enter"===a.key&&f()},isInvalid:!!b.captcha&&!!c.captcha,errorMessage:b.captcha,className:"w-full"})})]})}),"1"===af&&(0,d.jsx)("div",{className:"flex w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm justify-center mb-4"}),(0,d.jsx)(k.T,{className:"w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm min-h-[36px] h-10 flex-shrink-0 login-button mt-2 font-semibold overflow-hidden",onPress:()=>f(),color:"primary",isLoading:_,isDisabled:_,children:(0,d.jsx)("span",{className:"truncate whitespace-nowrap",children:_?"Logging in...":"Login"})}),(0,d.jsx)(l.y,{className:"w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm my-6"}),(0,d.jsxs)("div",{className:"flex w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm flex-wrap md:flex-nowrap gap-4",children:[(0,d.jsx)(h.r,{variant:"bordered",label:"PIN 6 Digit",type:"password",maxLength:6,value:ad,onChange:a=>ae(a.target.value.replace(/\D/g,"")),onKeyDown:a=>{"Enter"===a.key&&au()},classNames:{label:"text-sm"}}),(0,d.jsx)(k.T,{className:"w-full flex h-14 font-semibold",onPress:au,variant:"ghost",color:"danger",isLoading:ab,isDisabled:ab,children:ab?"Logging in...":"Login PIN"})]})]})}),(0,d.jsxs)("div",{className:"text-slate-400 mt-6 text-sm tracking-wider font-sans",children:["Belum Punya Akun ?"," ",(0,d.jsx)(q(),{href:"/register",className:"font-bold",children:"Hubungi Admin"})]}),(0,d.jsx)(l.y,{className:"w-full max-w-sm sm:max-w-sm md:max-w-sm lg:max-w-sm xl:max-w-sm my-6"}),(0,d.jsxs)("div",{className:"font-semibold text-slate-400 text-xs tracking-wider flex items-center font-sans gap-1",children:[(0,d.jsx)(o,{size:13}),(0,d.jsx)("label",{children:"2025 Direktorat PA | PDPSIPA"})]})]})}},74075:a=>{"use strict";a.exports=require("zlib")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},85426:(a,b,c)=>{Promise.resolve().then(c.bind(c,10343))},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},99664:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(auth)",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,41912)),"D:\\sintesaNEXT2\\src\\app\\(auth)\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,42921)),"D:\\sintesaNEXT2\\src\\app\\(auth)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,53361)),"D:\\sintesaNEXT2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.bind(c,54413)),"D:\\sintesaNEXT2\\src\\app\\not-found.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["D:\\sintesaNEXT2\\src\\app\\(auth)\\login\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(auth)/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(auth)/login/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[431,3598,9161,8223,7426,9697],()=>b(b.s=99664));module.exports=c})();