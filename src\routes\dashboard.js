import <PERSON><PERSON> from "joi";

// Dashboard routes for analytics and summary data
export default async function dashboardRoutes(fastify, options) {
  // Backward compatibility route for old Express format (dashboard queries)
  fastify.post(
    "/",
    {
      schema: {
        body: {
          type: "object",
          required: ["query"],
          properties: {
            query: {
              type: "string",
              description: "SQL query for dashboard data",
            },
          },
        },
        response: {
          200: {
            type: "object",
            properties: {
              result: { type: "array" },
            },
          },
        },
      },
      preHandler: [fastify.verifyToken],
    },
    async (request, reply) => {
      try {
        const { query } = request.body;

        if (!query) {
          return reply.code(400).send({
            success: false,
            message: "Query parameter is required",
          });
        }

        fastify.log.info(
          "Executing legacy dashboard query:",
          query.substring(0, 100) + "..."
        );

        // Execute the SQL query directly (matching old Express behavior)
        // Note: The old dashboard controller didn't decrypt the query, it used it directly
        const [results] = await fastify.db.query(query);

        // Return in the same format as old Express backend
        return reply.send({
          result: results,
        });
      } catch (error) {
        fastify.log.error("Legacy dashboard query error:", error);

        // Match old Express error handling
        const errorMessage =
          error.sqlMessage ||
          error.message ||
          "Terjadi kesalahan dalam memproses permintaan.";

        return reply.code(500).send({
          error: errorMessage,
        });
      }
    }
  );

  // Get advanced dashboard data (new route)
  fastify.post(
    "/advanced",
    {
      schema: {
        body: {
          type: "object",
          properties: {
            tahun: { type: "integer", minimum: 2000, maximum: 2100 },
            periode: {
              type: "string",
              enum: ["bulanan", "triwulan", "semester", "tahunan"],
              default: "tahunan",
            },
            kode_skpd: { type: "string" },
            refresh: { type: "boolean", default: false },
          },
        },
      },
      preHandler: [fastify.authenticate],
    },
    async (request, reply) => {
      try {
        const { tahun, periode, kode_skpd, refresh } = request.body;
        const userId = request.user.userId;

        // Check cache first (unless refresh is requested)
        const cacheKey = `dashboard:${tahun || "all"}:${periode}:${kode_skpd || "all"}:${userId}`;

        if (!refresh && fastify.redis) {
          const cachedData = await fastify.redis.get(cacheKey);
          if (cachedData) {
            fastify.log.info(
              `Dashboard data served from cache for user ${request.user.username}`
            );
            return reply.send({
              success: true,
              message: "Dashboard data retrieved from cache",
              data: JSON.parse(cachedData),
              cached: true,
            });
          }
        }

        // Build WHERE conditions
        let whereConditions = [];
        let queryParams = [];

        if (tahun) {
          whereConditions.push("tahun = ?");
          queryParams.push(tahun);
        }

        if (kode_skpd) {
          whereConditions.push("kode_skpd = ?");
          queryParams.push(kode_skpd);
        }

        const whereClause =
          whereConditions.length > 0
            ? `WHERE ${whereConditions.join(" AND ")}`
            : "";

        // Get overview statistics
        const overviewQuery = `
        SELECT 
          COUNT(*) as total_kegiatan,
          COUNT(DISTINCT kode_skpd) as total_skpd,
          COUNT(DISTINCT kode_program) as total_program,
          SUM(pagu_anggaran) as total_pagu,
          SUM(realisasi_anggaran) as total_realisasi,
          SUM(sisa_anggaran) as total_sisa,
          AVG(persentase_realisasi) as rata_rata_realisasi
        FROM adk_data 
        ${whereClause}
      `;

        const [overviewResults] = await fastify.db.query(
          overviewQuery,
          queryParams
        );
        const overview = overviewResults[0];

        // Get top performing SKPD
        const topSkpdQuery = `
        SELECT 
          kode_skpd,
          nama_skpd,
          SUM(pagu_anggaran) as total_pagu,
          SUM(realisasi_anggaran) as total_realisasi,
          AVG(persentase_realisasi) as rata_rata_realisasi,
          COUNT(*) as jumlah_kegiatan
        FROM adk_data 
        ${whereClause}
        GROUP BY kode_skpd, nama_skpd
        ORDER BY rata_rata_realisasi DESC, total_realisasi DESC
        LIMIT 10
      `;

        const [topSkpdResults] = await fastify.db.query(
          topSkpdQuery,
          queryParams
        );

        // Get budget realization trend
        let trendQuery;
        let trendParams = [...queryParams];

        switch (periode) {
          case "bulanan":
            trendQuery = `
            SELECT 
              MONTH(tanggal_realisasi) as periode,
              MONTHNAME(tanggal_realisasi) as nama_periode,
              SUM(realisasi_anggaran) as total_realisasi,
              COUNT(*) as jumlah_transaksi
            FROM adk_data 
            ${whereClause} AND tanggal_realisasi IS NOT NULL
            GROUP BY MONTH(tanggal_realisasi), MONTHNAME(tanggal_realisasi)
            ORDER BY periode
          `;
            break;
          case "triwulan":
            trendQuery = `
            SELECT 
              QUARTER(tanggal_realisasi) as periode,
              CONCAT('Triwulan ', QUARTER(tanggal_realisasi)) as nama_periode,
              SUM(realisasi_anggaran) as total_realisasi,
              COUNT(*) as jumlah_transaksi
            FROM adk_data 
            ${whereClause} AND tanggal_realisasi IS NOT NULL
            GROUP BY QUARTER(tanggal_realisasi)
            ORDER BY periode
          `;
            break;
          case "semester":
            trendQuery = `
            SELECT 
              CASE WHEN MONTH(tanggal_realisasi) <= 6 THEN 1 ELSE 2 END as periode,
              CASE WHEN MONTH(tanggal_realisasi) <= 6 THEN 'Semester 1' ELSE 'Semester 2' END as nama_periode,
              SUM(realisasi_anggaran) as total_realisasi,
              COUNT(*) as jumlah_transaksi
            FROM adk_data 
            ${whereClause} AND tanggal_realisasi IS NOT NULL
            GROUP BY CASE WHEN MONTH(tanggal_realisasi) <= 6 THEN 1 ELSE 2 END
            ORDER BY periode
          `;
            break;
          default: // tahunan
            trendQuery = `
            SELECT 
              tahun as periode,
              CONCAT('Tahun ', tahun) as nama_periode,
              SUM(realisasi_anggaran) as total_realisasi,
              COUNT(*) as jumlah_transaksi
            FROM adk_data 
            ${whereClause}
            GROUP BY tahun
            ORDER BY tahun
          `;
            break;
        }

        const [trendResults] = await fastify.db.query(trendQuery, trendParams);

        // Get budget distribution by program
        const programDistributionQuery = `
        SELECT 
          kode_program,
          nama_program,
          SUM(pagu_anggaran) as total_pagu,
          SUM(realisasi_anggaran) as total_realisasi,
          COUNT(*) as jumlah_kegiatan,
          AVG(persentase_realisasi) as rata_rata_realisasi
        FROM adk_data 
        ${whereClause}
        GROUP BY kode_program, nama_program
        ORDER BY total_pagu DESC
        LIMIT 15
      `;

        const [programResults] = await fastify.db.query(
          programDistributionQuery,
          queryParams
        );

        // Get recent activities
        const recentActivitiesQuery = `
        SELECT 
          kode_skpd,
          nama_skpd,
          kode_kegiatan,
          nama_kegiatan,
          pagu_anggaran,
          realisasi_anggaran,
          persentase_realisasi,
          tanggal_realisasi,
          created_at
        FROM adk_data 
        ${whereClause}
        ORDER BY created_at DESC, tanggal_realisasi DESC
        LIMIT 20
      `;

        const [recentResults] = await fastify.db.query(
          recentActivitiesQuery,
          queryParams
        );

        // Get performance indicators
        const performanceQuery = `
        SELECT 
          COUNT(CASE WHEN persentase_realisasi >= 90 THEN 1 END) as sangat_baik,
          COUNT(CASE WHEN persentase_realisasi >= 70 AND persentase_realisasi < 90 THEN 1 END) as baik,
          COUNT(CASE WHEN persentase_realisasi >= 50 AND persentase_realisasi < 70 THEN 1 END) as cukup,
          COUNT(CASE WHEN persentase_realisasi < 50 THEN 1 END) as kurang,
          COUNT(*) as total
        FROM adk_data 
        ${whereClause}
      `;

        const [performanceResults] = await fastify.db.query(
          performanceQuery,
          queryParams
        );
        const performance = performanceResults[0];

        // Get query statistics for this user
        const queryStatsQuery = `
        SELECT 
          COUNT(*) as total_queries,
          COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_queries,
          AVG(execution_time) as avg_execution_time
        FROM query_logs 
        WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      `;

        const [queryStatsResults] = await fastify.db.query(queryStatsQuery, [
          userId,
        ]);
        const queryStats = queryStatsResults[0];

        // Prepare dashboard data
        const dashboardData = {
          overview: {
            totalKegiatan: overview.total_kegiatan || 0,
            totalSkpd: overview.total_skpd || 0,
            totalProgram: overview.total_program || 0,
            totalPagu: overview.total_pagu || 0,
            totalRealisasi: overview.total_realisasi || 0,
            totalSisa: overview.total_sisa || 0,
            rataRataRealisasi: overview.rata_rata_realisasi || 0,
            persentaseRealisasi:
              overview.total_pagu > 0
                ? (overview.total_realisasi / overview.total_pagu) * 100
                : 0,
          },
          topSkpd: topSkpdResults,
          trendRealisasi: trendResults,
          distribusiProgram: programResults,
          aktivitasTerbaru: recentResults,
          indikatorKinerja: {
            sangatBaik: performance.sangat_baik || 0,
            baik: performance.baik || 0,
            cukup: performance.cukup || 0,
            kurang: performance.kurang || 0,
            total: performance.total || 0,
          },
          statistikQuery: {
            totalQueries: queryStats.total_queries || 0,
            successfulQueries: queryStats.successful_queries || 0,
            avgExecutionTime: queryStats.avg_execution_time || 0,
          },
          metadata: {
            tahun,
            periode,
            kode_skpd,
            generatedAt: new Date().toISOString(),
            userId,
          },
        };

        // Cache the result for 15 minutes
        if (fastify.redis) {
          await fastify.redis.setex(
            cacheKey,
            900,
            JSON.stringify(dashboardData)
          );
        }

        fastify.log.info(
          `Dashboard data generated for user ${request.user.username}`
        );

        return reply.send({
          success: true,
          message: "Dashboard data retrieved successfully",
          data: dashboardData,
          cached: false,
        });
      } catch (error) {
        fastify.log.error("Dashboard data error:", error);
        return reply.code(500).send({
          success: false,
          message: "Failed to retrieve dashboard data",
        });
      }
    }
  );

  // Get widget data
  fastify.get(
    "/widget/:widgetType",
    {
      schema: {
        params: {
          type: "object",
          required: ["widgetType"],
          properties: {
            widgetType: {
              type: "string",
              enum: [
                "overview",
                "realization",
                "performance",
                "recent",
                "top-skpd",
                "budget-distribution",
              ],
            },
          },
        },
        querystring: {
          type: "object",
          properties: {
            tahun: { type: "integer" },
            kode_skpd: { type: "string" },
            limit: { type: "integer", minimum: 1, maximum: 100, default: 10 },
          },
        },
      },
      preHandler: [fastify.authenticate],
    },
    async (request, reply) => {
      try {
        const { widgetType } = request.params;
        const { tahun, kode_skpd, limit } = request.query;

        let whereConditions = [];
        let queryParams = [];

        if (tahun) {
          whereConditions.push("tahun = ?");
          queryParams.push(tahun);
        }

        if (kode_skpd) {
          whereConditions.push("kode_skpd = ?");
          queryParams.push(kode_skpd);
        }

        const whereClause =
          whereConditions.length > 0
            ? `WHERE ${whereConditions.join(" AND ")}`
            : "";

        let widgetData = {};

        switch (widgetType) {
          case "overview":
            const overviewQuery = `
            SELECT 
              COUNT(*) as total_kegiatan,
              SUM(pagu_anggaran) as total_pagu,
              SUM(realisasi_anggaran) as total_realisasi,
              AVG(persentase_realisasi) as rata_rata_realisasi
            FROM adk_data ${whereClause}
          `;
            const [overviewResults] = await fastify.db.query(
              overviewQuery,
              queryParams
            );
            widgetData = overviewResults[0];
            break;

          case "realization":
            const realizationQuery = `
            SELECT 
              MONTH(tanggal_realisasi) as bulan,
              SUM(realisasi_anggaran) as total_realisasi
            FROM adk_data 
            ${whereClause} AND tanggal_realisasi IS NOT NULL
            GROUP BY MONTH(tanggal_realisasi)
            ORDER BY bulan
          `;
            const [realizationResults] = await fastify.db.query(
              realizationQuery,
              queryParams
            );
            widgetData = realizationResults;
            break;

          case "performance":
            const performanceQuery = `
            SELECT 
              CASE 
                WHEN persentase_realisasi >= 90 THEN 'Sangat Baik'
                WHEN persentase_realisasi >= 70 THEN 'Baik'
                WHEN persentase_realisasi >= 50 THEN 'Cukup'
                ELSE 'Kurang'
              END as kategori,
              COUNT(*) as jumlah
            FROM adk_data ${whereClause}
            GROUP BY kategori
          `;
            const [performanceResults] = await fastify.db.query(
              performanceQuery,
              queryParams
            );
            widgetData = performanceResults;
            break;

          case "recent":
            const recentQuery = `
            SELECT 
              nama_skpd,
              nama_kegiatan,
              realisasi_anggaran,
              persentase_realisasi,
              tanggal_realisasi
            FROM adk_data ${whereClause}
            ORDER BY tanggal_realisasi DESC, created_at DESC
            LIMIT ?
          `;
            const [recentResults] = await fastify.db.query(recentQuery, [
              ...queryParams,
              limit,
            ]);
            widgetData = recentResults;
            break;

          case "top-skpd":
            const topSkpdQuery = `
            SELECT 
              nama_skpd,
              SUM(realisasi_anggaran) as total_realisasi,
              AVG(persentase_realisasi) as rata_rata_realisasi
            FROM adk_data ${whereClause}
            GROUP BY kode_skpd, nama_skpd
            ORDER BY rata_rata_realisasi DESC
            LIMIT ?
          `;
            const [topSkpdResults] = await fastify.db.query(topSkpdQuery, [
              ...queryParams,
              limit,
            ]);
            widgetData = topSkpdResults;
            break;

          case "budget-distribution":
            const budgetQuery = `
            SELECT 
              nama_program,
              SUM(pagu_anggaran) as total_pagu,
              SUM(realisasi_anggaran) as total_realisasi
            FROM adk_data ${whereClause}
            GROUP BY kode_program, nama_program
            ORDER BY total_pagu DESC
            LIMIT ?
          `;
            const [budgetResults] = await fastify.db.query(budgetQuery, [
              ...queryParams,
              limit,
            ]);
            widgetData = budgetResults;
            break;

          default:
            return reply.code(400).send({
              success: false,
              message: "Invalid widget type",
            });
        }

        return reply.send({
          success: true,
          message: `${widgetType} widget data retrieved successfully`,
          data: widgetData,
        });
      } catch (error) {
        fastify.log.error("Widget data error:", error);
        return reply.code(500).send({
          success: false,
          message: "Failed to retrieve widget data",
        });
      }
    }
  );

  // Get dashboard analytics
  fastify.get(
    "/analytics",
    {
      schema: {
        querystring: {
          type: "object",
          properties: {
            tahun: { type: "integer" },
            periode: {
              type: "string",
              enum: ["7d", "30d", "90d", "1y"],
              default: "30d",
            },
          },
        },
      },
      preHandler: [fastify.authenticate],
    },
    async (request, reply) => {
      try {
        const { tahun, periode } = request.query;
        const userId = request.user.userId;

        // Calculate date range based on periode
        let dateCondition = "";
        switch (periode) {
          case "7d":
            dateCondition = "AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            break;
          case "30d":
            dateCondition =
              "AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            break;
          case "90d":
            dateCondition =
              "AND created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)";
            break;
          case "1y":
            dateCondition =
              "AND created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)";
            break;
        }

        // User activity analytics
        const userActivityQuery = `
        SELECT 
          DATE(created_at) as tanggal,
          COUNT(*) as jumlah_query,
          AVG(execution_time) as rata_rata_waktu
        FROM query_logs 
        WHERE user_id = ? ${dateCondition}
        GROUP BY DATE(created_at)
        ORDER BY tanggal DESC
      `;

        const [userActivityResults] = await fastify.db.query(
          userActivityQuery,
          [userId]
        );

        // System performance analytics
        const systemPerformanceQuery = `
        SELECT 
          COUNT(*) as total_queries,
          COUNT(DISTINCT user_id) as active_users,
          AVG(execution_time) as avg_execution_time,
          MAX(execution_time) as max_execution_time,
          COUNT(CASE WHEN status = 'error' THEN 1 END) as error_count
        FROM query_logs 
        WHERE 1=1 ${dateCondition}
      `;

        const [systemPerformanceResults] = await fastify.db.query(
          systemPerformanceQuery
        );

        // Popular queries analytics
        const popularQueriesQuery = `
        SELECT 
          LEFT(sql_query, 100) as query_preview,
          COUNT(*) as frequency,
          AVG(execution_time) as avg_time
        FROM query_logs 
        WHERE user_id = ? AND status = 'success' ${dateCondition}
        GROUP BY LEFT(sql_query, 100)
        ORDER BY frequency DESC
        LIMIT 10
      `;

        const [popularQueriesResults] = await fastify.db.query(
          popularQueriesQuery,
          [userId]
        );

        return reply.send({
          success: true,
          message: "Analytics data retrieved successfully",
          data: {
            userActivity: userActivityResults,
            systemPerformance: systemPerformanceResults[0],
            popularQueries: popularQueriesResults,
            periode,
            generatedAt: new Date().toISOString(),
          },
        });
      } catch (error) {
        fastify.log.error("Analytics data error:", error);
        return reply.code(500).send({
          success: false,
          message: "Failed to retrieve analytics data",
        });
      }
    }
  );

  // Clear dashboard cache
  fastify.delete(
    "/cache",
    {
      preHandler: [fastify.authenticate],
    },
    async (request, reply) => {
      try {
        if (fastify.redis) {
          const pattern = `dashboard:*:${request.user.userId}`;
          const keys = await fastify.redis.keys(pattern);

          if (keys.length > 0) {
            await fastify.redis.del(...keys);
          }

          fastify.log.info(
            `Dashboard cache cleared for user ${request.user.username}`
          );

          return reply.send({
            success: true,
            message: "Dashboard cache cleared successfully",
            data: { clearedKeys: keys.length },
          });
        } else {
          return reply.send({
            success: true,
            message: "No cache to clear (Redis not available)",
            data: { clearedKeys: 0 },
          });
        }
      } catch (error) {
        fastify.log.error("Clear cache error:", error);
        return reply.code(500).send({
          success: false,
          message: "Failed to clear dashboard cache",
        });
      }
    }
  );
}
