import Redis from 'ioredis';
import fp from 'fastify-plugin';

async function redisPlugin(fastify, options) {
  // Redis configuration
  const redisConfig = {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    db: process.env.REDIS_DB || 0,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
    keepAlive: 30000,
    connectTimeout: 10000,
    commandTimeout: 5000,
    family: 4
  };

  // Create Redis instance
  let redis = null;
  let redisConnected = false;

  try {
    redis = new Redis(redisConfig);
    
    // Add error handlers to prevent unhandled errors
    redis.on('error', (error) => {
      fastify.log.warn('Redis connection error:', error.message);
      redisConnected = false;
    });
    
    redis.on('connect', () => {
      fastify.log.info('✅ Redis connected successfully');
      redisConnected = true;
    });
    
    redis.on('close', () => {
      fastify.log.warn('Redis connection closed');
      redisConnected = false;
    });

    // Test connection
    await redis.ping();
    redisConnected = true;
  } catch (error) {
    fastify.log.warn('⚠️ Redis connection failed, continuing without cache:', error.message);
    redis = null;
    redisConnected = false;
  }

  // Redis utilities
  const cache = {
    // Set cache with expiration
    async set(key, value, ttl = 3600) {
      if (!redis || !redisConnected) return false;
      try {
        const serialized = JSON.stringify(value);
        if (ttl > 0) {
          await redis.setex(key, ttl, serialized);
        } else {
          await redis.set(key, serialized);
        }
        return true;
      } catch (error) {
        fastify.log.error('Redis set error:', error);
        return false;
      }
    },

    // Get cache
    async get(key) {
      if (!redis || !redisConnected) return null;
      try {
        const value = await redis.get(key);
        return value ? JSON.parse(value) : null;
      } catch (error) {
        fastify.log.error('Redis get error:', error);
        return null;
      }
    },

    // Delete cache
    async del(key) {
      if (!redis || !redisConnected) return false;
      try {
        await redis.del(key);
        return true;
      } catch (error) {
        fastify.log.error('Redis del error:', error);
        return false;
      }
    },

    // Check if key exists
    async exists(key) {
      if (!redis || !redisConnected) return false;
      try {
        const result = await redis.exists(key);
        return result === 1;
      } catch (error) {
        fastify.log.error('Redis exists error:', error);
        return false;
      }
    },

    // Set expiration
    async expire(key, ttl) {
      if (!redis || !redisConnected) return false;
      try {
        await redis.expire(key, ttl);
        return true;
      } catch (error) {
        fastify.log.error('Redis expire error:', error);
        return false;
      }
    },

    // Get TTL
    async ttl(key) {
      if (!redis || !redisConnected) return -1;
      try {
        return await redis.ttl(key);
      } catch (error) {
        fastify.log.error('Redis ttl error:', error);
        return -1;
      }
    },

    // Increment counter
    async incr(key, ttl = null) {
      if (!redis || !redisConnected) return 0;
      try {
        const result = await redis.incr(key);
        if (ttl && result === 1) {
          await redis.expire(key, ttl);
        }
        return result;
      } catch (error) {
        fastify.log.error('Redis incr error:', error);
        return 0;
      }
    },

    // Hash operations
    async hset(key, field, value) {
      if (!redis || !redisConnected) return false;
      try {
        await redis.hset(key, field, JSON.stringify(value));
        return true;
      } catch (error) {
        fastify.log.error('Redis hset error:', error);
        return false;
      }
    },

    async hget(key, field) {
      if (!redis || !redisConnected) return null;
      try {
        const value = await redis.hget(key, field);
        return value ? JSON.parse(value) : null;
      } catch (error) {
        fastify.log.error('Redis hget error:', error);
        return null;
      }
    },

    async hgetall(key) {
      if (!redis || !redisConnected) return {};
      try {
        const hash = await redis.hgetall(key);
        const result = {};
        for (const [field, value] of Object.entries(hash)) {
          try {
            result[field] = JSON.parse(value);
          } catch {
            result[field] = value;
          }
        }
        return result;
      } catch (error) {
        fastify.log.error('Redis hgetall error:', error);
        return {};
      }
    },

    async hdel(key, field) {
      if (!redis || !redisConnected) return false;
      try {
        await redis.hdel(key, field);
        return true;
      } catch (error) {
        fastify.log.error('Redis hdel error:', error);
        return false;
      }
    },

    // List operations
    async lpush(key, value) {
      if (!redis || !redisConnected) return false;
      try {
        await redis.lpush(key, JSON.stringify(value));
        return true;
      } catch (error) {
        fastify.log.error('Redis lpush error:', error);
        return false;
      }
    },

    async rpop(key) {
      if (!redis || !redisConnected) return null;
      try {
        const value = await redis.rpop(key);
        return value ? JSON.parse(value) : null;
      } catch (error) {
        fastify.log.error('Redis rpop error:', error);
        return null;
      }
    },

    async llen(key) {
      if (!redis || !redisConnected) return 0;
      try {
        return await redis.llen(key);
      } catch (error) {
        fastify.log.error('Redis llen error:', error);
        return 0;
      }
    },

    // Set operations
    async sadd(key, member) {
      if (!redis || !redisConnected) return false;
      try {
        await redis.sadd(key, JSON.stringify(member));
        return true;
      } catch (error) {
        fastify.log.error('Redis sadd error:', error);
        return false;
      }
    },

    async srem(key, member) {
      if (!redis || !redisConnected) return false;
      try {
        await redis.srem(key, JSON.stringify(member));
        return true;
      } catch (error) {
        fastify.log.error('Redis srem error:', error);
        return false;
      }
    },

    async smembers(key) {
      if (!redis || !redisConnected) return [];
      try {
        const members = await redis.smembers(key);
        return members.map(member => {
          try {
            return JSON.parse(member);
          } catch {
            return member;
          }
        });
      } catch (error) {
        fastify.log.error('Redis smembers error:', error);
        return [];
      }
    },

    // Pattern matching
    async keys(pattern) {
      if (!redis || !redisConnected) return [];
      try {
        return await redis.keys(pattern);
      } catch (error) {
        fastify.log.error('Redis keys error:', error);
        return [];
      }
    },

    // Flush database
    async flushdb() {
      if (!redis || !redisConnected) return false;
      try {
        await redis.flushdb();
        return true;
      } catch (error) {
        fastify.log.error('Redis flushdb error:', error);
        return false;
      }
    },

    // Get Redis instance for advanced operations
    getInstance() {
      return (redis && redisConnected) ? redis : null;
    }
  };

  // Session management utilities
  const session = {
    // Create session
    async create(sessionId, data, ttl = 28800) { // 8 hours default
      return await cache.set(`session:${sessionId}`, data, ttl);
    },

    // Get session
    async get(sessionId) {
      return await cache.get(`session:${sessionId}`);
    },

    // Update session
    async update(sessionId, data, ttl = 28800) {
      return await cache.set(`session:${sessionId}`, data, ttl);
    },

    // Delete session
    async destroy(sessionId) {
      return await cache.del(`session:${sessionId}`);
    },

    // Extend session
    async extend(sessionId, ttl = 28800) {
      return await cache.expire(`session:${sessionId}`, ttl);
    }
  };

  // Register cache and session utilities
  fastify.decorate('cache', cache);
  fastify.decorate('session', session);
  fastify.decorate('redis', redis);

  // Graceful shutdown
  fastify.addHook('onClose', async () => {
    if (redis && redisConnected) {
      try {
        await redis.quit();
        fastify.log.info('Redis connection closed');
      } catch (error) {
        fastify.log.error('Error closing Redis connection:', error);
      }
    }
  });
}

export default fp(redisPlugin, {
  name: 'redis',
  dependencies: []
});