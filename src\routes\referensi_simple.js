// Reference data routes - Simplified for backward compatibility
export default async function referensiRoutes(fastify, options) {
  // Backward compatibility route for old Express format
  fastify.post(
    "/",
    {
      schema: {
        body: {
          type: "object",
          required: ["query"],
          properties: {
            query: { type: "string", description: "Encrypted SQL query" },
          },
        },
        response: {
          200: {
            type: "object",
            properties: {
              result: { type: "array" },
            },
          },
        },
      },
      preHandler: [fastify.verifyToken],
    },
    async (request, reply) => {
      try {
        const { query } = request.body;

        if (!query) {
          return reply.code(400).send({
            success: false,
            message: "Query parameter is required",
          });
        }

        // Decrypt the query using the same method as old Express backend
        let decryptedQuery;
        try {
          decryptedQuery = fastify.encryption.decryptString(query);
          // Remove quotes if present (matching old behavior)
          decryptedQuery = decryptedQuery.replace(/"/g, "");
        } catch (decryptError) {
          fastify.log.error("Query decryption error:", decryptError);
          return reply.code(400).send({
            success: false,
            message: "Failed to decrypt query",
          });
        }

        fastify.log.info(
          "Executing legacy query:",
          decryptedQuery.substring(0, 100) + "..."
        );

        // Execute the SQL query directly (matching old Express behavior)
        const queryResult = await fastify.db.rawQuery(decryptedQuery);
        const results = queryResult.rows;

        // Return in the same format as old Express backend
        return reply.send({
          result: results,
        });
      } catch (error) {
        fastify.log.error("Legacy referensi query error: " + error.message);
        fastify.log.error("Error stack: " + error.stack);
        fastify.log.error("SQL Message: " + (error.sqlMessage || "none"));

        // Match old Express error handling
        const errorMessage =
          error.sqlMessage ||
          error.message ||
          "Terjadi kesalahan dalam memproses permintaan.";

        return reply.code(500).send({
          error: errorMessage,
        });
      }
    }
  );
}
