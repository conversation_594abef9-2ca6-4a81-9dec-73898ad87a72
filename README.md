# Sintesa Backend

A modern, high-performance backend API built with Fastify for the Sintesa application ecosystem. This backend provides secure authentication, data management, and real-time capabilities for government data inquiry systems.

## 🚀 Features

### Core Features
- **Fast & Lightweight**: Built on Fastify for maximum performance
- **Secure Authentication**: JWT-based auth with refresh tokens
- **Real-time Communication**: Socket.IO integration
- **File Management**: Secure file upload and processing
- **Data Encryption**: AES-256-GCM encryption for sensitive data
- **Rate Limiting**: Configurable request throttling
- **Comprehensive Logging**: Winston-based logging system
- **API Documentation**: Auto-generated Swagger/OpenAPI docs

### Database & Caching
- **MySQL Integration**: Robust database layer with connection pooling
- **Redis Caching**: High-performance caching and session storage
- **Query Optimization**: Prepared statements and query logging
- **Database Migrations**: Schema versioning and management

### Security Features
- **CORS Protection**: Configurable cross-origin resource sharing
- **Helmet Integration**: Security headers and protection
- **Input Validation**: Joi-based request validation
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Content sanitization
- **Rate Limiting**: DDoS protection

### Monitoring & Operations
- **Health Checks**: Comprehensive system health monitoring
- **Graceful Shutdown**: Clean process termination
- **Docker Support**: Containerized deployment
- **PM2 Integration**: Process management and clustering
- **Environment Configuration**: Flexible config management

## 📋 Prerequisites

- **Node.js**: Version 18.0.0 or higher
- **npm**: Version 8.0.0 or higher
- **MySQL**: Version 8.0 or higher
- **Redis**: Version 6.0 or higher (optional but recommended)

## 🛠️ Quick Start

### Option 1: Automated Setup (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd sintesa-backend

# Run the interactive setup script
node scripts/setup.js

# Start the development server
npm run dev
```

### Option 2: Manual Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Database Setup**
   ```bash
   # Create database
   mysql -u root -p -e "CREATE DATABASE sintesa;"
   
   # Import schema
   mysql -u root -p sintesa < database/schema.sql
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```

## 🐳 Docker Deployment

### Development with Docker Compose

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop services
docker-compose down
```

### Production Deployment

```bash
# Build and start production services
docker-compose -f docker-compose.yml --profile production up -d

# Scale the application
docker-compose up -d --scale app=3
```

## 📁 Project Structure

```
sintesa-backend/
├── src/
│   ├── controllers/         # Request handlers
│   ├── middleware/          # Custom middleware
│   ├── models/             # Data models
│   ├── plugins/            # Fastify plugins
│   ├── routes/             # API route definitions
│   ├── services/           # Business logic
│   ├── utils/              # Utility functions
│   └── server.js           # Application entry point
├── database/
│   ├── schema.sql          # Database schema
│   ├── seed.sql            # Initial data
│   └── migrations/         # Database migrations
├── config/
│   ├── database.js         # Database configuration
│   ├── redis.js            # Redis configuration
│   └── logger.js           # Logging configuration
├── scripts/
│   ├── setup.js            # Interactive setup script
│   └── health-check.js     # Health check utility
├── test/                   # Test files
├── logs/                   # Application logs
├── uploads/                # File uploads
├── temp/                   # Temporary files
├── docker-compose.yml      # Docker services
├── Dockerfile              # Container definition
├── ecosystem.config.js     # PM2 configuration
└── package.json            # Dependencies and scripts
```

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```env
# Server
NODE_ENV=development
PORT=3001
HOST=0.0.0.0

# Database
DB_HOST=localhost
DB_PORT=3306
DB_USER=sintesa_user
DB_PASSWORD=sintesa_pass_2024
DB_NAME=sintesa

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Security
JWT_SECRET=your-jwt-secret
ENCRYPTION_KEY=your-encryption-key

# CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

### Database Configuration

The application uses MySQL with the following key tables:
- `users` - User accounts and profiles
- `user_sessions` - Active user sessions
- `query_logs` - API query logging
- `adk_data` - Regional budget data
- `referensi_data` - Reference data
- `file_uploads` - File metadata

## 📚 API Documentation

### Available Endpoints

#### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/refresh` - Refresh JWT token
- `GET /api/auth/status` - Check auth status

#### User Management
- `GET /api/users` - List users (admin)
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `POST /api/users/change-password` - Change password

#### Data Inquiry
- `GET /api/inquiry/search` - Search data
- `POST /api/inquiry/advanced` - Advanced search
- `GET /api/inquiry/history` - Query history

#### Reference Data
- `GET /api/referensi/categories` - Get categories
- `GET /api/referensi/data` - Get reference data
- `POST /api/referensi/data` - Create reference data

#### Regional Budget (ADK)
- `GET /api/adk/summary` - Budget summary
- `GET /api/adk/by-skpd` - Budget by department
- `GET /api/adk/by-program` - Budget by program

#### System
- `GET /api/health` - Health check
- `GET /api/ping` - Simple ping
- `GET /api/ready` - Readiness check
- `GET /api/status` - System status

### Interactive API Documentation

When running in development mode, visit:
- Swagger UI: `http://localhost:3001/documentation`
- OpenAPI JSON: `http://localhost:3001/documentation/json`

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- --testPathPattern=server.test.js

# Run tests in watch mode
npm run test:watch
```

## 📊 Monitoring & Logging

### Health Monitoring

```bash
# Check application health
curl http://localhost:3001/api/health

# Use the health check script
node scripts/health-check.js
```

### Log Files

- Application logs: `logs/app.log`
- Error logs: `logs/error.log`
- Access logs: `logs/access.log`

### Performance Monitoring

The application includes built-in performance monitoring:
- Request/response timing
- Database query performance
- Memory usage tracking
- Error rate monitoring

## 🚀 Production Deployment

### Using PM2

```bash
# Install PM2 globally
npm install -g pm2

# Start application with PM2
pm2 start ecosystem.config.js --env production

# Monitor processes
pm2 monit

# View logs
pm2 logs

# Restart application
pm2 restart sintesa-backend
```

### Using Docker

```bash
# Build production image
docker build -t sintesa-backend:latest .

# Run container
docker run -d \
  --name sintesa-backend \
  -p 3001:3001 \
  --env-file .env \
  sintesa-backend:latest
```

### Environment-Specific Configurations

- **Development**: Full logging, hot reload, debug mode
- **Staging**: Production-like with enhanced logging
- **Production**: Optimized performance, minimal logging

## API Endpoints

### Authentication
- `POST /next/auth/login` - User login
- `POST /next/auth/refresh` - Refresh access token
- `POST /next/auth/logout` - User logout
- `GET /next/auth/status` - Check login status
- `PUT /next/auth/password` - Change password

### User Management
- `GET /next/users/profile` - Get current user profile
- `PUT /next/users/profile` - Update user profile
- `GET /next/users` - List all users (admin)
- `POST /next/users` - Create new user (admin)
- `PUT /next/users/:id` - Update user (admin)
- `DELETE /next/users/:id` - Delete user (admin)

### Inquiry System
- `POST /next/inquiry/query` - Execute encrypted SQL query
- `GET /next/inquiry/history` - Get query history
- `GET /next/inquiry/stats` - Get query statistics

### Reference Data
- `GET /next/referensi/categories` - Get all categories
- `GET /next/referensi/:category` - Get reference data by category
- `POST /next/referensi/:category` - Create reference item (admin)
- `PUT /next/referensi/:category/:id` - Update reference item (admin)
- `DELETE /next/referensi/:category/:id` - Delete reference item (admin)

### ADK Management
- `POST /next/adk/query` - Query ADK data with filters
- `GET /next/adk/summary/:year` - Get ADK summary by year
- `GET /next/adk/years` - Get available years
- `GET /next/adk/skpd` - Get SKPD list
- `GET /next/adk/program` - Get program list

### Dashboard
- `POST /next/dashboard` - Get dashboard data
- `GET /next/dashboard/widget/:type` - Get widget data
- `GET /next/dashboard/analytics` - Get analytics data
- `DELETE /next/dashboard/cache` - Clear dashboard cache

## Real-time Features

### Socket.IO Events

**Client to Server**:
- `authenticate` - Authenticate socket connection
- `join_room` - Join a specific room
- `leave_room` - Leave a room

**Server to Client**:
- `query_started` - Query execution started
- `query_progress` - Query execution progress
- `query_completed` - Query execution completed
- `query_error` - Query execution error
- `server_status` - Server status updates
- `user_notification` - User-specific notifications

## Security Considerations

### Authentication
- JWT tokens with short expiry times
- Refresh token rotation
- Session invalidation on logout
- Rate limiting on authentication endpoints

### Data Protection
- AES encryption for sensitive data
- Password hashing with bcrypt
- SQL injection prevention
- Input validation and sanitization

### Network Security
- CORS configuration
- Security headers with Helmet.js
- Request size limits
- Connection timeouts

## Performance Optimization

### Caching Strategy
- Redis for session storage
- Query result caching
- Dashboard data caching
- Static file caching

### Database Optimization
- Connection pooling
- Query timeout limits
- Pagination for large datasets
- Indexed queries

### Memory Management
- Request body size limits
- File upload size limits
- Connection limits
- Garbage collection optimization

## Monitoring and Logging

### Logging
- Structured JSON logging
- Log rotation and archival
- Different log levels (error, warn, info, debug)
- Request/response logging

### Monitoring
- Health check endpoints
- Performance metrics
- Error tracking
- Resource usage monitoring

### Metrics Available
- Request count and response times
- Database query performance
- Memory and CPU usage
- Active user sessions
- Error rates and types

## Deployment

### Production Setup

1. **Environment Configuration**:
   ```bash
   NODE_ENV=production
   TRUST_PROXY=true
   ```

2. **Security Hardening**:
   - Use strong JWT secrets
   - Enable HTTPS
   - Configure proper CORS origins
   - Set up firewall rules

3. **Process Management**:
   ```bash
   # Using PM2
   npm install -g pm2
   pm2 start src/server.js --name sintesa-backend
   pm2 startup
   pm2 save
   ```

4. **Reverse Proxy** (Nginx example):
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://localhost:88;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

### Docker Deployment

Create `Dockerfile`:
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 88
CMD ["npm", "start"]
```

Create `docker-compose.yml`:
```yaml
version: '3.8'
services:
  sintesa-backend:
    build: .
    ports:
      - "88:88"
    environment:
      - NODE_ENV=production
    depends_on:
      - mysql
      - redis
  
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: sintesa_db
      MYSQL_USER: sintesa_user
      MYSQL_PASSWORD: sintesa_password
    volumes:
      - mysql_data:/var/lib/mysql
  
  redis:
    image: redis:6-alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

## 🔒 Security Considerations

### Best Practices Implemented

1. **Authentication & Authorization**
   - JWT tokens with expiration
   - Refresh token rotation
   - Role-based access control
   - Session management

2. **Data Protection**
   - Input validation and sanitization
   - SQL injection prevention
   - XSS protection
   - CSRF protection

3. **Network Security**
   - HTTPS enforcement (production)
   - CORS configuration
   - Rate limiting
   - Security headers

4. **Operational Security**
   - Environment variable protection
   - Secure file uploads
   - Audit logging
   - Error handling without information leakage

## 🛠️ Development

### Available Scripts

```bash
npm run dev          # Start development server with hot reload
npm start            # Start production server
npm test             # Run test suite
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
npm run build        # Build for production
npm run db:migrate   # Run database migrations
npm run db:seed      # Seed database with sample data
```

### Code Style

The project uses:
- **ESLint**: Code linting and style enforcement
- **Prettier**: Code formatting
- **Husky**: Git hooks for quality assurance

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📞 Support

### Troubleshooting

#### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check MySQL service
   systemctl status mysql
   
   # Verify credentials
   mysql -u sintesa_user -p sintesa
   ```

2. **Redis Connection Failed**
   ```bash
   # Check Redis service
   systemctl status redis
   
   # Test connection
   redis-cli ping
   ```

3. **Port Already in Use**
   ```bash
   # Find process using port
   lsof -i :3001
   
   # Kill process
   kill -9 <PID>
   ```

#### Performance Issues

- Check database query performance in logs
- Monitor memory usage with `npm run monitor`
- Review Redis cache hit rates
- Analyze request patterns in access logs

### Getting Help

- Check the [documentation](./docs/)
- Review [API examples](./examples/)
- Submit [issues](./issues) for bugs
- Join our [community discussions](./discussions)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Fastify](https://www.fastify.io/) - Fast and low overhead web framework
- [MySQL](https://www.mysql.com/) - Reliable database system
- [Redis](https://redis.io/) - High-performance caching
- [Socket.IO](https://socket.io/) - Real-time communication
- [Winston](https://github.com/winstonjs/winston) - Logging library

---

**Sintesa Backend** - Built with ❤️ for modern government data systems